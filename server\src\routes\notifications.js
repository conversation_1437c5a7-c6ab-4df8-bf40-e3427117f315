const express = require('express');
const { query, body, validationResult } = require('express-validator');
const Notification = require('../models/Notification');
const notificationService = require('../services/notificationService');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get user notifications
router.get('/', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('type').optional().isIn(['new_interest', 'interest_accepted', 'new_message', 'profile_viewed', 'system']).withMessage('Invalid notification type'),
  query('read').optional().isBoolean().withMessage('Read status must be boolean'),
  query('priority').optional().isIn(['low', 'medium', 'high', 'urgent']).withMessage('Invalid priority')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      type,
      read,
      priority
    } = req.query;

    const userId = req.user._id;

    // Build query
    const query = { recipient: userId };
    
    if (type) query.type = type;
    if (read !== undefined) query.read = read === 'true';
    if (priority) query.priority = priority;

    // Add expiration filter
    query.$or = [
      { expiresAt: { $exists: false } },
      { expiresAt: { $gt: new Date() } }
    ];

    const notifications = await Notification.find(query)
      .populate('sender', 'name profilePicture')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalCount = await Notification.countDocuments(query);
    const unreadCount = await Notification.getUnreadCount(userId);

    res.json({
      success: true,
      data: {
        notifications: notifications.map(notification => ({
          id: notification._id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          read: notification.read,
          priority: notification.priority,
          category: notification.category,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText,
          imageUrl: notification.imageUrl,
          sender: notification.sender,
          timeAgo: notification.timeAgo,
          createdAt: notification.createdAt,
          readAt: notification.readAt
        })),
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        },
        unreadCount
      }
    });
  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notifications'
    });
  }
});

// Get unread notification count
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const unreadCount = await Notification.getUnreadCount(userId);

    res.json({
      success: true,
      data: {
        unreadCount
      }
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get unread count'
    });
  }
});

// Mark notification as read
router.put('/:notificationId/read', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOne({
      _id: notificationId,
      recipient: userId
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    await notification.markAsRead();

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: {
        id: notification._id,
        read: notification.read,
        readAt: notification.readAt
      }
    });
  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read'
    });
  }
});

// Mark all notifications as read
router.put('/mark-all-read', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const result = await Notification.markAllAsRead(userId);

    res.json({
      success: true,
      message: 'All notifications marked as read',
      data: {
        modifiedCount: result.modifiedCount
      }
    });
  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read'
    });
  }
});

// Delete notification
router.delete('/:notificationId', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user._id;

    const notification = await Notification.findOneAndDelete({
      _id: notificationId,
      recipient: userId
    });

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification'
    });
  }
});

// Get notification preferences
router.get('/preferences', authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    // Get user's notification preferences (you might want to add this to User model)
    const preferences = user.notificationPreferences || {
      email: {
        newInterest: true,
        interestAccepted: true,
        newMessage: true,
        profileViewed: false,
        systemUpdates: true
      },
      sms: {
        newInterest: true,
        interestAccepted: true,
        newMessage: false,
        profileViewed: false,
        systemUpdates: false
      },
      push: {
        newInterest: true,
        interestAccepted: true,
        newMessage: true,
        profileViewed: true,
        systemUpdates: true
      }
    };

    res.json({
      success: true,
      data: {
        preferences
      }
    });
  } catch (error) {
    console.error('Get notification preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification preferences'
    });
  }
});

// Update notification preferences
router.put('/preferences', [
  authenticateToken,
  body('preferences').isObject().withMessage('Preferences must be an object')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { preferences } = req.body;
    const user = req.user;

    // Update user's notification preferences
    user.notificationPreferences = preferences;
    await user.save();

    res.json({
      success: true,
      message: 'Notification preferences updated successfully',
      data: {
        preferences: user.notificationPreferences
      }
    });
  } catch (error) {
    console.error('Update notification preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update notification preferences'
    });
  }
});

// Get notification statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const stats = await notificationService.getNotificationStats(userId);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  } catch (error) {
    console.error('Get notification stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification statistics'
    });
  }
});

// Admin: Send system announcement
router.post('/admin/announcement', [
  authenticateToken,
  requireAdmin,
  body('title').isLength({ min: 1, max: 100 }).withMessage('Title must be between 1 and 100 characters'),
  body('message').isLength({ min: 1, max: 500 }).withMessage('Message must be between 1 and 500 characters'),
  body('recipients').optional().isArray().withMessage('Recipients must be an array'),
  body('sendEmail').optional().isBoolean().withMessage('Send email must be boolean'),
  body('expiresAt').optional().isISO8601().withMessage('Invalid expiration date')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { title, message, recipients = [], sendEmail = false, expiresAt } = req.body;

    const notifications = await notificationService.sendSystemAnnouncement(
      title,
      message,
      recipients,
      {
        sendEmail,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined
      }
    );

    res.status(201).json({
      success: true,
      message: 'System announcement sent successfully',
      data: {
        notificationsSent: notifications.length,
        recipients: recipients.length || 'all_users'
      }
    });
  } catch (error) {
    console.error('Send system announcement error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send system announcement'
    });
  }
});

// Admin: Cleanup old notifications
router.delete('/admin/cleanup', [
  authenticateToken,
  requireAdmin
], async (req, res) => {
  try {
    const result = await notificationService.cleanupOldNotifications();

    res.json({
      success: true,
      message: 'Old notifications cleaned up successfully',
      data: {
        deletedCount: result.deletedCount
      }
    });
  } catch (error) {
    console.error('Cleanup notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cleanup old notifications'
    });
  }
});

module.exports = router;
