import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, <PERSON>, CardB<PERSON>, Card<PERSON>ooter, Divider, Image } from '@heroui/react';
import { Icon } from '@iconify/react';
import { SuccessStory } from '../types/user';

// Mock success stories data
const successStories: SuccessStory[] = [
  {
    id: '1',
    coupleNames: '<PERSON><PERSON> & <PERSON>riya',
    marriageDate: '2023-11-15',
    story: 'We connected on BharatMatrimony and after a few meetings, we knew we were meant to be together. Our families met and everything fell into place perfectly.',
    photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=11'],
    testimonial: 'BharatMatrimony made our journey to finding love so smooth and wonderful!',
    location: 'Mumbai, Maharashtra',
    createdAt: '2023-12-10',
  },
  {
    id: '2',
    coupleNames: '<PERSON><PERSON><PERSON> & <PERSON>',
    marriageDate: '2023-09-22',
    story: 'Despite living in different cities, we found each other on BharatMatrimony. After months of calls and video chats, we decided to take the next step.',
    photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=12'],
    testimonial: 'Distance was never a barrier for us, thanks to BharatMatrimony!',
    location: 'Bangalore, Karnataka',
    createdAt: '2023-10-15',
  },
  {
    id: '3',
    coupleNames: 'Arjun & Nisha',
    marriageDate: '2023-12-05',
    story: 'Our parents created our profiles on BharatMatrimony. We connected instantly and after meeting a few times, we knew we wanted to spend our lives together.',
    photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=13'],
    testimonial: 'From strangers to soulmates, our journey was beautiful!',
    location: 'Delhi, NCR',
    createdAt: '2024-01-20',
  },
];

export const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-50 to-primary-100 py-16">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                Find Your Perfect <span className="text-primary">Life Partner</span>
              </h1>
              <p className="text-lg text-gray-700 mb-8">
                India's most trusted matrimony service for Indians worldwide. Start your journey to find your perfect match today.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/register">
                  <Button color="primary" size="lg">
                    Register Free
                  </Button>
                </Link>
                <Link to="/login">
                  <Button variant="flat" color="default" size="lg">
                    Login
                  </Button>
                </Link>
              </div>
            </div>
            <div className="md:w-1/2 flex justify-center">
              <Image
                src="https://img.heroui.chat/image/ai?w=600&h=500&u=1"
                alt="Happy Indian couple"
                className="rounded-lg shadow-lg max-w-full h-auto"
                width={500}
                height={400}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
            <div className="p-4">
              <h3 className="text-3xl font-bold text-primary mb-2">10M+</h3>
              <p className="text-gray-600">Registered Users</p>
            </div>
            <div className="p-4">
              <h3 className="text-3xl font-bold text-primary mb-2">1M+</h3>
              <p className="text-gray-600">Success Stories</p>
            </div>
            <div className="p-4">
              <h3 className="text-3xl font-bold text-primary mb-2">5000+</h3>
              <p className="text-gray-600">Daily Matches</p>
            </div>
            <div className="p-4">
              <h3 className="text-3xl font-bold text-primary mb-2">20+</h3>
              <p className="text-gray-600">Years of Trust</p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="flex flex-col items-center text-center">
              <div className="bg-primary-100 p-4 rounded-full mb-4">
                <Icon icon="lucide:user-plus" className="text-primary text-3xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Create Profile</h3>
              <p className="text-gray-600">Register and create your detailed profile with personal information, preferences, and photos.</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-primary-100 p-4 rounded-full mb-4">
                <Icon icon="lucide:search" className="text-primary text-3xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Find Matches</h3>
              <p className="text-gray-600">Browse through potential matches based on your preferences and compatibility.</p>
            </div>
            <div className="flex flex-col items-center text-center">
              <div className="bg-primary-100 p-4 rounded-full mb-4">
                <Icon icon="lucide:message-circle" className="text-primary text-3xl" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Connect & Meet</h3>
              <p className="text-gray-600">Express interest, communicate with matches, and take the next step towards marriage.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Success Stories Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-4">Success Stories</h2>
          <p className="text-center text-gray-600 mb-12">Thousands of happy couples found their perfect match on BharatMatrimony</p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {successStories.map((story) => (
              <Card key={story.id} className="max-w-md">
                <CardBody className="overflow-visible p-0">
                  <Image
                    shadow="sm"
                    radius="lg"
                    width="100%"
                    alt={story.coupleNames}
                    className="w-full object-cover h-[200px]"
                    src={story.photos[0]}
                  />
                </CardBody>
                <CardFooter className="flex flex-col items-start">
                  <h4 className="font-bold text-large">{story.coupleNames}</h4>
                  <p className="text-default-500 text-small mb-2">{story.location}</p>
                  <p className="text-sm line-clamp-3">{story.story}</p>
                </CardFooter>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-10">
            <Link to="/success-stories">
              <Button color="primary" variant="flat">
                View More Success Stories
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose BharatMatrimony</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:shield-check" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Verified Profiles</h3>
              <p className="text-gray-600">All profiles undergo strict verification process to ensure authenticity.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:filter" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Advanced Matching</h3>
              <p className="text-gray-600">Our smart algorithm suggests matches based on your preferences.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:star" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Horoscope Matching</h3>
              <p className="text-gray-600">Traditional kundli matching for astrological compatibility.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:lock" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Privacy Control</h3>
              <p className="text-gray-600">You decide who can view your profile and contact information.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:headphones" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Dedicated Support</h3>
              <p className="text-gray-600">Our relationship managers are available to assist you 24/7.</p>
            </div>
            <div className="bg-white p-6 rounded-lg shadow-sm">
              <Icon icon="lucide:users" className="text-primary text-3xl mb-4" />
              <h3 className="text-xl font-semibold mb-2">Community Focus</h3>
              <p className="text-gray-600">Find matches from your community, caste, or region.</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-primary-600">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready to Find Your Perfect Match?</h2>
          <p className="text-white text-lg mb-8 max-w-2xl mx-auto">
            Join millions of happy users who found their life partner through BharatMatrimony. Your perfect match is just a click away!
          </p>
          <Link to="/register">
            <Button color="default" size="lg" className="bg-white text-primary">
              Register Free Today
            </Button>
          </Link>
        </div>
      </section>
    </div>
  );
};