const express = require('express');
const cors = require('cors');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');

const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// In-memory storage (will be replaced with SQLite later)
let users = [];
let interests = [];
let messages = [];
let conversations = [];
let nextId = 1;

// Utility functions
const generateId = () => (nextId++).toString();
const findUserByEmail = (email) => users.find(u => u.email === email);
const findUserById = (id) => users.find(u => u.id === id);

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: 'Access token required'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'matrimony-secret-key');
    const user = findUserById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
};

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Matrimony API is running',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    database: 'In-Memory (Development)',
    totalUsers: users.length
  });
});

// Register
app.post('/api/auth/register', async (req, res) => {
  try {
    const { name, email, password, phone, profileType } = req.body;

    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Name, email, and password are required'
      });
    }

    if (findUserByEmail(email)) {
      return res.status(409).json({
        success: false,
        message: 'User with this email already exists'
      });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const user = {
      id: generateId(),
      name,
      email,
      password: hashedPassword,
      phone: phone || '',
      profileType: profileType || 'self',
      membershipType: 'free',
      isActive: true,
      profileCompleted: false,
      profileCompletionPercentage: 20,
      profilePicture: '',
      personalInfo: {},
      familyInfo: {},
      educationCareer: {},
      lifestyle: {},
      religiousInfo: {},
      partnerPreferences: {},
      verificationStatus: {
        email: false,
        phone: false,
        photo: false,
        document: false
      },
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString()
    };

    users.push(user);

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'matrimony-secret-key',
      { expiresIn: '7d' }
    );

    const { password: _, ...userResponse } = user;

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResponse,
        token,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed'
    });
  }
});

// Login
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Email and password are required'
      });
    }

    const user = findUserByEmail(email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    user.lastActive = new Date().toISOString();

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'matrimony-secret-key',
      { expiresIn: '7d' }
    );

    const { password: _, ...userResponse } = user;

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: userResponse,
        token,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed'
    });
  }
});

// Verify token
app.get('/api/auth/verify', authenticateToken, (req, res) => {
  const { password: _, ...userResponse } = req.user;
  res.json({
    success: true,
    data: {
      user: userResponse,
      valid: true
    }
  });
});

// Get user profile
app.get('/api/users/profile', authenticateToken, (req, res) => {
  const { password: _, ...userResponse } = req.user;
  res.json({
    success: true,
    data: { user: userResponse }
  });
});

// Update profile
app.put('/api/users/profile', authenticateToken, (req, res) => {
  try {
    const updates = req.body;
    const user = req.user;
    
    // Update allowed fields
    const allowedFields = ['name', 'phone', 'personalInfo', 'familyInfo', 'educationCareer', 'lifestyle', 'religiousInfo', 'partnerPreferences'];
    
    allowedFields.forEach(field => {
      if (updates[field] !== undefined) {
        user[field] = updates[field];
      }
    });
    
    // Calculate profile completion
    let completionScore = 20; // Base score for registration
    
    if (user.personalInfo && Object.keys(user.personalInfo).length > 0) completionScore += 20;
    if (user.educationCareer && Object.keys(user.educationCareer).length > 0) completionScore += 20;
    if (user.familyInfo && Object.keys(user.familyInfo).length > 0) completionScore += 15;
    if (user.religiousInfo && Object.keys(user.religiousInfo).length > 0) completionScore += 15;
    if (user.lifestyle && Object.keys(user.lifestyle).length > 0) completionScore += 10;
    
    user.profileCompletionPercentage = Math.min(completionScore, 100);
    user.profileCompleted = user.profileCompletionPercentage >= 80;
    
    const { password: _, ...userResponse } = user;
    
    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: userResponse }
    });
    
  } catch (error) {
    console.error('Profile update error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update profile'
    });
  }
});

// Search profiles
app.get('/api/search', authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 20, ageMin, ageMax, city, religion } = req.query;
    const currentUserId = req.user.id;
    
    let filteredUsers = users.filter(user => 
      user.id !== currentUserId && 
      user.isActive &&
      user.profileCompleted
    );
    
    // Apply filters
    if (ageMin || ageMax) {
      filteredUsers = filteredUsers.filter(user => {
        const age = user.personalInfo?.age;
        if (!age) return false;
        if (ageMin && age < parseInt(ageMin)) return false;
        if (ageMax && age > parseInt(ageMax)) return false;
        return true;
      });
    }
    
    if (city) {
      filteredUsers = filteredUsers.filter(user => 
        user.personalInfo?.city?.toLowerCase().includes(city.toLowerCase())
      );
    }
    
    if (religion) {
      filteredUsers = filteredUsers.filter(user => 
        user.religiousInfo?.religion?.toLowerCase() === religion.toLowerCase()
      );
    }
    
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);
    
    const profiles = paginatedUsers.map(user => {
      const { password: _, ...profile } = user;
      return profile;
    });

    res.json({
      success: true,
      data: {
        profiles,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredUsers.length / limit),
          totalResults: filteredUsers.length,
          hasNext: endIndex < filteredUsers.length,
          hasPrev: page > 1
        }
      }
    });
    
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed'
    });
  }
});

// Error handling
app.use((error, req, res, next) => {
  console.error('Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Matrimony server running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔗 Frontend: http://localhost:5173`);
  console.log(`📊 Database: In-Memory (Development Mode)`);
  console.log(`✅ Ready to accept connections`);
});

module.exports = app;
