import React from 'react';
import { useHistory } from 'react-router-dom';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Tabs, 
  Tab, 
  Input,
  Select,
  SelectItem,
  Textarea,
  RadioGroup,
  Radio,
  Checkbox,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const ProfileCreationPage: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState('personal');
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  const [success, setSuccess] = React.useState('');
  
  const { user } = useAuth();
  const history = useHistory();

  // Personal Information
  const [height, setHeight] = React.useState('');
  const [weight, setWeight] = React.useState('');
  const [maritalStatus, setMaritalStatus] = React.useState('');
  const [physicalStatus, setPhysicalStatus] = React.useState('normal');
  const [complexion, setComplexion] = React.useState('');
  const [bodyType, setBodyType] = React.useState('');
  const [aboutMe, setAboutMe] = React.useState('');
  const [country, setCountry] = React.useState('India');
  const [state, setState] = React.useState('');
  const [city, setCity] = React.useState('');
  const [willingToRelocate, setWillingToRelocate] = React.useState(false);
  const [subCaste, setSubCaste] = React.useState('');
  const [gothra, setGothra] = React.useState('');
  const [knownLanguages, setKnownLanguages] = React.useState<string[]>([]);

  // Family Information
  const [familyType, setFamilyType] = React.useState('');
  const [familyStatus, setFamilyStatus] = React.useState('');
  const [familyValues, setFamilyValues] = React.useState('');
  const [fatherOccupation, setFatherOccupation] = React.useState('');
  const [motherOccupation, setMotherOccupation] = React.useState('');
  const [fatherStatus, setFatherStatus] = React.useState('alive');
  const [motherStatus, setMotherStatus] = React.useState('alive');
  const [brothers, setBrothers] = React.useState('0');
  const [sisters, setSisters] = React.useState('0');
  const [marriedBrothers, setMarriedBrothers] = React.useState('0');
  const [marriedSisters, setMarriedSisters] = React.useState('0');
  const [familyIncome, setFamilyIncome] = React.useState('');
  const [familyBasedOut, setFamilyBasedOut] = React.useState('');
  const [aboutFamily, setAboutFamily] = React.useState('');

  // Education & Career
  const [highestEducation, setHighestEducation] = React.useState('');
  const [educationField, setEducationField] = React.useState('');
  const [university, setUniversity] = React.useState('');
  const [yearOfPassing, setYearOfPassing] = React.useState('');
  const [otherQualifications, setOtherQualifications] = React.useState('');
  const [occupation, setOccupation] = React.useState('');
  const [employedIn, setEmployedIn] = React.useState('');
  const [companyName, setCompanyName] = React.useState('');
  const [designation, setDesignation] = React.useState('');
  const [workLocation, setWorkLocation] = React.useState('');
  const [annualIncome, setAnnualIncome] = React.useState('');
  const [workExperience, setWorkExperience] = React.useState('');
  const [careerDescription, setCareerDescription] = React.useState('');

  // Lifestyle
  const [diet, setDiet] = React.useState('');
  const [drinking, setDrinking] = React.useState('');
  const [smoking, setSmoking] = React.useState('');
  const [hobbies, setHobbies] = React.useState<string[]>([]);
  const [interests, setInterests] = React.useState<string[]>([]);
  const [dressStyle, setDressStyle] = React.useState('');

  // Partner Preferences
  const [ageRangeMin, setAgeRangeMin] = React.useState('');
  const [ageRangeMax, setAgeRangeMax] = React.useState('');
  const [heightRangeMin, setHeightRangeMin] = React.useState('');
  const [heightRangeMax, setHeightRangeMax] = React.useState('');
  const [preferredMaritalStatus, setPreferredMaritalStatus] = React.useState<string[]>([]);
  const [preferredReligion, setPreferredReligion] = React.useState<string[]>([]);
  const [preferredCaste, setPreferredCaste] = React.useState<string[]>([]);
  const [preferredEducation, setPreferredEducation] = React.useState<string[]>([]);
  const [preferredOccupation, setPreferredOccupation] = React.useState<string[]>([]);
  const [incomeRangeMin, setIncomeRangeMin] = React.useState('');
  const [incomeRangeMax, setIncomeRangeMax] = React.useState('');
  const [preferredDiet, setPreferredDiet] = React.useState<string[]>([]);
  const [preferredCountries, setPreferredCountries] = React.useState<string[]>(['India']);
  const [preferredStates, setPreferredStates] = React.useState<string[]>([]);
  const [manglikStatus, setManglikStatus] = React.useState('');
  const [lookingFor, setLookingFor] = React.useState('');

  // Horoscope
  const [userManglikStatus, setUserManglikStatus] = React.useState('');
  const [timeOfBirth, setTimeOfBirth] = React.useState('');
  const [placeOfBirth, setPlaceOfBirth] = React.useState('');
  const [nakshatra, setNakshatra] = React.useState('');
  const [rashi, setRashi] = React.useState('');
  const [horoscopeMatch, setHoroscopeMatch] = React.useState(true);

  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };

  const handleNext = () => {
    const tabs = ['personal', 'family', 'education', 'lifestyle', 'preferences', 'horoscope'];
    const currentIndex = tabs.indexOf(activeTab);
    if (currentIndex < tabs.length - 1) {
      setActiveTab(tabs[currentIndex + 1]);
    }
  };

  const handlePrevious = () => {
    const tabs = ['personal', 'family', 'education', 'lifestyle', 'preferences', 'horoscope'];
    const currentIndex = tabs.indexOf(activeTab);
    if (currentIndex > 0) {
      setActiveTab(tabs[currentIndex - 1]);
    }
  };

  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      setError('');
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      setSuccess('Profile created successfully!');
      
      // Redirect to dashboard after a short delay
      setTimeout(() => {
        history.push('/dashboard');
      }, 2000);
    } catch (err) {
      setError('Failed to create profile. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const heightOptions = Array.from({ length: 61 }, (_, i) => {
    const heightInCm = 137 + i;
    const feet = Math.floor(heightInCm / 30.48);
    const inches = Math.round((heightInCm / 2.54) % 12);
    return { 
      key: heightInCm.toString(), 
      label: `${feet}'${inches}" - ${heightInCm} cm` 
    };
  });

  const maritalStatusOptions = [
    { key: 'never_married', label: 'Never Married' },
    { key: 'divorced', label: 'Divorced' },
    { key: 'widowed', label: 'Widowed' },
    { key: 'awaiting_divorce', label: 'Awaiting Divorce' },
  ];

  const educationOptions = [
    { key: 'high_school', label: 'High School' },
    { key: 'bachelor', label: 'Bachelor\'s Degree' },
    { key: 'master', label: 'Master\'s Degree' },
    { key: 'doctorate', label: 'Doctorate' },
    { key: 'diploma', label: 'Diploma' },
    { key: 'other', label: 'Other' },
  ];

  const occupationOptions = [
    { key: 'software', label: 'Software Professional' },
    { key: 'doctor', label: 'Doctor' },
    { key: 'engineer', label: 'Engineer' },
    { key: 'teacher', label: 'Teacher' },
    { key: 'business', label: 'Business Owner' },
    { key: 'government', label: 'Government Employee' },
    { key: 'other', label: 'Other' },
  ];

  const incomeOptions = [
    { key: 'below_5', label: 'Below 5 Lakhs' },
    { key: '5_10', label: '5-10 Lakhs' },
    { key: '10_15', label: '10-15 Lakhs' },
    { key: '15_20', label: '15-20 Lakhs' },
    { key: '20_30', label: '20-30 Lakhs' },
    { key: '30_50', label: '30-50 Lakhs' },
    { key: 'above_50', label: 'Above 50 Lakhs' },
  ];

  const dietOptions = [
    { key: 'vegetarian', label: 'Vegetarian' },
    { key: 'non_vegetarian', label: 'Non-Vegetarian' },
    { key: 'eggetarian', label: 'Eggetarian' },
    { key: 'vegan', label: 'Vegan' },
    { key: 'jain', label: 'Jain' },
  ];

  const hobbyOptions = [
    'Reading', 'Cooking', 'Traveling', 'Photography', 'Music', 'Dancing',
    'Painting', 'Sports', 'Gardening', 'Yoga', 'Meditation', 'Movies',
    'Writing', 'Singing', 'Hiking', 'Swimming', 'Cycling', 'Gaming'
  ];

  const stateOptions = [
    { key: 'andhra_pradesh', label: 'Andhra Pradesh' },
    { key: 'telangana', label: 'Telangana' },
    { key: 'tamil_nadu', label: 'Tamil Nadu' },
    { key: 'karnataka', label: 'Karnataka' },
    { key: 'kerala', label: 'Kerala' },
    { key: 'maharashtra', label: 'Maharashtra' },
    { key: 'gujarat', label: 'Gujarat' },
    { key: 'delhi', label: 'Delhi' },
    { key: 'punjab', label: 'Punjab' },
    { key: 'haryana', label: 'Haryana' },
    { key: 'uttar_pradesh', label: 'Uttar Pradesh' },
    { key: 'west_bengal', label: 'West Bengal' },
  ];

  const rashiOptions = [
    { key: 'aries', label: 'Aries (Mesha)' },
    { key: 'taurus', label: 'Taurus (Vrishabha)' },
    { key: 'gemini', label: 'Gemini (Mithuna)' },
    { key: 'cancer', label: 'Cancer (Karka)' },
    { key: 'leo', label: 'Leo (Simha)' },
    { key: 'virgo', label: 'Virgo (Kanya)' },
    { key: 'libra', label: 'Libra (Tula)' },
    { key: 'scorpio', label: 'Scorpio (Vrishchika)' },
    { key: 'sagittarius', label: 'Sagittarius (Dhanu)' },
    { key: 'capricorn', label: 'Capricorn (Makara)' },
    { key: 'aquarius', label: 'Aquarius (Kumbha)' },
    { key: 'pisces', label: 'Pisces (Meena)' },
  ];

  const nakshatraOptions = [
    { key: 'ashwini', label: 'Ashwini' },
    { key: 'bharani', label: 'Bharani' },
    { key: 'krittika', label: 'Krittika' },
    { key: 'rohini', label: 'Rohini' },
    { key: 'mrigashira', label: 'Mrigashira' },
    { key: 'ardra', label: 'Ardra' },
    { key: 'punarvasu', label: 'Punarvasu' },
    { key: 'pushya', label: 'Pushya' },
    { key: 'ashlesha', label: 'Ashlesha' },
    { key: 'magha', label: 'Magha' },
    { key: 'purva_phalguni', label: 'Purva Phalguni' },
    { key: 'uttara_phalguni', label: 'Uttara Phalguni' },
    { key: 'hasta', label: 'Hasta' },
    { key: 'chitra', label: 'Chitra' },
    { key: 'swati', label: 'Swati' },
    { key: 'vishakha', label: 'Vishakha' },
    { key: 'anuradha', label: 'Anuradha' },
    { key: 'jyeshtha', label: 'Jyeshtha' },
    { key: 'mula', label: 'Mula' },
    { key: 'purva_ashadha', label: 'Purva Ashadha' },
    { key: 'uttara_ashadha', label: 'Uttara Ashadha' },
    { key: 'shravana', label: 'Shravana' },
    { key: 'dhanishta', label: 'Dhanishta' },
    { key: 'shatabhisha', label: 'Shatabhisha' },
    { key: 'purva_bhadrapada', label: 'Purva Bhadrapada' },
    { key: 'uttara_bhadrapada', label: 'Uttara Bhadrapada' },
    { key: 'revati', label: 'Revati' },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="container mx-auto max-w-4xl">
        <Card>
          <CardHeader className="flex flex-col gap-1 items-center">
            <h1 className="text-2xl font-bold">Complete Your Profile</h1>
            <p className="text-default-500 text-sm">
              Fill in your details to find the perfect match
            </p>
          </CardHeader>
          
          <CardBody>
            {error && (
              <div className="bg-danger-50 text-danger p-3 rounded-md mb-4 text-sm">
                {error}
              </div>
            )}
            
            {success && (
              <div className="bg-success-50 text-success p-3 rounded-md mb-4 text-sm">
                {success}
              </div>
            )}
            
            <Tabs 
              aria-label="Profile Creation" 
              selectedKey={activeTab} 
              onSelectionChange={handleTabChange}
              className="mb-6"
            >
              <Tab key="personal" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:user" />
                  <span>Personal</span>
                </div>
              } />
              <Tab key="family" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:users" />
                  <span>Family</span>
                </div>
              } />
              <Tab key="education" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:graduation-cap" />
                  <span>Education</span>
                </div>
              } />
              <Tab key="lifestyle" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:heart" />
                  <span>Lifestyle</span>
                </div>
              } />
              <Tab key="preferences" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:filter" />
                  <span>Preferences</span>
                </div>
              } />
              <Tab key="horoscope" title={
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:star" />
                  <span>Horoscope</span>
                </div>
              } />
            </Tabs>
            
            <div className="mt-4">
              {activeTab === 'personal' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Personal Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Height"
                      placeholder="Select your height"
                      selectedKeys={height ? [height] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setHeight(selected);
                      }}
                    >
                      {heightOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Input
                      type="number"
                      label="Weight (kg)"
                      placeholder="Enter your weight"
                      value={weight}
                      onValueChange={setWeight}
                    />
                    
                    <Select
                      label="Marital Status"
                      placeholder="Select your marital status"
                      selectedKeys={maritalStatus ? [maritalStatus] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setMaritalStatus(selected);
                      }}
                    >
                      {maritalStatusOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <div>
                      <p className="text-sm font-medium mb-2">Physical Status</p>
                      <RadioGroup 
                        orientation="horizontal" 
                        value={physicalStatus} 
                        onValueChange={setPhysicalStatus}
                      >
                        <Radio value="normal">Normal</Radio>
                        <Radio value="physically_challenged">Physically Challenged</Radio>
                      </RadioGroup>
                    </div>
                    
                    <Select
                      label="Complexion"
                      placeholder="Select your complexion"
                      selectedKeys={complexion ? [complexion] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setComplexion(selected);
                      }}
                    >
                      <SelectItem key="fair" value="fair">Fair</SelectItem>
                      <SelectItem key="wheatish" value="wheatish">Wheatish</SelectItem>
                      <SelectItem key="wheatish_brown" value="wheatish_brown">Wheatish Brown</SelectItem>
                      <SelectItem key="brown" value="brown">Brown</SelectItem>
                      <SelectItem key="dark" value="dark">Dark</SelectItem>
                    </Select>
                    
                    <Select
                      label="Body Type"
                      placeholder="Select your body type"
                      selectedKeys={bodyType ? [bodyType] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setBodyType(selected);
                      }}
                    >
                      <SelectItem key="slim" value="slim">Slim</SelectItem>
                      <SelectItem key="athletic" value="athletic">Athletic</SelectItem>
                      <SelectItem key="average" value="average">Average</SelectItem>
                      <SelectItem key="heavy" value="heavy">Heavy</SelectItem>
                    </Select>
                  </div>
                  
                  <Divider />
                  
                  <h3 className="text-lg font-semibold">Location</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Country"
                      placeholder="Enter your country"
                      value={country}
                      onValueChange={setCountry}
                    />
                    
                    <Select
                      label="State"
                      placeholder="Select your state"
                      selectedKeys={state ? [state] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setState(selected);
                      }}
                    >
                      {stateOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Input
                      label="City"
                      placeholder="Enter your city"
                      value={city}
                      onValueChange={setCity}
                    />
                    
                    <div className="flex items-center h-full">
                      <Checkbox 
                        isSelected={willingToRelocate}
                        onValueChange={setWillingToRelocate}
                      >
                        Willing to relocate
                      </Checkbox>
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <h3 className="text-lg font-semibold">Additional Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Sub-caste (Optional)"
                      placeholder="Enter your sub-caste"
                      value={subCaste}
                      onValueChange={setSubCaste}
                    />
                    
                    <Input
                      label="Gothra (Optional)"
                      placeholder="Enter your gothra"
                      value={gothra}
                      onValueChange={setGothra}
                    />
                  </div>
                  
                  <Textarea
                    label="About Me"
                    placeholder="Write something about yourself"
                    value={aboutMe}
                    onValueChange={setAboutMe}
                    minRows={3}
                  />
                </div>
              )}
              
              {activeTab === 'family' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Family Information</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Family Type"
                      placeholder="Select your family type"
                      selectedKeys={familyType ? [familyType] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setFamilyType(selected);
                      }}
                    >
                      <SelectItem key="nuclear" value="nuclear">Nuclear</SelectItem>
                      <SelectItem key="joint" value="joint">Joint</SelectItem>
                      <SelectItem key="others" value="others">Others</SelectItem>
                    </Select>
                    
                    <Select
                      label="Family Status"
                      placeholder="Select your family status"
                      selectedKeys={familyStatus ? [familyStatus] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setFamilyStatus(selected);
                      }}
                    >
                      <SelectItem key="middle_class" value="middle_class">Middle Class</SelectItem>
                      <SelectItem key="upper_middle_class" value="upper_middle_class">Upper Middle Class</SelectItem>
                      <SelectItem key="rich" value="rich">Rich</SelectItem>
                      <SelectItem key="affluent" value="affluent">Affluent</SelectItem>
                    </Select>
                    
                    <Select
                      label="Family Values"
                      placeholder="Select your family values"
                      selectedKeys={familyValues ? [familyValues] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setFamilyValues(selected);
                      }}
                    >
                      <SelectItem key="orthodox" value="orthodox">Orthodox</SelectItem>
                      <SelectItem key="traditional" value="traditional">Traditional</SelectItem>
                      <SelectItem key="moderate" value="moderate">Moderate</SelectItem>
                      <SelectItem key="liberal" value="liberal">Liberal</SelectItem>
                    </Select>
                    
                    <Input
                      label="Father's Occupation"
                      placeholder="Enter your father's occupation"
                      value={fatherOccupation}
                      onValueChange={setFatherOccupation}
                    />
                    
                    <Input
                      label="Mother's Occupation"
                      placeholder="Enter your mother's occupation"
                      value={motherOccupation}
                      onValueChange={setMotherOccupation}
                    />
                    
                    <div>
                      <p className="text-sm font-medium mb-2">Father's Status</p>
                      <RadioGroup 
                        orientation="horizontal" 
                        value={fatherStatus} 
                        onValueChange={setFatherStatus}
                      >
                        <Radio value="alive">Alive</Radio>
                        <Radio value="deceased">Deceased</Radio>
                      </RadioGroup>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium mb-2">Mother's Status</p>
                      <RadioGroup 
                        orientation="horizontal" 
                        value={motherStatus} 
                        onValueChange={setMotherStatus}
                      >
                        <Radio value="alive">Alive</Radio>
                        <Radio value="deceased">Deceased</Radio>
                      </RadioGroup>
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <h3 className="text-lg font-semibold">Siblings</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Number of Brothers"
                      placeholder="Select number of brothers"
                      selectedKeys={brothers ? [brothers] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setBrothers(selected);
                      }}
                    >
                      {Array.from({ length: 6 }, (_, i) => (
                        <SelectItem key={i.toString()} value={i.toString()}>
                          {i}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Number of Sisters"
                      placeholder="Select number of sisters"
                      selectedKeys={sisters ? [sisters] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setSisters(selected);
                      }}
                    >
                      {Array.from({ length: 6 }, (_, i) => (
                        <SelectItem key={i.toString()} value={i.toString()}>
                          {i}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Married Brothers"
                      placeholder="Select number of married brothers"
                      selectedKeys={marriedBrothers ? [marriedBrothers] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setMarriedBrothers(selected);
                      }}
                      isDisabled={brothers === '0'}
                    >
                      {Array.from({ length: parseInt(brothers || '0') + 1 }, (_, i) => (
                        <SelectItem key={i.toString()} value={i.toString()}>
                          {i}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Married Sisters"
                      placeholder="Select number of married sisters"
                      selectedKeys={marriedSisters ? [marriedSisters] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setMarriedSisters(selected);
                      }}
                      isDisabled={sisters === '0'}
                    >
                      {Array.from({ length: parseInt(sisters || '0') + 1 }, (_, i) => (
                        <SelectItem key={i.toString()} value={i.toString()}>
                          {i}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>
                  
                  <Divider />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Family Income"
                      placeholder="Select family income"
                      selectedKeys={familyIncome ? [familyIncome] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setFamilyIncome(selected);
                      }}
                    >
                      {incomeOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Input
                      label="Family Based Out"
                      placeholder="Where is your family based"
                      value={familyBasedOut}
                      onValueChange={setFamilyBasedOut}
                    />
                  </div>
                  
                  <Textarea
                    label="About Family"
                    placeholder="Write something about your family"
                    value={aboutFamily}
                    onValueChange={setAboutFamily}
                    minRows={3}
                  />
                </div>
              )}
              
              {activeTab === 'education' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Education</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Highest Education"
                      placeholder="Select your highest education"
                      selectedKeys={highestEducation ? [highestEducation] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setHighestEducation(selected);
                      }}
                    >
                      {educationOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Input
                      label="Education Field"
                      placeholder="E.g., Computer Science, Medicine"
                      value={educationField}
                      onValueChange={setEducationField}
                    />
                    
                    <Input
                      label="University/College"
                      placeholder="Enter your university or college name"
                      value={university}
                      onValueChange={setUniversity}
                    />
                    
                    <Input
                      label="Year of Passing"
                      placeholder="Enter year of passing"
                      value={yearOfPassing}
                      onValueChange={setYearOfPassing}
                    />
                  </div>
                  
                  <Input
                    label="Other Qualifications (Optional)"
                    placeholder="Any other qualifications or certifications"
                    value={otherQualifications}
                    onValueChange={setOtherQualifications}
                  />
                  
                  <Divider />
                  
                  <h3 className="text-lg font-semibold">Career</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Occupation"
                      placeholder="Select your occupation"
                      selectedKeys={occupation ? [occupation] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setOccupation(selected);
                      }}
                    >
                      {occupationOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Employed In"
                      placeholder="Select your employment type"
                      selectedKeys={employedIn ? [employedIn] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setEmployedIn(selected);
                      }}
                    >
                      <SelectItem key="government" value="government">Government</SelectItem>
                      <SelectItem key="private" value="private">Private</SelectItem>
                      <SelectItem key="business" value="business">Business</SelectItem>
                      <SelectItem key="self_employed" value="self_employed">Self Employed</SelectItem>
                      <SelectItem key="not_working" value="not_working">Not Working</SelectItem>
                    </Select>
                    
                    <Input
                      label="Company Name"
                      placeholder="Enter your company name"
                      value={companyName}
                      onValueChange={setCompanyName}
                      isDisabled={employedIn === 'not_working'}
                    />
                    
                    <Input
                      label="Designation"
                      placeholder="Enter your designation"
                      value={designation}
                      onValueChange={setDesignation}
                      isDisabled={employedIn === 'not_working'}
                    />
                    
                    <Input
                      label="Work Location"
                      placeholder="Enter your work location"
                      value={workLocation}
                      onValueChange={setWorkLocation}
                      isDisabled={employedIn === 'not_working'}
                    />
                    
                    <Select
                      label="Annual Income"
                      placeholder="Select your annual income"
                      selectedKeys={annualIncome ? [annualIncome] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setAnnualIncome(selected);
                      }}
                      isDisabled={employedIn === 'not_working'}
                    >
                      {incomeOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Input
                      label="Work Experience (Years)"
                      placeholder="Enter your work experience"
                      value={workExperience}
                      onValueChange={setWorkExperience}
                      isDisabled={employedIn === 'not_working'}
                    />
                  </div>
                  
                  <Textarea
                    label="Career Description (Optional)"
                    placeholder="Describe your career path and aspirations"
                    value={careerDescription}
                    onValueChange={setCareerDescription}
                    minRows={3}
                    isDisabled={employedIn === 'not_working'}
                  />
                </div>
              )}
              
              {activeTab === 'lifestyle' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Lifestyle Preferences</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Diet"
                      placeholder="Select your diet preference"
                      selectedKeys={diet ? [diet] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setDiet(selected);
                      }}
                    >
                      {dietOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Drinking"
                      placeholder="Select your drinking habit"
                      selectedKeys={drinking ? [drinking] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setDrinking(selected);
                      }}
                    >
                      <SelectItem key="no" value="no">No</SelectItem>
                      <SelectItem key="occasionally" value="occasionally">Occasionally</SelectItem>
                      <SelectItem key="yes" value="yes">Yes</SelectItem>
                    </Select>
                    
                    <Select
                      label="Smoking"
                      placeholder="Select your smoking habit"
                      selectedKeys={smoking ? [smoking] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setSmoking(selected);
                      }}
                    >
                      <SelectItem key="no" value="no">No</SelectItem>
                      <SelectItem key="occasionally" value="occasionally">Occasionally</SelectItem>
                      <SelectItem key="yes" value="yes">Yes</SelectItem>
                    </Select>
                    
                    <Select
                      label="Dress Style"
                      placeholder="Select your dress style"
                      selectedKeys={dressStyle ? [dressStyle] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setDressStyle(selected);
                      }}
                    >
                      <SelectItem key="traditional" value="traditional">Traditional</SelectItem>
                      <SelectItem key="modern" value="modern">Modern</SelectItem>
                      <SelectItem key="mixed" value="mixed">Mixed</SelectItem>
                    </Select>
                  </div>
                  
                  <Divider />
                  
                  <h3 className="text-lg font-semibold">Hobbies & Interests</h3>
                  <div className="space-y-4">
                    <p className="text-sm font-medium mb-2">Hobbies (Select all that apply)</p>
                    <div className="flex flex-wrap gap-2">
                      {hobbyOptions.map((hobby) => (
                        <Checkbox
                          key={hobby}
                          isSelected={hobbies.includes(hobby)}
                          onValueChange={(isSelected) => {
                            if (isSelected) {
                              setHobbies([...hobbies, hobby]);
                            } else {
                              setHobbies(hobbies.filter(h => h !== hobby));
                            }
                          }}
                          size="sm"
                        >
                          {hobby}
                        </Checkbox>
                      ))}
                    </div>
                    
                    <Textarea
                      label="Other Interests"
                      placeholder="Describe your other interests"
                      value={interests.join(', ')}
                      onValueChange={(value) => setInterests(value.split(',').map(item => item.trim()))}
                      minRows={2}
                    />
                  </div>
                </div>
              )}
              
              {activeTab === 'preferences' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Partner Preferences</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium mb-2">Age Range</p>
                      <div className="flex gap-2 items-center">
                        <Input
                          type="number"
                          placeholder="Min"
                          value={ageRangeMin}
                          onValueChange={setAgeRangeMin}
                          className="w-24"
                        />
                        <span>to</span>
                        <Input
                          type="number"
                          placeholder="Max"
                          value={ageRangeMax}
                          onValueChange={setAgeRangeMax}
                          className="w-24"
                        />
                        <span>years</span>
                      </div>
                    </div>
                    
                    <div>
                      <p className="text-sm font-medium mb-2">Height Range</p>
                      <div className="flex gap-2 items-center">
                        <Select
                          placeholder="Min"
                          selectedKeys={heightRangeMin ? [heightRangeMin] : []}
                          onSelectionChange={(keys) => {
                            const selected = Array.from(keys)[0] as string;
                            setHeightRangeMin(selected);
                          }}
                          className="w-40"
                        >
                          {heightOptions.map((item) => (
                            <SelectItem key={item.key} value={item.key}>
                              {item.label}
                            </SelectItem>
                          ))}
                        </Select>
                        <span>to</span>
                        <Select
                          placeholder="Max"
                          selectedKeys={heightRangeMax ? [heightRangeMax] : []}
                          onSelectionChange={(keys) => {
                            const selected = Array.from(keys)[0] as string;
                            setHeightRangeMax(selected);
                          }}
                          className="w-40"
                        >
                          {heightOptions.map((item) => (
                            <SelectItem key={item.key} value={item.key}>
                              {item.label}
                            </SelectItem>
                          ))}
                        </Select>
                      </div>
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <div className="space-y-4">
                    <p className="text-sm font-medium mb-2">Marital Status (Select all that apply)</p>
                    <div className="flex flex-wrap gap-2">
                      {maritalStatusOptions.map((status) => (
                        <Checkbox
                          key={status.key}
                          isSelected={preferredMaritalStatus.includes(status.key)}
                          onValueChange={(isSelected) => {
                            if (isSelected) {
                              setPreferredMaritalStatus([...preferredMaritalStatus, status.key]);
                            } else {
                              setPreferredMaritalStatus(preferredMaritalStatus.filter(s => s !== status.key));
                            }
                          }}
                          size="sm"
                        >
                          {status.label}
                        </Checkbox>
                      ))}
                    </div>
                    
                    <p className="text-sm font-medium mb-2">Religion Preference (Select all that apply)</p>
                    <div className="flex flex-wrap gap-2">
                      {religions.map((religion) => (
                        <Checkbox
                          key={religion.key}
                          isSelected={preferredReligion.includes(religion.key)}
                          onValueChange={(isSelected) => {
                            if (isSelected) {
                              setPreferredReligion([...preferredReligion, religion.key]);
                            } else {
                              setPreferredReligion(preferredReligion.filter(r => r !== religion.key));
                            }
                          }}
                          size="sm"
                        >
                          {religion.label}
                        </Checkbox>
                      ))}
                    </div>
                    
                    <p className="text-sm font-medium mb-2">Diet Preference (Select all that apply)</p>
                    <div className="flex flex-wrap gap-2">
                      {dietOptions.map((diet) => (
                        <Checkbox
                          key={diet.key}
                          isSelected={preferredDiet.includes(diet.key)}
                          onValueChange={(isSelected) => {
                            if (isSelected) {
                              setPreferredDiet([...preferredDiet, diet.key]);
                            } else {
                              setPreferredDiet(preferredDiet.filter(d => d !== diet.key));
                            }
                          }}
                          size="sm"
                        >
                          {diet.label}
                        </Checkbox>
                      ))}
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium mb-2">Income Range</p>
                      <div className="flex gap-2 items-center">
                        <Select
                          placeholder="Min"
                          selectedKeys={incomeRangeMin ? [incomeRangeMin] : []}
                          onSelectionChange={(keys) => {
                            const selected = Array.from(keys)[0] as string;
                            setIncomeRangeMin(selected);
                          }}
                          className="w-40"
                        >
                          {incomeOptions.map((item) => (
                            <SelectItem key={item.key} value={item.key}>
                              {item.label}
                            </SelectItem>
                          ))}
                        </Select>
                        <span>to</span>
                        <Select
                          placeholder="Max"
                          selectedKeys={incomeRangeMax ? [incomeRangeMax] : []}
                          onSelectionChange={(keys) => {
                            const selected = Array.from(keys)[0] as string;
                            setIncomeRangeMax(selected);
                          }}
                          className="w-40"
                        >
                          {incomeOptions.map((item) => (
                            <SelectItem key={item.key} value={item.key}>
                              {item.label}
                            </SelectItem>
                          ))}
                        </Select>
                      </div>
                    </div>
                    
                    <Select
                      label="Manglik Status"
                      placeholder="Select manglik preference"
                      selectedKeys={manglikStatus ? [manglikStatus] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setManglikStatus(selected);
                      }}
                    >
                      <SelectItem key="yes" value="yes">Yes</SelectItem>
                      <SelectItem key="no" value="no">No</SelectItem>
                      <SelectItem key="anshik" value="anshik">Anshik</SelectItem>
                      <SelectItem key="doesnt_matter" value="doesnt_matter">Doesn't Matter</SelectItem>
                    </Select>
                  </div>
                  
                  <Textarea
                    label="What are you looking for in a partner?"
                    placeholder="Describe your ideal partner and expectations"
                    value={lookingFor}
                    onValueChange={setLookingFor}
                    minRows={3}
                  />
                </div>
              )}
              
              {activeTab === 'horoscope' && (
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold">Horoscope Details</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Select
                      label="Manglik Status"
                      placeholder="Select your manglik status"
                      selectedKeys={userManglikStatus ? [userManglikStatus] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setUserManglikStatus(selected);
                      }}
                    >
                      <SelectItem key="yes" value="yes">Yes</SelectItem>
                      <SelectItem key="no" value="no">No</SelectItem>
                      <SelectItem key="anshik" value="anshik">Anshik</SelectItem>
                      <SelectItem key="dont_know" value="dont_know">Don't Know</SelectItem>
                    </Select>
                    
                    <Input
                      type="time"
                      label="Time of Birth"
                      placeholder="Select your time of birth"
                      value={timeOfBirth}
                      onChange={(e) => setTimeOfBirth(e.target.value)}
                    />
                    
                    <Input
                      label="Place of Birth"
                      placeholder="Enter your place of birth"
                      value={placeOfBirth}
                      onValueChange={setPlaceOfBirth}
                    />
                    
                    <Select
                      label="Nakshatra"
                      placeholder="Select your nakshatra"
                      selectedKeys={nakshatra ? [nakshatra] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setNakshatra(selected);
                      }}
                    >
                      {nakshatraOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                    
                    <Select
                      label="Rashi"
                      placeholder="Select your rashi"
                      selectedKeys={rashi ? [rashi] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setRashi(selected);
                      }}
                    >
                      {rashiOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>
                  </div>
                  
                  <div>
                    <p className="text-sm font-medium mb-2">Horoscope Matching Preference</p>
                    <RadioGroup 
                      orientation="horizontal" 
                      value={horoscopeMatch ? 'yes' : 'no'} 
                      onValueChange={(value) => setHoroscopeMatch(value === 'yes')}
                    >
                      <Radio value="yes">Required</Radio>
                      <Radio value="no">Not Required</Radio>
                    </RadioGroup>
                  </div>
                  
                  <Divider />
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Upload Horoscope (Optional)</h3>
                    <p className="text-sm text-default-500">
                      You can upload your horoscope chart for better matching. Supported formats: PDF, JPG, PNG (Max size: 5MB)
                    </p>
                    
                    <div className="border-2 border-dashed border-default-300 rounded-lg p-6 text-center">
                      <Icon icon="lucide:upload-cloud" className="text-4xl text-default-400 mx-auto mb-2" />
                      <p className="text-default-600 mb-2">Drag and drop your horoscope file here</p>
                      <p className="text-default-400 text-sm mb-4">or</p>
                      <Button variant="flat" color="primary" size="sm">
                        Browse Files
                      </Button>
                    </div>
                  </div>
                  
                  <Divider />
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Kundli Chart</h3>
                    <p className="text-sm text-default-500 mb-4">
                      Your kundli chart will be generated based on your birth details. You can edit the planets in each house if needed.
                    </p>
                    
                    <div className="kundli-chart">
                      <div className="kundli-house">
                        <span className="font-semibold">House 1</span>
                        <span className="text-xs">Sun, Mercury</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 2</span>
                        <span className="text-xs">Venus</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 3</span>
                        <span className="text-xs">-</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 12</span>
                        <span className="text-xs">-</span>
                      </div>
                      <div className="kundli-house kundli-center">
                        <span className="font-semibold">Kundli</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 4</span>
                        <span className="text-xs">Mars</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 11</span>
                        <span className="text-xs">Rahu</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 10</span>
                        <span className="text-xs">Saturn</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 9</span>
                        <span className="text-xs">Jupiter</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 8</span>
                        <span className="text-xs">-</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 7</span>
                        <span className="text-xs">Moon</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 6</span>
                        <span className="text-xs">Ketu</span>
                      </div>
                      <div className="kundli-house">
                        <span className="font-semibold">House 5</span>
                        <span className="text-xs">-</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              
              <div className="flex justify-between mt-8">
                {activeTab !== 'personal' && (
                  <Button 
                    variant="flat" 
                    onPress={handlePrevious}
                    startContent={<Icon icon="lucide:arrow-left" />}
                  >
                    Previous
                  </Button>
                )}
                
                {activeTab !== 'horoscope' ? (
                  <Button 
                    color="primary" 
                    onPress={handleNext}
                    endContent={<Icon icon="lucide:arrow-right" />}
                  >
                    Next
                  </Button>
                ) : (
                  <Button 
                    color="primary"
                    onPress={handleSubmit}
                    isLoading={isLoading}
                  >
                    Complete Profile
                  </Button>
                )}
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};