// This file is missing, creating it now
import React from 'react';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Input,
  Accordion,
  AccordionItem,
  Ta<PERSON>,
  Tab,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';

export const HelpPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = React.useState('');
  const [activeTab, setActiveTab] = React.useState('faq');
  
  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };
  
  const handleSearch = () => {
    console.log('Searching for:', searchQuery);
    // Implement search logic
  };
  
  // FAQ categories
  const faqCategories = [
    {
      title: 'Account & Profile',
      faqs: [
        {
          question: 'How do I create an account?',
          answer: 'You can create an account by clicking on the "Register Free" button on the homepage. Fill in your basic details, verify your email or phone number, and you\'re good to go!'
        },
        {
          question: 'How do I edit my profile?',
          answer: 'To edit your profile, log in to your account, go to your dashboard, and click on "Edit Profile". You can update your personal information, photos, and preferences there.'
        },
        {
          question: 'How can I verify my profile?',
          answer: 'You can verify your profile by uploading your ID proof, educational certificates, and other relevant documents in the "Verification" section of your profile.'
        },
        {
          question: 'How do I delete my account?',
          answer: 'To delete your account, go to Settings > Account > Delete Account. Please note that this action is irreversible and all your data will be permanently deleted.'
        },
      ]
    },
    {
      title: 'Matching & Search',
      faqs: [
        {
          question: 'How does the matching algorithm work?',
          answer: 'Our matching algorithm considers various factors like your preferences, interests, lifestyle, and compatibility factors to suggest potential matches.'
        },
        {
          question: 'How can I search for specific profiles?',
          answer: 'You can use the advanced search feature to filter profiles based on age, height, religion, caste, education, profession, location, and many other criteria.'
        },
        {
          question: 'Why am I not getting enough matches?',
          answer: 'This could be due to very specific preferences or limited profiles matching your criteria. Try broadening your preferences to see more matches.'
        },
      ]
    },
    {
      title: 'Communication',
      faqs: [
        {
          question: 'How do I express interest in a profile?',
          answer: 'You can express interest by clicking on the "Send Interest" button on a profile. The person will be notified, and they can choose to accept or decline your interest.'
        },
        {
          question: 'How do I message someone?',
          answer: 'You can message someone after they accept your interest or if you have a premium membership. Go to the profile and click on "Message" to start a conversation.'
        },
        {
          question: 'Can I see who viewed my profile?',
          answer: 'Yes, with a premium membership, you can see who viewed your profile in the "Profile Views" section of your dashboard.'
        },
      ]
    },
    {
      title: 'Subscription & Payment',
      faqs: [
        {
          question: 'What are the benefits of a premium membership?',
          answer: 'Premium membership offers benefits like sending unlimited interests, viewing contact details, advanced search filters, priority listing in search results, and dedicated customer support.'
        },
        {
          question: 'How do I upgrade to a premium membership?',
          answer: 'You can upgrade by going to the "Subscription" page, selecting a plan that suits your needs, and completing the payment process.'
        },
        {
          question: 'What payment methods are accepted?',
          answer: 'We accept credit/debit cards, net banking, UPI, and various digital wallets for payment.'
        },
        {
          question: 'How do I cancel my subscription?',
          answer: 'You can cancel your subscription by going to Settings > Subscription > Cancel Subscription. Your premium features will remain active until the end of your billing cycle.'
        },
      ]
    },
    {
      title: 'Privacy & Security',
      faqs: [
        {
          question: 'How is my data protected?',
          answer: 'We use industry-standard encryption and security measures to protect your data. We do not share your personal information with third parties without your consent.'
        },
        {
          question: 'Can I control who sees my profile?',
          answer: 'Yes, you can control your profile visibility in the Privacy Settings. You can choose to show your profile to all members, only to premium members, or hide it completely.'
        },
        {
          question: 'How do I report a fake profile?',
          answer: 'If you suspect a profile is fake, you can report it by clicking on the "Report" button on the profile. Our team will investigate and take appropriate action.'
        },
      ]
    },
  ];
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">Help & Support</h1>
          <p className="text-default-600 max-w-2xl mx-auto mb-6">
            Find answers to common questions or get in touch with our customer support team.
          </p>
          <div className="max-w-2xl mx-auto">
            <Input
              placeholder="Search for help topics..."
              value={searchQuery}
              onValueChange={setSearchQuery}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSearch();
                }
              }}
              startContent={<Icon icon="lucide:search" className="text-default-400" />}
              endContent={
                <Button 
                  color="primary" 
                  variant="flat"
                  isIconOnly
                  onPress={handleSearch}
                >
                  <Icon icon="lucide:arrow-right" />
                </Button>
              }
            />
          </div>
        </div>
        
        <Card className="mb-12">
          <CardBody>
            <Tabs 
              aria-label="Help Options" 
              selectedKey={activeTab} 
              onSelectionChange={handleTabChange}
              className="w-full"
            >
              <Tab 
                key="faq" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:help-circle" />
                    <span>FAQs</span>
                  </div>
                }
              >
                <div className="py-4">
                  <div className="space-y-6">
                    {faqCategories.map((category, index) => (
                      <div key={index}>
                        <h3 className="text-lg font-semibold mb-3">{category.title}</h3>
                        <Accordion>
                          {category.faqs.map((faq, faqIndex) => (
                            <AccordionItem
                              key={faqIndex}
                              aria-label={faq.question}
                              title={faq.question}
                              className="text-sm"
                            >
                              <p className="text-default-600">{faq.answer}</p>
                            </AccordionItem>
                          ))}
                        </Accordion>
                      </div>
                    ))}
                  </div>
                </div>
              </Tab>
              
              <Tab 
                key="contact" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:message-circle" />
                    <span>Contact Us</span>
                  </div>
                }
              >
                <div className="py-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Get in Touch</h3>
                      <div className="space-y-4">
                        <Input
                          label="Name"
                          placeholder="Enter your name"
                        />
                        
                        <Input
                          label="Email"
                          placeholder="Enter your email"
                          type="email"
                        />
                        
                        <Input
                          label="Subject"
                          placeholder="Enter subject"
                        />
                        
                        <Input
                          label="Message"
                          placeholder="Enter your message"
                          as="textarea"
                          minRows={4}
                        />
                        
                        <Button 
                          color="primary" 
                          className="w-full"
                        >
                          Submit
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Contact Information</h3>
                      <div className="space-y-6">
                        <div>
                          <h4 className="font-medium mb-2">Customer Support</h4>
                          <div className="space-y-3">
                            <div className="flex items-center gap-2">
                              <Icon icon="lucide:phone" className="text-primary" />
                              <span>+91 1800-123-4567</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Icon icon="lucide:mail" className="text-primary" />
                              <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Icon icon="lucide:clock" className="text-primary" />
                              <span>9 AM - 9 PM (All days)</span>
                            </div>
                          </div>
                        </div>
                        
                        <Divider />
                        
                        <div>
                          <h4 className="font-medium mb-2">Head Office</h4>
                          <div className="flex items-start gap-2">
                            <Icon icon="lucide:map-pin" className="text-primary mt-1" />
                            <span>
                              BharatMatrimony Headquarters,<br />
                              123 Main Street, Andheri East,<br />
                              Mumbai, Maharashtra - 400069,<br />
                              India
                            </span>
                          </div>
                        </div>
                        
                        <Divider />
                        
                        <div>
                          <h4 className="font-medium mb-2">Connect with Us</h4>
                          <div className="flex space-x-4">
                            <Button 
                              isIconOnly 
                              variant="flat" 
                              color="default"
                              aria-label="Facebook"
                            >
                              <Icon icon="logos:facebook" />
                            </Button>
                            <Button 
                              isIconOnly 
                              variant="flat" 
                              color="default"
                              aria-label="Instagram"
                            >
                              <Icon icon="logos:instagram-icon" />
                            </Button>
                            <Button 
                              isIconOnly 
                              variant="flat" 
                              color="default"
                              aria-label="Twitter"
                            >
                              <Icon icon="logos:twitter" />
                            </Button>
                            <Button 
                              isIconOnly 
                              variant="flat" 
                              color="default"
                              aria-label="YouTube"
                            >
                              <Icon icon="logos:youtube-icon" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Tab>
              
              <Tab 
                key="safety" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:shield" />
                    <span>Safety Tips</span>
                  </div>
                }
              >
                <div className="py-4">
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Online Safety</h3>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>Never share your personal information like address, financial details, or ID proof with someone you've just met online.</li>
                        <li>Use the platform's messaging system for initial communication instead of sharing your phone number or email immediately.</li>
                        <li>Be cautious of profiles that seem too good to be true or have inconsistent information.</li>
                        <li>Report suspicious profiles or behavior to our customer support team immediately.</li>
                        <li>Be wary of users who ask for money or financial assistance, regardless of the reason.</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Meeting in Person</h3>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>Always meet in a public place for your first few meetings.</li>
                        <li>Inform a friend or family member about your meeting details, including the location and the person you're meeting.</li>
                        <li>Arrange your own transportation to and from the meeting place.</li>
                        <li>Trust your instincts. If something feels off, don't hesitate to leave.</li>
                        <li>Avoid sharing too many personal details during your first meeting.</li>
                      </ul>
                    </div>
                    
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Protecting Your Identity</h3>
                      <ul className="list-disc pl-5 space-y-2">
                        <li>Use a unique password for your matrimony account and change it regularly.</li>
                        <li>Be mindful of the information and photos you share on your profile.</li>
                        <li>Use the privacy settings to control who can view your profile and contact information.</li>
                        <li>Be cautious about clicking on links sent by other users, as they could be phishing attempts.</li>
                        <li>Log out of your account when using shared or public computers.</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </Tab>
            </Tabs>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};