const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { query, validationResult } = require('express-validator');

// In-memory storage for analytics data (in production, use proper database tables)
let profileViews = [];
let searchAppearances = [];
let analyticsIdCounter = 1;

// Helper function to generate analytics ID
const generateAnalyticsId = () => {
  return `analytics_${analyticsIdCounter++}_${Date.now()}`;
};

// Helper function to get date range
const getDateRange = (period) => {
  const endDate = new Date();
  const startDate = new Date();
  
  switch (period) {
    case '7d':
      startDate.setDate(startDate.getDate() - 7);
      break;
    case '30d':
      startDate.setDate(startDate.getDate() - 30);
      break;
    case '90d':
      startDate.setDate(startDate.getDate() - 90);
      break;
    case '1y':
      startDate.setFullYear(startDate.getFullYear() - 1);
      break;
    default:
      startDate.setDate(startDate.getDate() - 30);
  }
  
  return { startDate, endDate };
};

// Get dashboard statistics
router.get('/dashboard', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { startDate, endDate } = getDateRange('30d');

    // Get user data
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Calculate profile views in period
    const profileViewsInPeriod = profileViews.filter(view => 
      view.profileUserId === userId &&
      new Date(view.viewedAt) >= startDate &&
      new Date(view.viewedAt) <= endDate
    ).length;

    // Get interests data (from interests.js storage)
    // This would normally come from database
    const interestsSent = 15; // Mock data
    const interestsReceived = 8; // Mock data
    const mutualInterests = 3; // Mock data
    const messages = 12; // Mock data

    const stats = {
      profileViews: profileViewsInPeriod,
      interestsSent,
      interestsReceived,
      mutualInterests,
      messages,
      profileCompletionPercentage: user.profileCompletionPercentage || 0,
      membershipType: user.membershipType || 'free',
      lastActive: user.lastActive,
      joinedDate: user.createdAt
    };

    res.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dashboard statistics'
    });
  }
});

// Get profile views analytics
router.get('/profile-views', authenticateToken, [
  query('period').optional().isIn(['7d', '30d', '90d', '1y'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { period = '30d' } = req.query;
    const { startDate, endDate } = getDateRange(period);

    // Get profile views for the user in the specified period
    const userProfileViews = profileViews.filter(view => 
      view.profileUserId === userId &&
      new Date(view.viewedAt) >= startDate &&
      new Date(view.viewedAt) <= endDate
    );

    // Group by date
    const viewsByDate = {};
    userProfileViews.forEach(view => {
      const date = new Date(view.viewedAt).toISOString().split('T')[0];
      viewsByDate[date] = (viewsByDate[date] || 0) + 1;
    });

    // Convert to array format
    const chartData = Object.entries(viewsByDate).map(([date, count]) => ({
      date,
      views: count
    })).sort((a, b) => new Date(a.date) - new Date(b.date));

    // Get viewer demographics
    const viewerDemographics = {
      ageGroups: {
        '18-25': 0,
        '26-30': 0,
        '31-35': 0,
        '36-40': 0,
        '40+': 0
      },
      locations: {},
      membershipTypes: {
        free: 0,
        silver: 0,
        gold: 0,
        platinum: 0
      }
    };

    // This would normally involve joining with user data
    // For now, using mock data
    viewerDemographics.ageGroups['26-30'] = Math.floor(userProfileViews.length * 0.4);
    viewerDemographics.ageGroups['31-35'] = Math.floor(userProfileViews.length * 0.3);
    viewerDemographics.ageGroups['18-25'] = Math.floor(userProfileViews.length * 0.2);
    viewerDemographics.ageGroups['36-40'] = Math.floor(userProfileViews.length * 0.1);

    const analytics = {
      totalViews: userProfileViews.length,
      uniqueViewers: new Set(userProfileViews.map(v => v.viewerUserId)).size,
      chartData,
      viewerDemographics,
      period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    };

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get profile views analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile views analytics'
    });
  }
});

// Get interest statistics
router.get('/interests', authenticateToken, [
  query('period').optional().isIn(['7d', '30d', '90d', '1y'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { period = '30d' } = req.query;
    const { startDate, endDate } = getDateRange(period);

    // Mock interest data (in production, this would come from interests table)
    const interestStats = {
      sent: {
        total: 15,
        pending: 8,
        accepted: 4,
        declined: 3,
        successRate: 26.7
      },
      received: {
        total: 12,
        pending: 5,
        accepted: 4,
        declined: 3,
        responseRate: 58.3
      },
      mutual: 3,
      chartData: [
        { date: '2024-01-01', sent: 2, received: 1 },
        { date: '2024-01-02', sent: 1, received: 2 },
        { date: '2024-01-03', sent: 3, received: 1 },
        { date: '2024-01-04', sent: 1, received: 3 },
        { date: '2024-01-05', sent: 2, received: 2 }
      ],
      period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    };

    res.json({
      success: true,
      data: interestStats
    });

  } catch (error) {
    console.error('Get interest analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get interest analytics'
    });
  }
});

// Get search appearances analytics
router.get('/search-appearances', authenticateToken, [
  query('period').optional().isIn(['7d', '30d', '90d', '1y'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { period = '30d' } = req.query;
    const { startDate, endDate } = getDateRange(period);

    // Get search appearances for the user in the specified period
    const userSearchAppearances = searchAppearances.filter(appearance => 
      appearance.profileUserId === userId &&
      new Date(appearance.appearedAt) >= startDate &&
      new Date(appearance.appearedAt) <= endDate
    );

    // Group by date
    const appearancesByDate = {};
    userSearchAppearances.forEach(appearance => {
      const date = new Date(appearance.appearedAt).toISOString().split('T')[0];
      appearancesByDate[date] = (appearancesByDate[date] || 0) + 1;
    });

    // Convert to array format
    const chartData = Object.entries(appearancesByDate).map(([date, count]) => ({
      date,
      appearances: count
    })).sort((a, b) => new Date(a.date) - new Date(b.date));

    // Calculate search keywords/filters that led to appearances
    const searchFilters = {
      age: userSearchAppearances.filter(a => a.filters?.age).length,
      location: userSearchAppearances.filter(a => a.filters?.location).length,
      education: userSearchAppearances.filter(a => a.filters?.education).length,
      occupation: userSearchAppearances.filter(a => a.filters?.occupation).length,
      religion: userSearchAppearances.filter(a => a.filters?.religion).length
    };

    const analytics = {
      totalAppearances: userSearchAppearances.length,
      uniqueSearchers: new Set(userSearchAppearances.map(a => a.searcherUserId)).size,
      chartData,
      searchFilters,
      averagePosition: userSearchAppearances.reduce((sum, a) => sum + (a.position || 0), 0) / userSearchAppearances.length || 0,
      period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    };

    res.json({
      success: true,
      data: analytics
    });

  } catch (error) {
    console.error('Get search appearances analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get search appearances analytics'
    });
  }
});

// Record profile view (called when someone views a profile)
router.post('/profile-view', authenticateToken, async (req, res) => {
  try {
    const viewerUserId = req.user.id;
    const { profileUserId } = req.body;

    if (!profileUserId || viewerUserId === profileUserId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid profile view request'
      });
    }

    // Check if view already exists in last 24 hours (to avoid duplicate counting)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const existingView = profileViews.find(view => 
      view.viewerUserId === viewerUserId &&
      view.profileUserId === profileUserId &&
      new Date(view.viewedAt) > yesterday
    );

    if (!existingView) {
      // Record new profile view
      const profileView = {
        id: generateAnalyticsId(),
        viewerUserId,
        profileUserId,
        viewedAt: new Date().toISOString(),
        source: 'search', // Could be 'search', 'recommendation', 'interest', etc.
        deviceType: 'web' // Could be 'web', 'mobile', 'app'
      };

      profileViews.push(profileView);
    }

    res.json({
      success: true,
      message: 'Profile view recorded'
    });

  } catch (error) {
    console.error('Record profile view error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record profile view'
    });
  }
});

// Record search appearance (called when a profile appears in search results)
router.post('/search-appearance', authenticateToken, async (req, res) => {
  try {
    const searcherUserId = req.user.id;
    const { profileUserId, position, filters } = req.body;

    if (!profileUserId || searcherUserId === profileUserId) {
      return res.status(400).json({
        success: false,
        message: 'Invalid search appearance request'
      });
    }

    // Record search appearance
    const searchAppearance = {
      id: generateAnalyticsId(),
      searcherUserId,
      profileUserId,
      position: position || 0,
      filters: filters || {},
      appearedAt: new Date().toISOString(),
      searchType: 'advanced' // Could be 'basic', 'advanced', 'recommendation'
    };

    searchAppearances.push(searchAppearance);

    res.json({
      success: true,
      message: 'Search appearance recorded'
    });

  } catch (error) {
    console.error('Record search appearance error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record search appearance'
    });
  }
});

// Get profile optimization suggestions
router.get('/optimization', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user data
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const suggestions = [];

    // Check profile completion
    if (user.profileCompletionPercentage < 100) {
      suggestions.push({
        type: 'profile_completion',
        priority: 'high',
        title: 'Complete Your Profile',
        description: 'Profiles with 100% completion get 5x more views',
        action: 'Complete missing sections',
        impact: 'High'
      });
    }

    // Check photos
    const photos = user.profilePhotos || [];
    if (photos.length < 3) {
      suggestions.push({
        type: 'photos',
        priority: 'high',
        title: 'Add More Photos',
        description: 'Profiles with 3+ photos get 3x more interests',
        action: 'Upload more photos',
        impact: 'High'
      });
    }

    // Check profile views
    const recentViews = profileViews.filter(view => 
      view.profileUserId === userId &&
      new Date(view.viewedAt) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
    ).length;

    if (recentViews < 10) {
      suggestions.push({
        type: 'visibility',
        priority: 'medium',
        title: 'Improve Profile Visibility',
        description: 'Your profile had low visibility this week',
        action: 'Update your profile or consider premium membership',
        impact: 'Medium'
      });
    }

    // Check verification status
    if (!user.emailVerifiedAt || !user.phoneVerifiedAt) {
      suggestions.push({
        type: 'verification',
        priority: 'medium',
        title: 'Verify Your Profile',
        description: 'Verified profiles get 2x more trust and responses',
        action: 'Complete email and phone verification',
        impact: 'Medium'
      });
    }

    res.json({
      success: true,
      data: { suggestions }
    });

  } catch (error) {
    console.error('Get optimization suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get optimization suggestions'
    });
  }
});

module.exports = router;
