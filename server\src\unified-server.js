const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const morgan = require("morgan");
const compression = require("compression");
const rateLimit = require("express-rate-limit");
const path = require("path");
require("dotenv").config();

const app = express();

// Determine database type
const dbType = process.env.DB_TYPE || "sqlite"; // 'sqlite', 'postgres', or 'mongodb'

console.log(
  `🚀 Starting Matrimony Server with ${dbType.toUpperCase()} database`
);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: {
    success: false,
    message: "Too many requests from this IP, please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
  })
);
app.use(compression());
app.use(morgan("combined"));
app.use(limiter);
app.use(
  cors({
    origin: process.env.CLIENT_URL || "http://localhost:5173",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Serve static files
app.use("/uploads", express.static(path.join(__dirname, "../uploads")));

// Health check endpoint
app.get("/api/health", async (req, res) => {
  try {
    let dbStatus = "unknown";
    let dbInfo = {};

    // Check database connection based on type
    if (dbType === "mongodb") {
      const mongoose = require("mongoose");
      dbStatus =
        mongoose.connection.readyState === 1 ? "connected" : "disconnected";
      dbInfo = {
        type: "MongoDB",
        host: process.env.MONGODB_URI ? "configured" : "not configured",
        readyState: mongoose.connection.readyState,
      };
    } else {
      const { dbUtils } = require("./config/database");
      const isConnected = await dbUtils.isConnected();
      dbStatus = isConnected ? "connected" : "disconnected";
      dbInfo = {
        type: dbType.toUpperCase(),
        ...dbUtils.getInfo(),
      };
    }

    res.status(200).json({
      success: true,
      message: "Matrimony API is running",
      timestamp: new Date().toISOString(),
      version: "2.0.0",
      database: {
        status: dbStatus,
        type: dbType,
        info: dbInfo,
      },
      environment: process.env.NODE_ENV || "development",
    });
  } catch (error) {
    console.error("Health check error:", error);
    res.status(500).json({
      success: false,
      message: "Health check failed",
      error: error.message,
    });
  }
});

// Database initialization
const initializeDatabase = async () => {
  try {
    if (dbType === "mongodb") {
      // MongoDB initialization
      const mongoose = require("mongoose");
      await mongoose.connect(
        process.env.MONGODB_URI || "mongodb://localhost:27017/matrimony-db"
      );
      console.log("✓ Connected to MongoDB");
    } else {
      // SQL database initialization (SQLite/PostgreSQL)
      const {
        initializeDatabase,
        seedSampleData,
      } = require("./models/sequelize");

      // Initialize database
      await initializeDatabase(process.env.DB_FORCE_SYNC === "true");

      // Seed sample data if requested
      if (process.env.SEED_DATA === "true") {
        await seedSampleData();
      }
    }
  } catch (error) {
    console.error("❌ Database initialization failed:", error.message);

    if (process.env.NODE_ENV === "production") {
      process.exit(1);
    } else {
      console.log("⚠️  Continuing without database connection for development");
    }
  }
};

// Load routes based on database type
const loadRoutes = () => {
  try {
    console.log("📚 Loading API routes...");

    // Import middleware
    const errorHandler = require("./middleware/errorHandler");

    // Load routes based on database type
    const routePrefix = dbType === "mongodb" ? "./routes" : "./routes/api";

    // Authentication routes
    try {
      const authRoutes = require(`${routePrefix}/auth`);
      app.use("/api/auth", authRoutes);
      console.log("✓ Auth routes loaded");
    } catch (error) {
      console.error("✗ Auth routes failed:", error.message);
    }

    // User routes
    try {
      const userRoutes = require(`${routePrefix}/users`);
      app.use("/api/users", userRoutes);
      console.log("✓ User routes loaded");
    } catch (error) {
      console.error("✗ User routes failed:", error.message);
    }

    // Search routes
    try {
      const searchRoutes = require(`${routePrefix}/search`);
      app.use("/api/search", searchRoutes);
      console.log("✓ Search routes loaded");
    } catch (error) {
      console.error("✗ Search routes failed:", error.message);
    }

    // Interest routes
    try {
      const interestRoutes = require(`${routePrefix}/interests`);
      app.use("/api/interests", interestRoutes);
      console.log("✓ Interest routes loaded");
    } catch (error) {
      console.error("✗ Interest routes failed:", error.message);
    }

    // Message routes
    try {
      const messageRoutes = require(`${routePrefix}/messages`);
      app.use("/api/messages", messageRoutes);
      console.log("✓ Message routes loaded");
    } catch (error) {
      console.error("✗ Message routes failed:", error.message);
    }

    // Subscription routes
    try {
      const subscriptionRoutes = require(`${routePrefix}/subscriptions`);
      app.use("/api/subscription", subscriptionRoutes);
      console.log("✓ Subscription routes loaded");
    } catch (error) {
      console.error("✗ Subscription routes failed:", error.message);
    }

    // Analytics routes
    try {
      const analyticsRoutes = require(`${routePrefix}/analytics`);
      app.use("/api/analytics", analyticsRoutes);
      console.log("✓ Analytics routes loaded");
    } catch (error) {
      console.error("✗ Analytics routes failed:", error.message);
    }

    // Additional routes for advanced features
    const additionalRoutes = [
      "matching",
      "stories",
      "notifications",
      "videocalls",
    ];

    additionalRoutes.forEach((routeName) => {
      try {
        const route = require(`./routes/${routeName}`);
        app.use(`/api/${routeName}`, route);
        console.log(`✓ ${routeName} routes loaded`);
      } catch (error) {
        console.log(`⚠️  ${routeName} routes not available:`, error.message);
      }
    });

    // Error handling middleware
    app.use(errorHandler);

    // 404 handler
    app.use("*", (req, res) => {
      res.status(404).json({
        success: false,
        message: "Route not found",
        path: req.originalUrl,
      });
    });

    console.log("✅ All available routes loaded successfully");
  } catch (error) {
    console.error("❌ Error loading routes:", error);
  }
};

// Socket.IO setup for real-time features
const setupSocketIO = (server) => {
  const { Server } = require("socket.io");
  const io = new Server(server, {
    cors: {
      origin: process.env.CLIENT_URL || "http://localhost:5173",
      methods: ["GET", "POST"],
    },
  });

  // Store io instance globally for use in other modules
  global.io = io;

  io.on("connection", (socket) => {
    console.log("👤 User connected:", socket.id);

    // Join user to their personal room for notifications
    socket.on("join-user-room", (userId) => {
      socket.join(userId);
      console.log(`👤 User ${userId} joined their room`);
    });

    // Handle video call signaling
    socket.on("call-signal", (data) => {
      socket.to(data.to).emit("call-signal", {
        ...data,
        from: socket.id,
      });
    });

    // Handle chat typing indicators
    socket.on("typing", (data) => {
      socket.to(data.conversationId).emit("typing", {
        userId: data.userId,
        isTyping: data.isTyping,
      });
    });

    socket.on("disconnect", () => {
      console.log("👤 User disconnected:", socket.id);
    });
  });

  console.log("🔌 Socket.IO initialized");
  return io;
};

// Initialize server
const startServer = async () => {
  try {
    // Initialize database
    await initializeDatabase();

    // Load routes
    loadRoutes();

    // Start HTTP server
    const PORT = process.env.PORT || 5000;
    const server = app.listen(PORT, () => {
      console.log(`🚀 Matrimony server is running on port ${PORT}`);
      console.log(`📱 Environment: ${process.env.NODE_ENV || "development"}`);
      console.log(`🗄️  Database: ${dbType.toUpperCase()}`);
      console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
      console.log(`📖 API Documentation: http://localhost:${PORT}/api/docs`);
    });

    // Setup Socket.IO
    setupSocketIO(server);

    // Graceful shutdown
    process.on("SIGTERM", async () => {
      console.log("🛑 SIGTERM received, shutting down gracefully");

      server.close(() => {
        console.log("✅ HTTP server closed");

        // Close database connections
        if (dbType === "mongodb") {
          const mongoose = require("mongoose");
          mongoose.connection.close();
        } else {
          const { closeConnection } = require("./config/database");
          closeConnection();
        }

        process.exit(0);
      });
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;
