const User = require('../models/User');

class MatchingService {
  constructor() {
    this.weightings = {
      age: 0.15,
      location: 0.12,
      education: 0.10,
      occupation: 0.08,
      income: 0.08,
      religion: 0.15,
      caste: 0.10,
      lifestyle: 0.12,
      family: 0.05,
      horoscope: 0.05
    };
  }

  // Calculate compatibility score between two users
  async calculateCompatibility(user1, user2) {
    let totalScore = 0;
    let maxPossibleScore = 0;

    // Age compatibility
    const ageScore = this.calculateAgeCompatibility(user1, user2);
    totalScore += ageScore * this.weightings.age;
    maxPossibleScore += this.weightings.age;

    // Location compatibility
    const locationScore = this.calculateLocationCompatibility(user1, user2);
    totalScore += locationScore * this.weightings.location;
    maxPossibleScore += this.weightings.location;

    // Education compatibility
    const educationScore = this.calculateEducationCompatibility(user1, user2);
    totalScore += educationScore * this.weightings.education;
    maxPossibleScore += this.weightings.education;

    // Occupation compatibility
    const occupationScore = this.calculateOccupationCompatibility(user1, user2);
    totalScore += occupationScore * this.weightings.occupation;
    maxPossibleScore += this.weightings.occupation;

    // Income compatibility
    const incomeScore = this.calculateIncomeCompatibility(user1, user2);
    totalScore += incomeScore * this.weightings.income;
    maxPossibleScore += this.weightings.income;

    // Religion compatibility
    const religionScore = this.calculateReligionCompatibility(user1, user2);
    totalScore += religionScore * this.weightings.religion;
    maxPossibleScore += this.weightings.religion;

    // Caste compatibility
    const casteScore = this.calculateCasteCompatibility(user1, user2);
    totalScore += casteScore * this.weightings.caste;
    maxPossibleScore += this.weightings.caste;

    // Lifestyle compatibility
    const lifestyleScore = this.calculateLifestyleCompatibility(user1, user2);
    totalScore += lifestyleScore * this.weightings.lifestyle;
    maxPossibleScore += this.weightings.lifestyle;

    // Family compatibility
    const familyScore = this.calculateFamilyCompatibility(user1, user2);
    totalScore += familyScore * this.weightings.family;
    maxPossibleScore += this.weightings.family;

    // Horoscope compatibility (if available)
    const horoscopeScore = this.calculateHoroscopeCompatibility(user1, user2);
    totalScore += horoscopeScore * this.weightings.horoscope;
    maxPossibleScore += this.weightings.horoscope;

    const compatibilityPercentage = Math.round((totalScore / maxPossibleScore) * 100);

    return {
      overallScore: compatibilityPercentage,
      breakdown: {
        age: Math.round(ageScore * 100),
        location: Math.round(locationScore * 100),
        education: Math.round(educationScore * 100),
        occupation: Math.round(occupationScore * 100),
        income: Math.round(incomeScore * 100),
        religion: Math.round(religionScore * 100),
        caste: Math.round(casteScore * 100),
        lifestyle: Math.round(lifestyleScore * 100),
        family: Math.round(familyScore * 100),
        horoscope: Math.round(horoscopeScore * 100)
      }
    };
  }

  calculateAgeCompatibility(user1, user2) {
    const age1 = user1.personalInfo?.age || 0;
    const age2 = user2.personalInfo?.age || 0;
    
    if (!age1 || !age2) return 0;
    
    const ageDiff = Math.abs(age1 - age2);
    
    if (ageDiff <= 2) return 1.0;
    if (ageDiff <= 5) return 0.8;
    if (ageDiff <= 8) return 0.6;
    if (ageDiff <= 12) return 0.4;
    return 0.2;
  }

  calculateLocationCompatibility(user1, user2) {
    const city1 = user1.personalInfo?.city?.toLowerCase();
    const state1 = user1.personalInfo?.state?.toLowerCase();
    const country1 = user1.personalInfo?.country?.toLowerCase();
    
    const city2 = user2.personalInfo?.city?.toLowerCase();
    const state2 = user2.personalInfo?.state?.toLowerCase();
    const country2 = user2.personalInfo?.country?.toLowerCase();

    if (city1 === city2) return 1.0;
    if (state1 === state2) return 0.7;
    if (country1 === country2) return 0.5;
    return 0.2;
  }

  calculateEducationCompatibility(user1, user2) {
    const edu1 = user1.educationCareer?.highestEducation?.toLowerCase();
    const edu2 = user2.educationCareer?.highestEducation?.toLowerCase();

    if (!edu1 || !edu2) return 0.5;

    const educationLevels = {
      'phd': 7,
      'masters': 6,
      'mba': 6,
      'bachelors': 5,
      'diploma': 4,
      'intermediate': 3,
      'high school': 2,
      'primary': 1
    };

    const level1 = this.getEducationLevel(edu1, educationLevels);
    const level2 = this.getEducationLevel(edu2, educationLevels);

    const diff = Math.abs(level1 - level2);
    
    if (diff === 0) return 1.0;
    if (diff === 1) return 0.8;
    if (diff === 2) return 0.6;
    return 0.4;
  }

  getEducationLevel(education, levels) {
    for (const [key, value] of Object.entries(levels)) {
      if (education.includes(key)) return value;
    }
    return 3; // Default to intermediate level
  }

  calculateOccupationCompatibility(user1, user2) {
    const occ1 = user1.educationCareer?.occupation?.toLowerCase();
    const occ2 = user2.educationCareer?.occupation?.toLowerCase();

    if (!occ1 || !occ2) return 0.5;

    // Professional categories
    const categories = {
      'software': ['software', 'developer', 'programmer', 'engineer', 'tech'],
      'medical': ['doctor', 'nurse', 'medical', 'physician', 'surgeon'],
      'business': ['business', 'manager', 'executive', 'consultant', 'analyst'],
      'education': ['teacher', 'professor', 'education', 'academic'],
      'government': ['government', 'civil', 'officer', 'clerk'],
      'finance': ['finance', 'bank', 'accountant', 'ca', 'financial']
    };

    const cat1 = this.getOccupationCategory(occ1, categories);
    const cat2 = this.getOccupationCategory(occ2, categories);

    if (cat1 === cat2) return 1.0;
    if (cat1 && cat2) return 0.6;
    return 0.5;
  }

  getOccupationCategory(occupation, categories) {
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => occupation.includes(keyword))) {
        return category;
      }
    }
    return null;
  }

  calculateIncomeCompatibility(user1, user2) {
    const income1 = this.parseIncome(user1.educationCareer?.annualIncome);
    const income2 = this.parseIncome(user2.educationCareer?.annualIncome);

    if (!income1 || !income2) return 0.5;

    const ratio = Math.min(income1, income2) / Math.max(income1, income2);
    
    if (ratio >= 0.8) return 1.0;
    if (ratio >= 0.6) return 0.8;
    if (ratio >= 0.4) return 0.6;
    return 0.4;
  }

  parseIncome(incomeStr) {
    if (!incomeStr) return null;
    
    const income = incomeStr.toLowerCase();
    if (income.includes('lakh')) {
      const match = income.match(/(\d+)/);
      return match ? parseInt(match[1]) : null;
    }
    if (income.includes('crore')) {
      const match = income.match(/(\d+)/);
      return match ? parseInt(match[1]) * 100 : null;
    }
    return null;
  }

  calculateReligionCompatibility(user1, user2) {
    const religion1 = user1.religiousInfo?.religion;
    const religion2 = user2.religiousInfo?.religion;

    if (!religion1 || !religion2) return 0.5;
    
    return religion1 === religion2 ? 1.0 : 0.1;
  }

  calculateCasteCompatibility(user1, user2) {
    const caste1 = user1.religiousInfo?.caste?.toLowerCase();
    const caste2 = user2.religiousInfo?.caste?.toLowerCase();

    if (!caste1 || !caste2) return 0.5;
    
    if (caste1 === caste2) return 1.0;
    
    // Check if same caste category (broad matching)
    const casteCategories = {
      'brahmin': ['brahmin', 'pandit', 'sharma', 'mishra'],
      'kshatriya': ['kshatriya', 'rajput', 'thakur'],
      'vaishya': ['vaishya', 'baniya', 'agarwal', 'gupta'],
      'other': []
    };

    const cat1 = this.getCasteCategory(caste1, casteCategories);
    const cat2 = this.getCasteCategory(caste2, casteCategories);

    return cat1 === cat2 ? 0.7 : 0.3;
  }

  getCasteCategory(caste, categories) {
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => caste.includes(keyword))) {
        return category;
      }
    }
    return 'other';
  }

  calculateLifestyleCompatibility(user1, user2) {
    let score = 0;
    let factors = 0;

    // Diet compatibility
    if (user1.lifestyle?.diet && user2.lifestyle?.diet) {
      score += user1.lifestyle.diet === user2.lifestyle.diet ? 1 : 0.3;
      factors++;
    }

    // Smoking compatibility
    if (user1.lifestyle?.smoking && user2.lifestyle?.smoking) {
      score += user1.lifestyle.smoking === user2.lifestyle.smoking ? 1 : 0.5;
      factors++;
    }

    // Drinking compatibility
    if (user1.lifestyle?.drinking && user2.lifestyle?.drinking) {
      score += user1.lifestyle.drinking === user2.lifestyle.drinking ? 1 : 0.5;
      factors++;
    }

    return factors > 0 ? score / factors : 0.5;
  }

  calculateFamilyCompatibility(user1, user2) {
    let score = 0;
    let factors = 0;

    // Family type compatibility
    if (user1.familyInfo?.familyType && user2.familyInfo?.familyType) {
      score += user1.familyInfo.familyType === user2.familyInfo.familyType ? 1 : 0.6;
      factors++;
    }

    // Family values compatibility
    if (user1.familyInfo?.familyValues && user2.familyInfo?.familyValues) {
      score += user1.familyInfo.familyValues === user2.familyInfo.familyValues ? 1 : 0.5;
      factors++;
    }

    return factors > 0 ? score / factors : 0.5;
  }

  calculateHoroscopeCompatibility(user1, user2) {
    const manglik1 = user1.religiousInfo?.manglik;
    const manglik2 = user2.religiousInfo?.manglik;

    if (!manglik1 || !manglik2) return 0.5;

    if (manglik1 === 'no' && manglik2 === 'no') return 1.0;
    if (manglik1 === 'yes' && manglik2 === 'yes') return 1.0;
    if (manglik1 === 'anshik' || manglik2 === 'anshik') return 0.7;
    return 0.2;
  }

  // Get top matches for a user
  async getTopMatches(userId, limit = 20) {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      // Get potential matches based on basic criteria
      const potentialMatches = await User.find({
        _id: { $ne: userId },
        isActive: true,
        profileCompleted: true,
        blockedUsers: { $ne: userId }
      }).limit(100); // Get more candidates for better matching

      // Calculate compatibility for each potential match
      const matchesWithScores = await Promise.all(
        potentialMatches.map(async (match) => {
          const compatibility = await this.calculateCompatibility(user, match);
          return {
            user: match,
            compatibility
          };
        })
      );

      // Sort by compatibility score and return top matches
      return matchesWithScores
        .sort((a, b) => b.compatibility.overallScore - a.compatibility.overallScore)
        .slice(0, limit);

    } catch (error) {
      console.error('Error getting top matches:', error);
      throw error;
    }
  }
}

module.exports = new MatchingService();
