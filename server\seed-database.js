#!/usr/bin/env node

/**
 * Database Seeding Script
 * Seeds the database with 20 comprehensive user profiles
 */

const path = require('path');

// Set environment for development
process.env.NODE_ENV = process.env.NODE_ENV || 'development';

console.log('🌱 Starting database seeding process...');
console.log(`📍 Environment: ${process.env.NODE_ENV}`);

// Import the seeding function
const { create20Users } = require('./src/scripts/seed-20-users');

const seedDatabase = async () => {
  try {
    console.log('🔄 Initializing database connection...');
    
    // Initialize database first
    const { initializeDatabase } = require('./src/scripts/init-database');
    await initializeDatabase();
    
    console.log('✅ Database initialized successfully');
    console.log('🌱 Starting user seeding...');
    
    // Create 20 users
    await create20Users();
    
    console.log('\n🎉 Database seeding completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   ✅ 20 diverse user profiles created');
    console.log('   ✅ Multiple membership types (Free, Silver, Gold, Platinum)');
    console.log('   ✅ Various professions and locations across India');
    console.log('   ✅ Complete profile information for all sections');
    console.log('   ✅ Realistic partner preferences and compatibility data');
    
    console.log('\n🔐 Test Credentials:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: password123');
    console.log('   💎 Type: Gold Member');
    console.log('   📍 Location: Mumbai');
    
    console.log('\n🚀 Ready to test the application!');
    console.log('   🌐 Frontend: http://localhost:3000');
    console.log('   🔌 Backend: http://localhost:3001');
    
    process.exit(0);
    
  } catch (error) {
    console.error('\n💥 Database seeding failed:', error);
    console.error('\nStack trace:', error.stack);
    
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure the database is accessible');
    console.log('   2. Check your environment variables');
    console.log('   3. Ensure all dependencies are installed');
    console.log('   4. Try running: npm install');
    
    process.exit(1);
  }
};

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️  Seeding process interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️  Seeding process terminated');
  process.exit(1);
});

// Run the seeding process
seedDatabase();

module.exports = seedDatabase;
