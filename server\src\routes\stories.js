const express = require('express');
const { body, query, validationResult } = require('express-validator');
const SuccessStory = require('../models/SuccessStory');
const User = require('../models/User');
const { authenticateToken, optionalAuth, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Submit a success story
router.post('/submit', [
  authenticateToken,
  body('coupleNames').isLength({ min: 1, max: 100 }).withMessage('Couple names are required'),
  body('story').isLength({ min: 50, max: 2000 }).withMessage('Story must be between 50 and 2000 characters'),
  body('marriageDate').isISO8601().withMessage('Valid marriage date is required'),
  body('testimonial').optional().isLength({ max: 500 }).withMessage('Testimonial too long'),
  body('groomId').optional().isMongoId().withMessage('Invalid groom ID'),
  body('brideId').optional().isMongoId().withMessage('Invalid bride ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      coupleNames,
      story,
      testimonial,
      marriageDate,
      groomId,
      brideId,
      weddingLocation,
      photos,
      howTheyMet,
      timelineEvents,
      tags
    } = req.body;

    const successStory = new SuccessStory({
      coupleNames,
      story,
      testimonial,
      marriageDate,
      weddingLocation,
      photos: photos || [],
      howTheyMet: howTheyMet || 'through_platform',
      timelineEvents: timelineEvents || [],
      tags: tags || [],
      submittedBy: req.user._id,
      isPublic: false, // Requires admin approval
      isApproved: false
    });

    // Add couple references if provided
    if (groomId && brideId) {
      successStory.couple = {
        groom: groomId,
        bride: brideId
      };
    }

    await successStory.save();

    res.status(201).json({
      success: true,
      message: 'Success story submitted successfully. It will be reviewed before publishing.',
      data: {
        storyId: successStory._id,
        status: 'pending_approval'
      }
    });
  } catch (error) {
    console.error('Submit success story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit success story'
    });
  }
});

// Get public success stories
router.get('/public', [
  optionalAuth,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20'),
  query('sortBy').optional().isIn(['newest', 'popular', 'featured']).withMessage('Invalid sort option')
], async (req, res) => {
  try {
    const { page = 1, limit = 10, sortBy = 'newest' } = req.query;

    let sortOptions = {};
    switch (sortBy) {
      case 'newest':
        sortOptions = { marriageDate: -1, createdAt: -1 };
        break;
      case 'popular':
        sortOptions = { views: -1, 'likes.length': -1 };
        break;
      case 'featured':
        sortOptions = { featured: -1, marriageDate: -1 };
        break;
      default:
        sortOptions = { marriageDate: -1 };
    }

    const stories = await SuccessStory.find({
      isPublic: true,
      isApproved: true
    })
    .populate('couple.bride couple.groom', 'name personalInfo.city')
    .sort(sortOptions)
    .skip((page - 1) * limit)
    .limit(parseInt(limit))
    .select('-comments.user -likes.user'); // Don't expose user details in public API

    const totalCount = await SuccessStory.countDocuments({
      isPublic: true,
      isApproved: true
    });

    // Format stories for public consumption
    const formattedStories = stories.map(story => ({
      id: story._id,
      coupleNames: story.coupleNames,
      story: story.story,
      testimonial: story.testimonial,
      marriageDate: story.marriageDate,
      weddingLocation: story.weddingLocation,
      photos: story.photos,
      howTheyMet: story.howTheyMet,
      timelineEvents: story.timelineEvents,
      tags: story.tags,
      views: story.views,
      likeCount: story.likeCount,
      commentCount: story.commentCount,
      featured: story.featured,
      createdAt: story.createdAt
    }));

    res.json({
      success: true,
      data: {
        stories: formattedStories,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        },
        sortBy
      }
    });
  } catch (error) {
    console.error('Get public stories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get success stories'
    });
  }
});

// Get featured success stories
router.get('/featured', async (req, res) => {
  try {
    const featuredStories = await SuccessStory.getFeaturedStories(5);

    const formattedStories = featuredStories.map(story => ({
      id: story._id,
      coupleNames: story.coupleNames,
      story: story.story.substring(0, 200) + '...', // Truncate for preview
      marriageDate: story.marriageDate,
      photos: story.photos.slice(0, 1), // Only main photo
      views: story.views,
      likeCount: story.likeCount,
      featured: story.featured
    }));

    res.json({
      success: true,
      data: {
        featuredStories: formattedStories
      }
    });
  } catch (error) {
    console.error('Get featured stories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get featured stories'
    });
  }
});

// Get single success story
router.get('/:storyId', optionalAuth, async (req, res) => {
  try {
    const { storyId } = req.params;

    const story = await SuccessStory.findById(storyId)
      .populate('couple.bride couple.groom', 'name personalInfo.city')
      .populate('comments.user', 'name profilePicture');

    if (!story || !story.isPublic || !story.isApproved) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    // Increment view count
    await story.incrementViews();

    // Check if current user has liked the story
    const isLiked = req.user ? story.isLikedBy(req.user._id) : false;

    res.json({
      success: true,
      data: {
        story: {
          id: story._id,
          coupleNames: story.coupleNames,
          story: story.story,
          testimonial: story.testimonial,
          marriageDate: story.marriageDate,
          weddingLocation: story.weddingLocation,
          photos: story.photos,
          howTheyMet: story.howTheyMet,
          timelineEvents: story.timelineEvents,
          tags: story.tags,
          views: story.views,
          likeCount: story.likeCount,
          commentCount: story.commentCount,
          featured: story.featured,
          isLiked,
          comments: story.comments.filter(comment => comment.isApproved),
          createdAt: story.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Get success story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get success story'
    });
  }
});

// Like/Unlike a success story
router.post('/:storyId/like', authenticateToken, async (req, res) => {
  try {
    const { storyId } = req.params;
    const userId = req.user._id;

    const story = await SuccessStory.findById(storyId);

    if (!story || !story.isPublic || !story.isApproved) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    const isLiked = story.isLikedBy(userId);

    if (isLiked) {
      await story.removeLike(userId);
    } else {
      await story.addLike(userId);
    }

    res.json({
      success: true,
      data: {
        isLiked: !isLiked,
        likeCount: story.likeCount
      }
    });
  } catch (error) {
    console.error('Like story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update like status'
    });
  }
});

// Add comment to success story
router.post('/:storyId/comments', [
  authenticateToken,
  body('comment').isLength({ min: 1, max: 500 }).withMessage('Comment must be between 1 and 500 characters')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { storyId } = req.params;
    const { comment } = req.body;
    const userId = req.user._id;

    const story = await SuccessStory.findById(storyId);

    if (!story || !story.isPublic || !story.isApproved) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    story.comments.push({
      user: userId,
      comment,
      isApproved: false // Comments require moderation
    });

    await story.save();

    res.status(201).json({
      success: true,
      message: 'Comment submitted successfully. It will be reviewed before publishing.',
      data: {
        commentCount: story.commentCount
      }
    });
  } catch (error) {
    console.error('Add comment error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to add comment'
    });
  }
});

// Admin: Approve/Reject success story
router.put('/:storyId/approve', [
  authenticateToken,
  requireAdmin,
  body('approved').isBoolean().withMessage('Approved status must be boolean'),
  body('featured').optional().isBoolean().withMessage('Featured status must be boolean')
], async (req, res) => {
  try {
    const { storyId } = req.params;
    const { approved, featured } = req.body;

    const story = await SuccessStory.findById(storyId);

    if (!story) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    story.isApproved = approved;
    story.isPublic = approved;
    story.approvedBy = req.user._id;
    story.approvedAt = new Date();

    if (featured !== undefined) {
      story.featured = featured;
      if (featured) {
        story.featuredUntil = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days
      }
    }

    await story.save();

    res.json({
      success: true,
      message: `Success story ${approved ? 'approved' : 'rejected'} successfully`,
      data: {
        storyId: story._id,
        approved: story.isApproved,
        featured: story.featured
      }
    });
  } catch (error) {
    console.error('Approve story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update story approval status'
    });
  }
});

module.exports = router;
