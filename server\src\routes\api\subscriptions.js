const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');

// Subscription plans data
const subscriptionPlans = [
  {
    id: 'silver',
    name: 'Silver',
    type: 'silver',
    monthlyPrice: 999,
    quarterlyPrice: 2499,
    yearlyPrice: 7999,
    features: {
      profileViews: 50,
      interests: 20,
      messages: 10,
      advancedSearch: true,
      contactDetails: false,
      horoscopeMatching: false,
      prioritySupport: false,
      profileBoost: false,
      dedicatedManager: false,
      videoCall: false
    },
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'gold',
    name: 'Gold',
    type: 'gold',
    monthlyPrice: 1999,
    quarterlyPrice: 4999,
    yearlyPrice: 15999,
    features: {
      profileViews: 200,
      interests: 100,
      messages: 50,
      advancedSearch: true,
      contactDetails: true,
      horoscopeMatching: true,
      prioritySupport: true,
      profileBoost: true,
      dedicatedManager: false,
      videoCall: false
    },
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'platinum',
    name: 'Platinum',
    type: 'platinum',
    monthlyPrice: 3999,
    quarterlyPrice: 9999,
    yearlyPrice: 31999,
    features: {
      profileViews: -1, // Unlimited
      interests: -1, // Unlimited
      messages: -1, // Unlimited
      advancedSearch: true,
      contactDetails: true,
      horoscopeMatching: true,
      prioritySupport: true,
      profileBoost: true,
      dedicatedManager: true,
      videoCall: true
    },
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// In-memory storage for subscriptions (in production, use proper database table)
let userSubscriptions = [];
let subscriptionIdCounter = 1;

// Helper function to generate subscription ID
const generateSubscriptionId = () => {
  return `sub_${subscriptionIdCounter++}_${Date.now()}`;
};

// Get all subscription plans
router.get('/plans', async (req, res) => {
  try {
    res.json({
      success: true,
      data: { plans: subscriptionPlans }
    });
  } catch (error) {
    console.error('Get subscription plans error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription plans'
    });
  }
});

// Get current user subscription
router.get('/current', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Find active subscription for user
    const subscription = userSubscriptions.find(sub => 
      sub.userId === userId && 
      sub.status === 'active' && 
      new Date(sub.endDate) > new Date()
    );

    if (!subscription) {
      return res.json({
        success: true,
        data: { subscription: null }
      });
    }

    // Get plan details
    const plan = subscriptionPlans.find(p => p.id === subscription.planId);

    res.json({
      success: true,
      data: { 
        subscription: {
          ...subscription,
          plan
        }
      }
    });

  } catch (error) {
    console.error('Get current subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get current subscription'
    });
  }
});

// Create subscription
router.post('/create', authenticateToken, [
  body('planId').isIn(['silver', 'gold', 'platinum']).withMessage('Invalid plan ID'),
  body('duration').isIn(['monthly', 'quarterly', 'yearly']).withMessage('Invalid duration'),
  body('amount').isNumeric().withMessage('Amount must be a number')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { planId, duration, amount } = req.body;

    // Get plan details
    const plan = subscriptionPlans.find(p => p.id === planId);
    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Plan not found'
      });
    }

    // Validate amount
    const expectedAmount = duration === 'monthly' ? plan.monthlyPrice :
                          duration === 'quarterly' ? plan.quarterlyPrice :
                          plan.yearlyPrice;

    if (amount !== expectedAmount) {
      return res.status(400).json({
        success: false,
        message: 'Invalid amount for selected plan and duration'
      });
    }

    // Check for existing active subscription
    const existingSubscription = userSubscriptions.find(sub => 
      sub.userId === userId && 
      sub.status === 'active' && 
      new Date(sub.endDate) > new Date()
    );

    if (existingSubscription) {
      return res.status(409).json({
        success: false,
        message: 'User already has an active subscription'
      });
    }

    // Calculate subscription dates
    const startDate = new Date();
    const endDate = new Date();
    
    if (duration === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (duration === 'quarterly') {
      endDate.setMonth(endDate.getMonth() + 3);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    // Create subscription
    const subscription = {
      id: generateSubscriptionId(),
      userId,
      planId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status: 'active',
      paymentId: `payment_${Date.now()}`, // In real app, this would come from payment gateway
      paymentMethod: 'razorpay',
      amountPaid: amount,
      currency: 'INR',
      autoRenewal: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    userSubscriptions.push(subscription);

    // Update user membership type
    const user = await User.findByPk(userId);
    if (user) {
      user.membershipType = plan.type;
      user.isPremium = true;
      user.subscriptionExpiry = endDate.toISOString();
      await user.save();
    }

    res.status(201).json({
      success: true,
      message: 'Subscription created successfully',
      data: { 
        subscription: {
          ...subscription,
          plan
        }
      }
    });

  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription'
    });
  }
});

// Cancel subscription
router.post('/cancel', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Find active subscription
    const subscriptionIndex = userSubscriptions.findIndex(sub => 
      sub.userId === userId && 
      sub.status === 'active' && 
      new Date(sub.endDate) > new Date()
    );

    if (subscriptionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found'
      });
    }

    // Cancel subscription
    userSubscriptions[subscriptionIndex].status = 'cancelled';
    userSubscriptions[subscriptionIndex].updatedAt = new Date().toISOString();

    // Update user membership (keep benefits until expiry)
    const user = await User.findByPk(userId);
    if (user) {
      user.autoRenewal = false;
      await user.save();
    }

    res.json({
      success: true,
      message: 'Subscription cancelled successfully. Benefits will remain active until expiry date.'
    });

  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to cancel subscription'
    });
  }
});

// Get payment history
router.get('/payments', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Get user's subscription history
    const userPayments = userSubscriptions
      .filter(sub => sub.userId === userId)
      .map(sub => {
        const plan = subscriptionPlans.find(p => p.id === sub.planId);
        return {
          id: sub.id,
          planName: plan?.name || 'Unknown',
          amount: sub.amountPaid,
          currency: sub.currency,
          paymentMethod: sub.paymentMethod,
          paymentId: sub.paymentId,
          status: sub.status,
          startDate: sub.startDate,
          endDate: sub.endDate,
          createdAt: sub.createdAt
        };
      })
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json({
      success: true,
      data: { payments: userPayments }
    });

  } catch (error) {
    console.error('Get payment history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get payment history'
    });
  }
});

// Check subscription status and features
router.get('/features', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Find active subscription
    const subscription = userSubscriptions.find(sub => 
      sub.userId === userId && 
      sub.status === 'active' && 
      new Date(sub.endDate) > new Date()
    );

    let features = {
      profileViews: 10,
      interests: 5,
      messages: 3,
      advancedSearch: false,
      contactDetails: false,
      horoscopeMatching: false,
      prioritySupport: false,
      profileBoost: false,
      dedicatedManager: false,
      videoCall: false
    };

    if (subscription) {
      const plan = subscriptionPlans.find(p => p.id === subscription.planId);
      if (plan) {
        features = plan.features;
      }
    }

    res.json({
      success: true,
      data: { 
        features,
        hasActiveSubscription: !!subscription,
        subscriptionType: subscription ? subscription.planId : 'free'
      }
    });

  } catch (error) {
    console.error('Get subscription features error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription features'
    });
  }
});

// Upgrade subscription
router.post('/upgrade', authenticateToken, [
  body('newPlanId').isIn(['silver', 'gold', 'platinum']).withMessage('Invalid plan ID'),
  body('duration').isIn(['monthly', 'quarterly', 'yearly']).withMessage('Invalid duration')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { newPlanId, duration } = req.body;

    // Find current subscription
    const currentSubscriptionIndex = userSubscriptions.findIndex(sub => 
      sub.userId === userId && 
      sub.status === 'active' && 
      new Date(sub.endDate) > new Date()
    );

    if (currentSubscriptionIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'No active subscription found to upgrade'
      });
    }

    const currentSubscription = userSubscriptions[currentSubscriptionIndex];
    const currentPlan = subscriptionPlans.find(p => p.id === currentSubscription.planId);
    const newPlan = subscriptionPlans.find(p => p.id === newPlanId);

    if (!newPlan) {
      return res.status(404).json({
        success: false,
        message: 'New plan not found'
      });
    }

    // Check if it's actually an upgrade
    const planHierarchy = { silver: 1, gold: 2, platinum: 3 };
    if (planHierarchy[newPlanId] <= planHierarchy[currentSubscription.planId]) {
      return res.status(400).json({
        success: false,
        message: 'Can only upgrade to a higher plan'
      });
    }

    // Cancel current subscription
    userSubscriptions[currentSubscriptionIndex].status = 'upgraded';
    userSubscriptions[currentSubscriptionIndex].updatedAt = new Date().toISOString();

    // Calculate new subscription dates
    const startDate = new Date();
    const endDate = new Date();
    
    if (duration === 'monthly') {
      endDate.setMonth(endDate.getMonth() + 1);
    } else if (duration === 'quarterly') {
      endDate.setMonth(endDate.getMonth() + 3);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    // Create new subscription
    const newSubscription = {
      id: generateSubscriptionId(),
      userId,
      planId: newPlanId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status: 'active',
      paymentId: `payment_${Date.now()}`,
      paymentMethod: 'razorpay',
      amountPaid: duration === 'monthly' ? newPlan.monthlyPrice :
                  duration === 'quarterly' ? newPlan.quarterlyPrice :
                  newPlan.yearlyPrice,
      currency: 'INR',
      autoRenewal: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    userSubscriptions.push(newSubscription);

    // Update user membership type
    const user = await User.findByPk(userId);
    if (user) {
      user.membershipType = newPlan.type;
      user.subscriptionExpiry = endDate.toISOString();
      await user.save();
    }

    res.json({
      success: true,
      message: 'Subscription upgraded successfully',
      data: { 
        subscription: {
          ...newSubscription,
          plan: newPlan
        }
      }
    });

  } catch (error) {
    console.error('Upgrade subscription error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upgrade subscription'
    });
  }
});

module.exports = router;
