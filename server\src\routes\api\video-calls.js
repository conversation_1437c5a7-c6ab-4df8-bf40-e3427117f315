const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');
const { v4: uuidv4 } = require('uuid');

// In-memory storage for video calls
let videoCalls = new Map();
let callHistory = new Map();

// Generate room ID
const generateRoomId = () => {
  return `room_${uuidv4()}`;
};

// Initiate video call
router.post('/initiate', authenticateToken, [
  body('recipientId').notEmpty().withMessage('Recipient ID is required'),
  body('callType').optional().isIn(['video', 'audio']).withMessage('Invalid call type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const callerId = req.user.id;
    const { recipientId, callType = 'video' } = req.body;

    if (callerId === recipientId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot call yourself'
      });
    }

    // Check if recipient exists
    const recipient = await User.findByPk(recipientId);
    if (!recipient) {
      return res.status(404).json({
        success: false,
        message: 'Recipient not found'
      });
    }

    // Check if caller has premium membership for video calls
    const caller = await User.findByPk(callerId);
    if (!caller.isPremium && callType === 'video') {
      return res.status(403).json({
        success: false,
        message: 'Video calling requires premium membership',
        upgradeRequired: true
      });
    }

    // Check if there's an ongoing call
    const ongoingCall = Array.from(videoCalls.values()).find(call => 
      (call.callerId === callerId || call.recipientId === callerId) && 
      call.status === 'ongoing'
    );

    if (ongoingCall) {
      return res.status(409).json({
        success: false,
        message: 'You are already in a call'
      });
    }

    // Create call record
    const callId = uuidv4();
    const roomId = generateRoomId();
    
    const call = {
      id: callId,
      callerId,
      recipientId,
      callerName: caller.name,
      recipientName: recipient.name,
      callType,
      roomId,
      status: 'ringing', // ringing, accepted, declined, ended, missed
      initiatedAt: new Date().toISOString(),
      acceptedAt: null,
      endedAt: null,
      duration: 0,
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' }
      ]
    };

    videoCalls.set(callId, call);

    // In production, send real-time notification to recipient
    console.log(`Video call initiated: ${caller.name} calling ${recipient.name}`);

    res.json({
      success: true,
      message: 'Call initiated successfully',
      data: {
        callId,
        roomId,
        callType,
        recipient: {
          id: recipient.id,
          name: recipient.name,
          profilePicture: recipient.profilePicture || null
        },
        iceServers: call.iceServers
      }
    });

  } catch (error) {
    console.error('Initiate video call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate video call'
    });
  }
});

// Accept video call
router.post('/:callId/accept', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user.id;

    const call = videoCalls.get(callId);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    if (call.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to accept this call'
      });
    }

    if (call.status !== 'ringing') {
      return res.status(400).json({
        success: false,
        message: 'Call is no longer available'
      });
    }

    // Accept the call
    call.status = 'accepted';
    call.acceptedAt = new Date().toISOString();
    videoCalls.set(callId, call);

    res.json({
      success: true,
      message: 'Call accepted successfully',
      data: {
        callId,
        roomId: call.roomId,
        callType: call.callType,
        caller: {
          id: call.callerId,
          name: call.callerName
        },
        iceServers: call.iceServers
      }
    });

  } catch (error) {
    console.error('Accept video call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept video call'
    });
  }
});

// Decline video call
router.post('/:callId/decline', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user.id;

    const call = videoCalls.get(callId);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    if (call.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to decline this call'
      });
    }

    if (call.status !== 'ringing') {
      return res.status(400).json({
        success: false,
        message: 'Call is no longer available'
      });
    }

    // Decline the call
    call.status = 'declined';
    call.endedAt = new Date().toISOString();
    videoCalls.set(callId, call);

    // Add to call history
    const callerHistory = callHistory.get(call.callerId) || [];
    const recipientHistory = callHistory.get(call.recipientId) || [];
    
    callerHistory.push({ ...call, userRole: 'caller' });
    recipientHistory.push({ ...call, userRole: 'recipient' });
    
    callHistory.set(call.callerId, callerHistory);
    callHistory.set(call.recipientId, recipientHistory);

    res.json({
      success: true,
      message: 'Call declined successfully'
    });

  } catch (error) {
    console.error('Decline video call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to decline video call'
    });
  }
});

// End video call
router.post('/:callId/end', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user.id;

    const call = videoCalls.get(callId);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    if (call.callerId !== userId && call.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to end this call'
      });
    }

    // Calculate duration if call was accepted
    let duration = 0;
    if (call.acceptedAt) {
      duration = Math.floor((new Date() - new Date(call.acceptedAt)) / 1000);
    }

    // End the call
    call.status = 'ended';
    call.endedAt = new Date().toISOString();
    call.duration = duration;
    videoCalls.set(callId, call);

    // Add to call history
    const callerHistory = callHistory.get(call.callerId) || [];
    const recipientHistory = callHistory.get(call.recipientId) || [];
    
    callerHistory.push({ ...call, userRole: 'caller' });
    recipientHistory.push({ ...call, userRole: 'recipient' });
    
    callHistory.set(call.callerId, callerHistory);
    callHistory.set(call.recipientId, recipientHistory);

    res.json({
      success: true,
      message: 'Call ended successfully',
      data: {
        duration,
        endedAt: call.endedAt
      }
    });

  } catch (error) {
    console.error('End video call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to end video call'
    });
  }
});

// Get call status
router.get('/:callId/status', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user.id;

    const call = videoCalls.get(callId);
    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    if (call.callerId !== userId && call.recipientId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view this call'
      });
    }

    res.json({
      success: true,
      data: {
        callId: call.id,
        status: call.status,
        callType: call.callType,
        duration: call.duration,
        initiatedAt: call.initiatedAt,
        acceptedAt: call.acceptedAt,
        endedAt: call.endedAt
      }
    });

  } catch (error) {
    console.error('Get call status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call status'
    });
  }
});

// Get call history
router.get('/history', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { page = 1, limit = 20 } = req.query;
    
    const userHistory = callHistory.get(userId) || [];
    
    // Sort by most recent first
    userHistory.sort((a, b) => new Date(b.initiatedAt) - new Date(a.initiatedAt));
    
    // Paginate
    const offset = (page - 1) * limit;
    const paginatedHistory = userHistory.slice(offset, offset + parseInt(limit));
    
    // Format history for response
    const formattedHistory = paginatedHistory.map(call => ({
      id: call.id,
      type: call.callType,
      status: call.status,
      duration: call.duration,
      initiatedAt: call.initiatedAt,
      endedAt: call.endedAt,
      userRole: call.userRole,
      otherUser: {
        id: call.userRole === 'caller' ? call.recipientId : call.callerId,
        name: call.userRole === 'caller' ? call.recipientName : call.callerName
      }
    }));

    res.json({
      success: true,
      data: {
        calls: formattedHistory,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(userHistory.length / limit),
          totalCount: userHistory.length,
          hasNext: offset + parseInt(limit) < userHistory.length,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get call history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call history'
    });
  }
});

// Get video call settings
router.get('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    const defaultSettings = {
      allowVideoCalls: true,
      allowAudioCalls: true,
      autoAcceptFromMutualInterests: false,
      showOnlineStatus: true,
      callNotifications: true,
      callHistory: true
    };

    // In production, get from user preferences
    const settings = defaultSettings;

    res.json({
      success: true,
      data: { settings }
    });

  } catch (error) {
    console.error('Get video call settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get video call settings'
    });
  }
});

// Update video call settings
router.put('/settings', authenticateToken, [
  body('allowVideoCalls').optional().isBoolean(),
  body('allowAudioCalls').optional().isBoolean(),
  body('autoAcceptFromMutualInterests').optional().isBoolean(),
  body('showOnlineStatus').optional().isBoolean(),
  body('callNotifications').optional().isBoolean(),
  body('callHistory').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    // In production, save to user preferences
    
    res.json({
      success: true,
      message: 'Video call settings updated successfully',
      data: { settings: req.body }
    });

  } catch (error) {
    console.error('Update video call settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update video call settings'
    });
  }
});

module.exports = router;
