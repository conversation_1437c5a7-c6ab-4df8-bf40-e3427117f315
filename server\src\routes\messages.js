const express = require('express');
const { body, query, validationResult } = require('express-validator');
const { Message, Conversation } = require('../models/Message');
const User = require('../models/User');
const Interest = require('../models/Interest');
const { authenticateToken, requireCompleteProfile, checkUsageLimit } = require('../middleware/auth');

const router = express.Router();

// Get conversations
router.get('/conversations', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { page = 1, limit = 20 } = req.query;
    const userId = req.user._id;

    const conversations = await Conversation.find({
      participants: userId,
      isActive: true
    })
    .populate('participants', 'name profilePicture membershipType verificationStatus personalInfo.age personalInfo.city lastActive')
    .populate('lastMessage')
    .sort({ lastMessageTime: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

    // Format conversations for response
    const formattedConversations = conversations.map(conv => {
      const otherParticipant = conv.participants.find(p => p._id.toString() !== userId.toString());
      const unreadCount = conv.unreadCount.get(userId.toString()) || 0;

      return {
        id: conv._id,
        participant: {
          id: otherParticipant._id,
          name: otherParticipant.name,
          profilePicture: otherParticipant.profilePicture,
          membershipType: otherParticipant.membershipType,
          verificationStatus: otherParticipant.verificationStatus,
          age: otherParticipant.personalInfo?.age,
          city: otherParticipant.personalInfo?.city,
          lastActive: otherParticipant.lastActive,
          isOnline: otherParticipant.lastActive && (new Date() - otherParticipant.lastActive) < 5 * 60 * 1000 // 5 minutes
        },
        lastMessage: conv.lastMessage,
        lastMessageTime: conv.lastMessageTime,
        unreadCount,
        isActive: conv.isActive,
        isMuted: conv.mutedBy.some(m => m.user.toString() === userId.toString()),
        createdAt: conv.createdAt
      };
    });

    const totalCount = await Conversation.countDocuments({
      participants: userId,
      isActive: true
    });

    res.json({
      success: true,
      data: {
        conversations: formattedConversations,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get conversations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversations'
    });
  }
});

// Get messages in a conversation
router.get('/conversations/:conversationId/messages', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user._id;

    // Check if user is participant in conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation || !conversation.participants.includes(userId)) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view this conversation'
      });
    }

    const messages = await Message.find({
      conversationId,
      deleted: false
    })
    .populate('sender', 'name profilePicture')
    .populate('replyTo', 'content sender')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

    // Mark messages as read
    await Message.markConversationAsRead(conversationId, userId);
    await conversation.resetUnreadCount(userId);

    const totalCount = await Message.countDocuments({
      conversationId,
      deleted: false
    });

    res.json({
      success: true,
      data: {
        messages: messages.reverse(), // Reverse to show oldest first
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get messages'
    });
  }
});

// Send message
router.post('/send', [
  authenticateToken,
  requireCompleteProfile,
  checkUsageLimit('messagesSent'),
  body('receiverId').isMongoId().withMessage('Invalid receiver ID'),
  body('content').isLength({ min: 1, max: 1000 }).withMessage('Message content must be between 1 and 1000 characters'),
  body('messageType').optional().isIn(['text', 'image', 'voice', 'contact', 'location']).withMessage('Invalid message type'),
  body('replyTo').optional().isMongoId().withMessage('Invalid reply message ID')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { receiverId, content, messageType = 'text', attachmentUrl, replyTo, metadata } = req.body;
    const senderId = req.user._id;

    // Check if receiver exists and is active
    const receiver = await User.findById(receiverId);
    if (!receiver || !receiver.isActive || receiver.isBlocked) {
      return res.status(404).json({
        success: false,
        message: 'Receiver not found or not available'
      });
    }

    // Check if sender is blocked by receiver
    if (receiver.blockedUsers.includes(senderId)) {
      return res.status(403).json({
        success: false,
        message: 'You cannot send messages to this user'
      });
    }

    // Check if there's a mutual interest or if user has premium messaging
    const mutualInterest = await Interest.findOne({
      $or: [
        { from: senderId, to: receiverId, status: 'accepted' },
        { from: receiverId, to: senderId, status: 'accepted' }
      ]
    });

    const hasPremiumMessaging = req.user.membershipType !== 'free' && 
                               req.user.premiumFeatures?.directMessaging;

    if (!mutualInterest && !hasPremiumMessaging) {
      return res.status(403).json({
        success: false,
        message: 'You need mutual interest or premium membership to send messages',
        code: 'MESSAGING_RESTRICTED'
      });
    }

    // Find or create conversation
    const conversation = await Conversation.findOrCreate(senderId, receiverId);

    // Create message
    const message = new Message({
      conversationId: conversation._id,
      sender: senderId,
      receiver: receiverId,
      content,
      messageType,
      attachmentUrl,
      replyTo,
      metadata
    });

    await message.save();

    // Update conversation
    conversation.lastMessage = message._id;
    conversation.lastMessageTime = new Date();
    await conversation.incrementUnreadCount(receiverId);

    // Increment usage count
    if (req.subscription) {
      await req.subscription.incrementUsage('messagesSent');
    }

    // Populate message for response
    await message.populate('sender', 'name profilePicture');
    if (replyTo) {
      await message.populate('replyTo', 'content sender');
    }

    // Emit real-time message via Socket.IO
    const io = req.app.get('io');
    if (io) {
      io.to(receiverId.toString()).emit('receive-message', {
        message,
        conversationId: conversation._id
      });
    }

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        message,
        conversationId: conversation._id
      }
    });
  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
});

// Mark message as read
router.put('/messages/:messageId/read', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user._id;

    const message = await Message.findById(messageId);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // Check if user is the receiver
    if (message.receiver.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only mark messages sent to you as read'
      });
    }

    await message.markAsRead();

    res.json({
      success: true,
      message: 'Message marked as read'
    });
  } catch (error) {
    console.error('Mark message as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark message as read'
    });
  }
});

// Delete message
router.delete('/messages/:messageId', authenticateToken, async (req, res) => {
  try {
    const { messageId } = req.params;
    const userId = req.user._id;

    const message = await Message.findById(messageId);

    if (!message) {
      return res.status(404).json({
        success: false,
        message: 'Message not found'
      });
    }

    // Check if user is the sender
    if (message.sender.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only delete messages you sent'
      });
    }

    // Soft delete
    message.deleted = true;
    message.deletedAt = new Date();
    message.deletedBy = userId;

    await message.save();

    res.json({
      success: true,
      message: 'Message deleted successfully'
    });
  } catch (error) {
    console.error('Delete message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete message'
    });
  }
});

// Get unread message count
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const unreadCount = await Message.getUnreadCount(userId);

    res.json({
      success: true,
      data: {
        unreadCount
      }
    });
  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get unread count'
    });
  }
});

// Block/Unblock conversation
router.put('/conversations/:conversationId/block', authenticateToken, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { block = true } = req.body;
    const userId = req.user._id;

    const conversation = await Conversation.findById(conversationId);

    if (!conversation || !conversation.participants.includes(userId)) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    if (block) {
      if (!conversation.blockedBy.includes(userId)) {
        conversation.blockedBy.push(userId);
      }
    } else {
      conversation.blockedBy = conversation.blockedBy.filter(id => id.toString() !== userId.toString());
    }

    await conversation.save();

    res.json({
      success: true,
      message: `Conversation ${block ? 'blocked' : 'unblocked'} successfully`
    });
  } catch (error) {
    console.error('Block conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update conversation block status'
    });
  }
});

module.exports = router;
