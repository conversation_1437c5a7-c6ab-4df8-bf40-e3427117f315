import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Tabs,
  Tab,
  Avatar,
  Badge,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  <PERSON>dal<PERSON>ooter,
  useDisclosure,
  Textarea,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Divider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { interestAPI } from '../services/api';
import { Interest, InterestsResponse } from '../types/matrimony';

export const InterestsManagementPage: React.FC = () => {
  const { user } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [interests, setInterests] = React.useState<InterestsResponse>({
    sent: [],
    received: []
  });
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedInterest, setSelectedInterest] = React.useState<Interest | null>(null);
  const [responseMessage, setResponseMessage] = React.useState('');
  const [responding, setResponding] = React.useState(false);
  const [withdrawing, setWithdrawing] = React.useState<string | null>(null);

  // Load interests
  React.useEffect(() => {
    const loadInterests = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const response = await interestAPI.getInterests();
        
        if (response.success) {
          setInterests(response.data);
        } else {
          setError(response.message || 'Failed to load interests');
        }
      } catch (err: any) {
        console.error('Load interests error:', err);
        setError(err.response?.data?.message || 'Failed to load interests');
      } finally {
        setLoading(false);
      }
    };
    
    loadInterests();
  }, [user]);

  // Respond to interest
  const handleRespondToInterest = async (interestId: string, status: 'accepted' | 'declined') => {
    try {
      setResponding(true);
      
      const response = await interestAPI.respondToInterest(interestId, status, responseMessage);
      
      if (response.success) {
        // Update the interest in the list
        setInterests(prev => ({
          ...prev,
          received: prev.received.map(interest =>
            interest.id === interestId
              ? { ...interest, status, responseMessage, respondedAt: new Date().toISOString() }
              : interest
          )
        }));
        
        setResponseMessage('');
        onClose();
      } else {
        setError(response.message || 'Failed to respond to interest');
      }
    } catch (err: any) {
      console.error('Respond to interest error:', err);
      setError(err.response?.data?.message || 'Failed to respond to interest');
    } finally {
      setResponding(false);
    }
  };

  // Withdraw interest
  const handleWithdrawInterest = async (interestId: string) => {
    try {
      setWithdrawing(interestId);
      
      const response = await interestAPI.withdrawInterest(interestId);
      
      if (response.success) {
        // Remove the interest from the sent list
        setInterests(prev => ({
          ...prev,
          sent: prev.sent.filter(interest => interest.id !== interestId)
        }));
      } else {
        setError(response.message || 'Failed to withdraw interest');
      }
    } catch (err: any) {
      console.error('Withdraw interest error:', err);
      setError(err.response?.data?.message || 'Failed to withdraw interest');
    } finally {
      setWithdrawing(null);
    }
  };

  const openResponseModal = (interest: Interest) => {
    setSelectedInterest(interest);
    setResponseMessage('');
    onOpen();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'warning';
      case 'accepted': return 'success';
      case 'declined': return 'danger';
      case 'expired': return 'default';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return 'lucide:clock';
      case 'accepted': return 'lucide:check-circle';
      case 'declined': return 'lucide:x-circle';
      case 'expired': return 'lucide:calendar-x';
      default: return 'lucide:help-circle';
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to manage your interests.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Interests</h1>
        <p className="text-default-500">
          Manage interests you've sent and received from other members
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          color="danger"
          variant="flat"
          className="mb-6"
          startContent={<Icon icon="lucide:alert-circle" />}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <Spinner size="lg" color="primary" />
            <p className="mt-4 text-default-500">Loading interests...</p>
          </div>
        </div>
      )}

      {/* Interests Tabs */}
      {!loading && (
        <Tabs aria-label="Interests" color="primary" variant="underlined" className="mb-6">
          <Tab
            key="received"
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:heart" />
                <span>Received ({interests.received.length})</span>
              </div>
            }
          >
            <div className="space-y-4">
              {interests.received.length === 0 ? (
                <div className="text-center py-12">
                  <Icon icon="lucide:heart" size={64} className="mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">No Interests Received</h3>
                  <p className="text-default-500 mb-6">
                    When someone shows interest in your profile, it will appear here.
                  </p>
                  <Link to="/search">
                    <Button color="primary">Browse Profiles</Button>
                  </Link>
                </div>
              ) : (
                interests.received.map((interest) => (
                  <Card key={interest.id} className="hover:shadow-lg transition-shadow">
                    <CardBody>
                      <div className="flex gap-4">
                        <Avatar
                          src={interest.user?.profilePicture}
                          size="lg"
                          fallback={<Icon icon="lucide:user" size={32} />}
                        />
                        
                        <div className="flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-semibold text-lg">{interest.user?.name}</h3>
                              <p className="text-default-600">
                                {interest.user?.age} years • {interest.user?.location}
                              </p>
                              <p className="text-default-600">
                                {interest.user?.occupation} • {interest.user?.education}
                              </p>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Chip
                                size="sm"
                                color={getStatusColor(interest.status)}
                                variant="flat"
                                startContent={<Icon icon={getStatusIcon(interest.status)} size={14} />}
                              >
                                {interest.status.charAt(0).toUpperCase() + interest.status.slice(1)}
                              </Chip>
                              
                              {interest.isMutual && (
                                <Badge content="Mutual" color="success" variant="flat" />
                              )}
                            </div>
                          </div>
                          
                          {interest.message && (
                            <div className="bg-default-50 p-3 rounded-lg mb-3">
                              <p className="text-sm text-default-700">"{interest.message}"</p>
                            </div>
                          )}
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-default-500">
                              Received on {formatDate(interest.sentAt)}
                            </span>
                            
                            <div className="flex gap-2">
                              <Link to={`/profile/${interest.user?.id}`}>
                                <Button size="sm" variant="flat">
                                  View Profile
                                </Button>
                              </Link>
                              
                              {interest.status === 'pending' && (
                                <>
                                  <Button
                                    size="sm"
                                    color="success"
                                    onPress={() => openResponseModal(interest)}
                                  >
                                    Accept
                                  </Button>
                                  <Button
                                    size="sm"
                                    color="danger"
                                    variant="flat"
                                    onPress={() => {
                                      setSelectedInterest(interest);
                                      handleRespondToInterest(interest.id, 'declined');
                                    }}
                                  >
                                    Decline
                                  </Button>
                                </>
                              )}
                              
                              {interest.status === 'accepted' && (
                                <Link to={`/messages/${interest.user?.id}`}>
                                  <Button size="sm" color="primary">
                                    Message
                                  </Button>
                                </Link>
                              )}
                            </div>
                          </div>
                          
                          {interest.responseMessage && (
                            <div className="mt-3 p-3 bg-primary-50 rounded-lg">
                              <p className="text-sm text-primary-700">
                                <strong>Your response:</strong> "{interest.responseMessage}"
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))
              )}
            </div>
          </Tab>

          <Tab
            key="sent"
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:send" />
                <span>Sent ({interests.sent.length})</span>
              </div>
            }
          >
            <div className="space-y-4">
              {interests.sent.length === 0 ? (
                <div className="text-center py-12">
                  <Icon icon="lucide:send" size={64} className="mx-auto mb-4 text-default-400" />
                  <h3 className="text-xl font-semibold mb-2">No Interests Sent</h3>
                  <p className="text-default-500 mb-6">
                    Start browsing profiles and send interests to connect with potential matches.
                  </p>
                  <Link to="/search">
                    <Button color="primary">Browse Profiles</Button>
                  </Link>
                </div>
              ) : (
                interests.sent.map((interest) => (
                  <Card key={interest.id} className="hover:shadow-lg transition-shadow">
                    <CardBody>
                      <div className="flex gap-4">
                        <Avatar
                          src={interest.user?.profilePicture}
                          size="lg"
                          fallback={<Icon icon="lucide:user" size={32} />}
                        />
                        
                        <div className="flex-1">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-semibold text-lg">{interest.user?.name}</h3>
                              <p className="text-default-600">
                                {interest.user?.age} years • {interest.user?.location}
                              </p>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <Chip
                                size="sm"
                                color={getStatusColor(interest.status)}
                                variant="flat"
                                startContent={<Icon icon={getStatusIcon(interest.status)} size={14} />}
                              >
                                {interest.status.charAt(0).toUpperCase() + interest.status.slice(1)}
                              </Chip>
                              
                              {interest.isMutual && (
                                <Badge content="Mutual" color="success" variant="flat" />
                              )}
                            </div>
                          </div>
                          
                          {interest.message && (
                            <div className="bg-default-50 p-3 rounded-lg mb-3">
                              <p className="text-sm text-default-700">"{interest.message}"</p>
                            </div>
                          )}
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-default-500">
                              Sent on {formatDate(interest.sentAt)}
                              {interest.respondedAt && (
                                <> • Responded on {formatDate(interest.respondedAt)}</>
                              )}
                            </span>
                            
                            <div className="flex gap-2">
                              <Link to={`/profile/${interest.user?.id}`}>
                                <Button size="sm" variant="flat">
                                  View Profile
                                </Button>
                              </Link>
                              
                              {interest.status === 'pending' && (
                                <Button
                                  size="sm"
                                  color="danger"
                                  variant="flat"
                                  onPress={() => handleWithdrawInterest(interest.id)}
                                  isLoading={withdrawing === interest.id}
                                >
                                  Withdraw
                                </Button>
                              )}
                              
                              {interest.status === 'accepted' && (
                                <Link to={`/messages/${interest.user?.id}`}>
                                  <Button size="sm" color="primary">
                                    Message
                                  </Button>
                                </Link>
                              )}
                            </div>
                          </div>
                          
                          {interest.responseMessage && (
                            <div className="mt-3 p-3 bg-success-50 rounded-lg">
                              <p className="text-sm text-success-700">
                                <strong>Their response:</strong> "{interest.responseMessage}"
                              </p>
                            </div>
                          )}
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))
              )}
            </div>
          </Tab>
        </Tabs>
      )}

      {/* Response Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>
            Accept Interest from {selectedInterest?.user?.name}
          </ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <p className="text-default-600">
                You're about to accept the interest from {selectedInterest?.user?.name}. 
                This will allow both of you to start messaging each other.
              </p>
              
              <Textarea
                label="Response Message (Optional)"
                placeholder="Write a nice message to start the conversation..."
                value={responseMessage}
                onChange={(e) => setResponseMessage(e.target.value)}
                maxRows={3}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="success"
              onPress={() => selectedInterest && handleRespondToInterest(selectedInterest.id, 'accepted')}
              isLoading={responding}
            >
              Accept Interest
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
