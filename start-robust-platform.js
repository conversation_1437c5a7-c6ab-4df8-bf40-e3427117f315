#!/usr/bin/env node

/**
 * Robust Platform Startup Script
 * Handles all initialization, error checking, and graceful startup
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const config = {
  backend: {
    port: 3001,
    dir: './server',
    command: 'npm',
    args: ['run', 'dev']
  },
  frontend: {
    port: 3000,
    dir: './',
    command: 'npm',
    args: ['start']
  },
  healthCheck: {
    maxRetries: 30,
    interval: 2000
  }
};

// Color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = (message, color = 'reset') => {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
};

// Process management
const processes = new Map();

// Graceful shutdown handler
const gracefulShutdown = (signal) => {
  log(`\n🛑 Received ${signal}. Starting graceful shutdown...`, 'yellow');
  
  processes.forEach((process, name) => {
    log(`Stopping ${name}...`, 'yellow');
    process.kill('SIGTERM');
  });
  
  setTimeout(() => {
    log('Forcing shutdown...', 'red');
    process.exit(1);
  }, 10000);
  
  setTimeout(() => {
    log('✅ Graceful shutdown completed', 'green');
    process.exit(0);
  }, 2000);
};

process.on('SIGINT', () => gracefulShutdown('SIGINT'));
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

// Check if directory exists
const checkDirectory = (dir) => {
  if (!fs.existsSync(dir)) {
    log(`❌ Directory not found: ${dir}`, 'red');
    return false;
  }
  return true;
};

// Check if package.json exists and has required scripts
const checkPackageJson = (dir, requiredScripts = []) => {
  const packagePath = path.join(dir, 'package.json');
  
  if (!fs.existsSync(packagePath)) {
    log(`❌ package.json not found in ${dir}`, 'red');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    for (const script of requiredScripts) {
      if (!packageJson.scripts || !packageJson.scripts[script]) {
        log(`⚠️  Script '${script}' not found in ${dir}/package.json`, 'yellow');
      }
    }
    
    return true;
  } catch (error) {
    log(`❌ Invalid package.json in ${dir}: ${error.message}`, 'red');
    return false;
  }
};

// Install dependencies if needed
const installDependencies = (dir) => {
  return new Promise((resolve, reject) => {
    const nodeModulesPath = path.join(dir, 'node_modules');
    
    if (fs.existsSync(nodeModulesPath)) {
      log(`✅ Dependencies already installed in ${dir}`, 'green');
      resolve();
      return;
    }
    
    log(`📦 Installing dependencies in ${dir}...`, 'blue');
    
    const installProcess = spawn('npm', ['install'], {
      cwd: dir,
      stdio: 'pipe'
    });
    
    installProcess.stdout.on('data', (data) => {
      process.stdout.write(`${colors.blue}[NPM] ${data}${colors.reset}`);
    });
    
    installProcess.stderr.on('data', (data) => {
      process.stderr.write(`${colors.yellow}[NPM] ${data}${colors.reset}`);
    });
    
    installProcess.on('close', (code) => {
      if (code === 0) {
        log(`✅ Dependencies installed successfully in ${dir}`, 'green');
        resolve();
      } else {
        log(`❌ Failed to install dependencies in ${dir}`, 'red');
        reject(new Error(`npm install failed with code ${code}`));
      }
    });
  });
};

// Health check function
const healthCheck = async (url, maxRetries = 30, interval = 2000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await axios.get(url, { timeout: 5000 });
      return true;
    } catch (error) {
      if (i === maxRetries - 1) {
        return false;
      }
      await new Promise(resolve => setTimeout(resolve, interval));
    }
  }
  return false;
};

// Start a service
const startService = (name, config) => {
  return new Promise((resolve, reject) => {
    log(`🚀 Starting ${name}...`, 'blue');
    
    const process = spawn(config.command, config.args, {
      cwd: config.dir,
      stdio: 'pipe',
      env: { ...process.env, FORCE_COLOR: '1' }
    });
    
    processes.set(name, process);
    
    let started = false;
    let output = '';
    
    process.stdout.on('data', (data) => {
      const text = data.toString();
      output += text;
      
      // Check for startup indicators
      if (!started && (
        text.includes('Server running') ||
        text.includes('webpack compiled') ||
        text.includes('Local:') ||
        text.includes('ready') ||
        text.includes(`localhost:${config.port}`)
      )) {
        started = true;
        log(`✅ ${name} started successfully`, 'green');
        resolve();
      }
      
      // Forward output with prefix
      process.stdout.write(`${colors.cyan}[${name.toUpperCase()}] ${text}${colors.reset}`);
    });
    
    process.stderr.on('data', (data) => {
      const text = data.toString();
      
      // Don't treat warnings as errors for frontend
      if (name === 'Frontend' && (text.includes('WARNING') || text.includes('warning'))) {
        process.stdout.write(`${colors.yellow}[${name.toUpperCase()}] ${text}${colors.reset}`);
        return;
      }
      
      process.stderr.write(`${colors.red}[${name.toUpperCase()}] ${text}${colors.reset}`);
    });
    
    process.on('close', (code) => {
      processes.delete(name);
      if (code !== 0 && !started) {
        log(`❌ ${name} failed to start (exit code: ${code})`, 'red');
        reject(new Error(`${name} failed to start`));
      } else {
        log(`⚠️  ${name} stopped (exit code: ${code})`, 'yellow');
      }
    });
    
    process.on('error', (error) => {
      log(`❌ ${name} error: ${error.message}`, 'red');
      if (!started) {
        reject(error);
      }
    });
    
    // Timeout for startup
    setTimeout(() => {
      if (!started) {
        log(`⏰ ${name} startup timeout`, 'yellow');
        resolve(); // Don't fail, just continue
      }
    }, 60000); // 60 seconds timeout
  });
};

// Seed database if needed
const seedDatabase = async () => {
  try {
    log('🌱 Checking database seeding...', 'blue');
    
    // Check if users exist
    const response = await axios.get('http://localhost:3001/api/search?limit=1');
    
    if (response.data.success && response.data.data?.profiles?.length > 0) {
      log('✅ Database already seeded', 'green');
      return;
    }
    
    log('🌱 Seeding database with sample users...', 'blue');
    
    return new Promise((resolve, reject) => {
      const seedProcess = spawn('npm', ['run', 'seed'], {
        cwd: './server',
        stdio: 'pipe'
      });
      
      seedProcess.stdout.on('data', (data) => {
        process.stdout.write(`${colors.green}[SEED] ${data}${colors.reset}`);
      });
      
      seedProcess.stderr.on('data', (data) => {
        process.stderr.write(`${colors.yellow}[SEED] ${data}${colors.reset}`);
      });
      
      seedProcess.on('close', (code) => {
        if (code === 0) {
          log('✅ Database seeded successfully', 'green');
          resolve();
        } else {
          log('⚠️  Database seeding failed, continuing anyway...', 'yellow');
          resolve(); // Don't fail the startup
        }
      });
    });
    
  } catch (error) {
    log('⚠️  Could not check/seed database, continuing...', 'yellow');
  }
};

// Main startup function
const startPlatform = async () => {
  try {
    log('🚀 Starting Robust Matrimony Platform...', 'bold');
    log('=' * 60, 'blue');
    
    // Pre-flight checks
    log('🔍 Running pre-flight checks...', 'blue');
    
    // Check directories
    if (!checkDirectory(config.backend.dir)) {
      throw new Error('Backend directory not found');
    }
    
    if (!checkDirectory(config.frontend.dir)) {
      throw new Error('Frontend directory not found');
    }
    
    // Check package.json files
    checkPackageJson(config.backend.dir, ['dev', 'start', 'seed']);
    checkPackageJson(config.frontend.dir, ['start', 'build']);
    
    log('✅ Pre-flight checks completed', 'green');
    
    // Install dependencies
    log('📦 Checking dependencies...', 'blue');
    await installDependencies(config.backend.dir);
    await installDependencies(config.frontend.dir);
    
    // Start backend
    await startService('Backend', config.backend);
    
    // Wait for backend health check
    log('🏥 Waiting for backend health check...', 'blue');
    const backendHealthy = await healthCheck(
      `http://localhost:${config.backend.port}/health`,
      config.healthCheck.maxRetries,
      config.healthCheck.interval
    );
    
    if (backendHealthy) {
      log('✅ Backend is healthy', 'green');
    } else {
      log('⚠️  Backend health check failed, continuing anyway...', 'yellow');
    }
    
    // Seed database
    await seedDatabase();
    
    // Start frontend
    await startService('Frontend', config.frontend);
    
    // Final health checks
    log('🏥 Running final health checks...', 'blue');
    
    const frontendHealthy = await healthCheck(
      `http://localhost:${config.frontend.port}`,
      10,
      2000
    );
    
    // Success message
    log('\n' + '=' * 60, 'green');
    log('🎉 MATRIMONY PLATFORM STARTED SUCCESSFULLY!', 'bold');
    log('=' * 60, 'green');
    
    log('\n📍 Access Points:', 'blue');
    log(`   🌐 Frontend: http://localhost:${config.frontend.port}`, 'cyan');
    log(`   🔌 Backend API: http://localhost:${config.backend.port}`, 'cyan');
    log(`   📚 API Docs: http://localhost:${config.backend.port}/api/docs`, 'cyan');
    
    log('\n🔐 Test Credentials:', 'blue');
    log('   📧 Email: <EMAIL>', 'cyan');
    log('   🔑 Password: password123', 'cyan');
    log('   💎 Type: Gold Member', 'cyan');
    
    log('\n🧪 Testing:', 'blue');
    log('   Run: node test-all-features.js', 'cyan');
    
    log('\n⚡ Status:', 'blue');
    log(`   Backend: ${backendHealthy ? '🟢 Healthy' : '🟡 Running'}`, backendHealthy ? 'green' : 'yellow');
    log(`   Frontend: ${frontendHealthy ? '🟢 Healthy' : '🟡 Running'}`, frontendHealthy ? 'green' : 'yellow');
    
    log('\n💡 Tips:', 'blue');
    log('   • Use Ctrl+C to stop all services', 'cyan');
    log('   • Check logs above for any warnings', 'cyan');
    log('   • Visit the frontend URL to start using the platform', 'cyan');
    
    log('\n🚀 Ready to connect hearts across India! 💕', 'bold');
    
  } catch (error) {
    log(`💥 Startup failed: ${error.message}`, 'red');
    log('\n🔧 Troubleshooting:', 'yellow');
    log('   1. Make sure you have Node.js 16+ installed', 'cyan');
    log('   2. Check if ports 3000 and 3001 are available', 'cyan');
    log('   3. Run: npm install in both root and server directories', 'cyan');
    log('   4. Check the error logs above for specific issues', 'cyan');
    
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  startPlatform();
}

module.exports = { startPlatform };
