import React from 'react';
import { Link, useHistory } from 'react-router-dom';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Input, 
  Button, 
  RadioGroup, 
  Radio,
  Select,
  SelectItem,
  Checkbox
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const RegisterPage: React.FC = () => {
  const [step, setStep] = React.useState(1);
  const [profileFor, setProfileFor] = React.useState('self');
  const [name, setName] = React.useState('');
  const [gender, setGender] = React.useState('');
  const [email, setEmail] = React.useState('');
  const [phone, setPhone] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');
  const [dateOfBirth, setDateOfBirth] = React.useState('');
  const [religion, setReligion] = React.useState('');
  const [motherTongue, setMotherTongue] = React.useState('');
  const [community, setCommunity] = React.useState('');
  const [termsAccepted, setTermsAccepted] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  
  const { register } = useAuth();
  const history = useHistory();

  const validateStep1 = () => {
    if (!profileFor) {
      setError('Please select who you are creating this profile for');
      return false;
    }
    if (!name) {
      setError('Please enter your name');
      return false;
    }
    if (!gender) {
      setError('Please select your gender');
      return false;
    }
    if (!email) {
      setError('Please enter your email');
      return false;
    }
    if (!phone) {
      setError('Please enter your phone number');
      return false;
    }
    
    setError('');
    return true;
  };

  const validateStep2 = () => {
    if (!password) {
      setError('Please enter a password');
      return false;
    }
    if (password.length < 8) {
      setError('Password must be at least 8 characters long');
      return false;
    }
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }
    if (!dateOfBirth) {
      setError('Please enter your date of birth');
      return false;
    }
    
    setError('');
    return true;
  };

  const validateStep3 = () => {
    if (!religion) {
      setError('Please select your religion');
      return false;
    }
    if (!motherTongue) {
      setError('Please select your mother tongue');
      return false;
    }
    if (!community) {
      setError('Please select your community');
      return false;
    }
    if (!termsAccepted) {
      setError('Please accept the terms and conditions');
      return false;
    }
    
    setError('');
    return true;
  };

  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    } else if (step === 2 && validateStep2()) {
      setStep(3);
    }
  };

  const handlePrevStep = () => {
    if (step > 1) {
      setStep(step - 1);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateStep3()) {
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      await register(email, password, name);
      
      history.push('/profile-creation');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const religions = [
    { key: 'hindu', label: 'Hindu' },
    { key: 'muslim', label: 'Muslim' },
    { key: 'christian', label: 'Christian' },
    { key: 'sikh', label: 'Sikh' },
    { key: 'buddhist', label: 'Buddhist' },
    { key: 'jain', label: 'Jain' },
    { key: 'parsi', label: 'Parsi' },
    { key: 'jewish', label: 'Jewish' },
    { key: 'other', label: 'Other' },
  ];

  const languages = [
    { key: 'hindi', label: 'Hindi' },
    { key: 'tamil', label: 'Tamil' },
    { key: 'telugu', label: 'Telugu' },
    { key: 'kannada', label: 'Kannada' },
    { key: 'malayalam', label: 'Malayalam' },
    { key: 'marathi', label: 'Marathi' },
    { key: 'bengali', label: 'Bengali' },
    { key: 'gujarati', label: 'Gujarati' },
    { key: 'punjabi', label: 'Punjabi' },
    { key: 'urdu', label: 'Urdu' },
    { key: 'english', label: 'English' },
    { key: 'other', label: 'Other' },
  ];

  const communities = [
    { key: 'brahmin', label: 'Brahmin' },
    { key: 'kshatriya', label: 'Kshatriya' },
    { key: 'vaishya', label: 'Vaishya' },
    { key: 'shudra', label: 'Shudra' },
    { key: 'sc', label: 'SC' },
    { key: 'st', label: 'ST' },
    { key: 'obc', label: 'OBC' },
    { key: 'general', label: 'General' },
    { key: 'other', label: 'Other' },
  ];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-1 items-center">
          <h1 className="text-2xl font-bold">Create Your Account</h1>
          <p className="text-default-500 text-sm">Find your perfect match on BharatMatrimony</p>
          
          <div className="flex items-center w-full mt-6">
            <div className={`step-indicator ${step >= 1 ? 'active' : ''}`}>1</div>
            <div className={`step-line ${step >= 2 ? 'active' : ''}`}></div>
            <div className={`step-indicator ${step >= 2 ? 'active' : ''}`}>2</div>
            <div className={`step-line ${step >= 3 ? 'active' : ''}`}></div>
            <div className={`step-indicator ${step >= 3 ? 'active' : ''}`}>3</div>
          </div>
        </CardHeader>
        
        <CardBody>
          {error && (
            <div className="bg-danger-50 text-danger p-3 rounded-md mb-4 text-sm">
              {error}
            </div>
          )}
          
          <form onSubmit={handleSubmit} className="space-y-4">
            {step === 1 && (
              <>
                <div>
                  <p className="text-sm font-medium mb-2">Profile for</p>
                  <RadioGroup 
                    orientation="horizontal" 
                    value={profileFor} 
                    onValueChange={setProfileFor}
                  >
                    <Radio value="self">Myself</Radio>
                    <Radio value="son">Son</Radio>
                    <Radio value="daughter">Daughter</Radio>
                    <Radio value="relative">Relative</Radio>
                  </RadioGroup>
                </div>
                
                <Input
                  label="Full Name"
                  placeholder="Enter your full name"
                  value={name}
                  onValueChange={setName}
                  isRequired
                />
                
                <div>
                  <p className="text-sm font-medium mb-2">Gender</p>
                  <RadioGroup 
                    orientation="horizontal" 
                    value={gender} 
                    onValueChange={setGender}
                  >
                    <Radio value="male">Male</Radio>
                    <Radio value="female">Female</Radio>
                  </RadioGroup>
                </div>
                
                <Input
                  type="email"
                  label="Email"
                  placeholder="Enter your email"
                  value={email}
                  onValueChange={setEmail}
                  startContent={<Icon icon="lucide:mail" className="text-default-400" />}
                  isRequired
                />
                
                <Input
                  type="tel"
                  label="Phone Number"
                  placeholder="Enter your phone number"
                  value={phone}
                  onValueChange={setPhone}
                  startContent={<span className="text-default-400">+91</span>}
                  isRequired
                />
              </>
            )}
            
            {step === 2 && (
              <>
                <Input
                  type="password"
                  label="Password"
                  placeholder="Create a password"
                  value={password}
                  onValueChange={setPassword}
                  startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                  isRequired
                />
                
                <Input
                  type="password"
                  label="Confirm Password"
                  placeholder="Confirm your password"
                  value={confirmPassword}
                  onValueChange={setConfirmPassword}
                  startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                  isRequired
                />
                
                <Input
                  type="date"
                  label="Date of Birth"
                  placeholder="Select your date of birth"
                  value={dateOfBirth}
                  onChange={(e) => setDateOfBirth(e.target.value)}
                  isRequired
                />
                
                <p className="text-xs text-default-500 mt-2">
                  Your date of birth helps us find the most compatible matches for you.
                </p>
              </>
            )}
            
            {step === 3 && (
              <>
                <Select
                  label="Religion"
                  placeholder="Select your religion"
                  selectedKeys={religion ? [religion] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setReligion(selected);
                  }}
                  isRequired
                >
                  {religions.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  label="Mother Tongue"
                  placeholder="Select your mother tongue"
                  selectedKeys={motherTongue ? [motherTongue] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setMotherTongue(selected);
                  }}
                  isRequired
                >
                  {languages.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Select
                  label="Community"
                  placeholder="Select your community"
                  selectedKeys={community ? [community] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setCommunity(selected);
                  }}
                  isRequired
                >
                  {communities.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>
                
                <Checkbox 
                  isSelected={termsAccepted}
                  onValueChange={setTermsAccepted}
                  size="sm"
                >
                  <span className="text-sm">
                    I accept the <Link to="/terms" className="text-primary">Terms & Conditions</Link> and <Link to="/privacy-policy" className="text-primary">Privacy Policy</Link>
                  </span>
                </Checkbox>
              </>
            )}
            
            <div className="flex justify-between pt-4">
              {step > 1 ? (
                <Button 
                  variant="flat" 
                  onPress={handlePrevStep}
                  startContent={<Icon icon="lucide:arrow-left" />}
                >
                  Back
                </Button>
              ) : (
                <div></div>
              )}
              
              {step < 3 ? (
                <Button 
                  color="primary" 
                  onPress={handleNextStep}
                  endContent={<Icon icon="lucide:arrow-right" />}
                >
                  Next
                </Button>
              ) : (
                <Button 
                  type="submit" 
                  color="primary"
                  isLoading={isLoading}
                >
                  Create Account
                </Button>
              )}
            </div>
          </form>
          
          <div className="mt-6 text-center">
            <p className="text-sm text-default-500">
              Already have an account?{' '}
              <Link to="/login" className="text-primary font-medium">
                Login
              </Link>
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};