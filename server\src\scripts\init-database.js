const { sequelize } = require("../config/database");
const User = require("../models/sequelize/User");

const initializeDatabase = async () => {
  try {
    console.log("🔄 Initializing database...");

    // Test database connection
    await sequelize.authenticate();
    console.log("✅ Database connection established successfully");

    // Drop all tables if they exist (for development)
    if (process.env.NODE_ENV === "development") {
      console.log("🗑️  Dropping existing tables...");
      await sequelize.drop();
    }

    // Create tables
    console.log("📋 Creating tables...");
    await sequelize.sync({ force: process.env.NODE_ENV === "development" });

    console.log("✅ Database initialized successfully");

    // Create sample data for development
    if (process.env.NODE_ENV === "development") {
      const { create20Users } = require("./seed-20-users");
      await create20Users();
    }
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    throw error;
  }
};

const createSampleData = async () => {
  try {
    console.log("🌱 Creating comprehensive seed data with 20 users...");

    // Create 20 diverse sample users representing different Indian demographics
    const sampleUsers = [
      // User 1 - Software Engineer from Mumbai
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Arjun Sharma",
        phone: "+************",
        dateOfBirth: "1992-03-15",
        gender: "male",
        membershipType: "gold",
        personalInfo: {
          firstName: "John",
          lastName: "Doe",
          age: 33,
          height: 175,
          weight: 70,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Maharashtra",
          city: "Mumbai",
          complexion: "fair",
          bodyType: "average",
          aboutMe:
            "Software engineer looking for a life partner who shares similar values and interests.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Business",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Computer Science",
          occupation: "Software Engineer",
          designation: "Senior Developer",
          companyType: "private",
          workExperienceYears: 8,
          annualIncomeRange: "10_15_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Brahmin",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Priya Sharma",
        phone: "+************",
        dateOfBirth: "1992-08-20",
        gender: "female",
        membershipType: "gold",
        personalInfo: {
          firstName: "Priya",
          lastName: "Sharma",
          age: 31,
          height: 162,
          weight: 55,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Delhi",
          city: "New Delhi",
          complexion: "wheatish",
          bodyType: "slim",
          aboutMe:
            "Doctor by profession, looking for someone who values family and career equally.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "upper_middle_class",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Doctor",
          motherOccupation: "Teacher",
        },
        educationCareer: {
          highestEducation: "doctorate",
          educationField: "Medicine",
          occupation: "Doctor",
          designation: "Consultant",
          companyType: "private",
          workExperienceYears: 6,
          annualIncomeRange: "15_20_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Sharma",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters", "doctorate"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Rahul Patel",
        phone: "+************",
        dateOfBirth: "1988-12-10",
        gender: "male",
        membershipType: "silver",
        personalInfo: {
          firstName: "Rahul",
          lastName: "Patel",
          age: 35,
          height: 180,
          weight: 75,
          maritalStatus: "never_married",
          motherTongue: "gujarati",
          country: "India",
          state: "Gujarat",
          city: "Ahmedabad",
          complexion: "fair",
          bodyType: "athletic",
          aboutMe:
            "Business owner with traditional values, looking for a caring and understanding life partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "rich",
          familyValues: "traditional",
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 2,
          marriedSisters: 2,
          fatherOccupation: "Business",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Business Administration",
          occupation: "Business Owner",
          designation: "CEO",
          companyType: "business",
          workExperienceYears: 12,
          annualIncomeRange: "above_50_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Patel",
          manglikStatus: "anshik",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 3 months from now
      },
    ];

    for (const userData of sampleUsers) {
      try {
        const user = await User.create(userData);
        user.calculateProfileCompletion();
        await user.save();
        console.log(`✅ Created user: ${user.email}`);
      } catch (error) {
        console.error(
          `❌ Failed to create user ${userData.email}:`,
          error.message
        );
      }
    }

    console.log("✅ Sample data created successfully");
  } catch (error) {
    console.error("❌ Failed to create sample data:", error);
  }
};

// Run if called directly
if (require.main === module) {
  initializeDatabase()
    .then(() => {
      console.log("🎉 Database initialization completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Database initialization failed:", error);
      process.exit(1);
    });
}

module.exports = { initializeDatabase, createSampleData };
