export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  profileCompleted: boolean;
  profileType: 'self' | 'son' | 'daughter' | 'brother' | 'sister' | 'relative' | 'friend';
  membershipType: 'free' | 'silver' | 'gold' | 'platinum';
  profilePicture: string;
  verificationStatus: {
    email: boolean;
    phone: boolean;
    photo: boolean;
    document: boolean;
  };
  createdAt: string;
  personalInfo?: PersonalInfo;
  familyInfo?: FamilyInfo;
  educationCareer?: EducationCareer;
  lifestyle?: Lifestyle;
  partnerPreferences?: PartnerPreferences;
  horoscope?: Horoscope;
}

export interface PersonalInfo {
  firstName: string;
  lastName: string;
  gender: 'male' | 'female' | 'other';
  dateOfBirth: string;
  age: number;
  height: number; // in cm
  weight: number; // in kg
  maritalStatus: 'never_married' | 'divorced' | 'widowed' | 'awaiting_divorce';
  physicalStatus: 'normal' | 'physically_challenged';
  healthInformation?: string;
  complexion: 'fair' | 'wheatish' | 'wheatish_brown' | 'brown' | 'dark';
  bodyType: 'slim' | 'athletic' | 'average' | 'heavy';
  aboutMe: string;
  location: {
    country: string;
    state: string;
    city: string;
    willingToRelocate: boolean;
  };
  religion: string;
  caste: string;
  subCaste?: string;
  gothra?: string;
  motherTongue: string;
  knownLanguages: string[];
  photos: string[];
}

export interface FamilyInfo {
  familyType: 'nuclear' | 'joint' | 'others';
  familyStatus: 'middle_class' | 'upper_middle_class' | 'rich' | 'affluent';
  familyValues: 'orthodox' | 'traditional' | 'moderate' | 'liberal';
  fatherOccupation: string;
  motherOccupation: string;
  fatherStatus: 'alive' | 'deceased';
  motherStatus: 'alive' | 'deceased';
  brothers: number;
  sisters: number;
  marriedBrothers: number;
  marriedSisters: number;
  familyIncome: string;
  familyBasedOut: string;
  aboutFamily: string;
}

export interface EducationCareer {
  highestEducation: string;
  educationField: string;
  university: string;
  yearOfPassing?: string;
  otherQualifications?: string;
  occupation: string;
  employedIn: 'government' | 'private' | 'business' | 'self_employed' | 'not_working';
  companyName?: string;
  designation?: string;
  workLocation?: string;
  annualIncome: string;
  workExperience?: number; // in years
  careerDescription?: string;
}

export interface Lifestyle {
  diet: 'vegetarian' | 'non_vegetarian' | 'eggetarian' | 'vegan' | 'jain';
  drinking: 'no' | 'occasionally' | 'yes';
  smoking: 'no' | 'occasionally' | 'yes';
  hobbies: string[];
  interests: string[];
  musicPreferences?: string[];
  moviePreferences?: string[];
  sportsPreferences?: string[];
  dressStyle?: 'traditional' | 'modern' | 'mixed';
}

export interface PartnerPreferences {
  ageRange: {
    min: number;
    max: number;
  };
  heightRange: {
    min: number; // in cm
    max: number; // in cm
  };
  maritalStatus: ('never_married' | 'divorced' | 'widowed' | 'awaiting_divorce')[];
  religion: string[];
  caste: string[];
  subCaste?: string[];
  education: string[];
  occupation: string[];
  incomeRange: {
    min: number;
    max: number;
  };
  dietPreference?: ('vegetarian' | 'non_vegetarian' | 'eggetarian' | 'vegan' | 'jain')[];
  drinkingPreference?: ('no' | 'occasionally' | 'yes')[];
  smokingPreference?: ('no' | 'occasionally' | 'yes')[];
  locationPreference: {
    countries: string[];
    states: string[];
    cities: string[];
  };
  manglikStatus?: 'yes' | 'no' | 'anshik' | 'doesnt_matter';
  lookingFor: string;
}

export interface Horoscope {
  manglikStatus: 'yes' | 'no' | 'anshik' | 'dont_know';
  timeOfBirth?: string;
  placeOfBirth?: string;
  nakshatra?: string;
  rashi?: string;
  horoscopeMatch?: boolean;
  horoscopeFile?: string; // URL to uploaded horoscope file
  houses?: Record<string, string[]>; // Houses and planets in them
}

export interface ProfileMatch {
  id: string;
  userId: string;
  name: string;
  age: number;
  location: string;
  profession: string;
  education: string;
  photo: string;
  compatibilityScore: number;
  isPremium: boolean;
  isVerified: boolean;
  lastActive: string;
  shortlisted: boolean;
  interestSent: boolean;
  interestReceived: boolean;
  viewed: boolean;
}

export interface Interest {
  id: string;
  from: string;
  to: string;
  status: 'pending' | 'accepted' | 'declined';
  message?: string;
  createdAt: string;
  isPremium: boolean;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  receiverId: string;
  content: string;
  attachmentUrl?: string;
  attachmentType?: 'image' | 'voice';
  read: boolean;
  createdAt: string;
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  profilePicture: string;
  name: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  duration: number; // in months
  features: string[];
  popular: boolean;
}

export interface SuccessStory {
  id: string;
  coupleNames: string;
  marriageDate: string;
  story: string;
  photos: string[];
  testimonial: string;
  location: string;
  createdAt: string;
}
