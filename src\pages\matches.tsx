// This file is missing, creating it now
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  Button, 
  Tabs, 
  Tab, 
  Avatar, 
  Divider,
  Chip,
  Badge
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const MatchesPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = React.useState('new');
  
  // Mock data for matches
  const newMatches = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON>',
      age: 28,
      location: 'Mumbai, Maharashtra',
      profession: 'Software Engineer',
      education: 'B.Tech, Computer Science',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
      compatibilityScore: 85,
      isPremium: true,
      isVerified: true,
      lastActive: '2023-06-15T10:30:00Z',
    },
    {
      id: '2',
      name: '<PERSON><PERSON><PERSON>',
      age: 26,
      location: 'Bangalore, Karnataka',
      profession: 'Data Scientist',
      education: 'M.Sc, Statistics',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=3',
      compatibilityScore: 78,
      isPremium: false,
      isVerified: true,
      lastActive: '2023-06-14T14:20:00Z',
    },
    {
      id: '3',
      name: 'Pooja Verma',
      age: 27,
      location: 'Delhi, NCR',
      profession: 'Marketing Manager',
      education: 'MBA, Marketing',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=4',
      compatibilityScore: 72,
      isPremium: true,
      isVerified: true,
      lastActive: '2023-06-15T09:15:00Z',
    },
  ];
  
  const recentlyViewed = [
    {
      id: '4',
      name: 'Kavita Singh',
      age: 29,
      location: 'Pune, Maharashtra',
      profession: 'HR Manager',
      education: 'MBA, Human Resources',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=5',
      compatibilityScore: 68,
      isPremium: false,
      isVerified: true,
      lastActive: '2023-06-15T11:45:00Z',
    },
    {
      id: '5',
      name: 'Meera Reddy',
      age: 25,
      location: 'Hyderabad, Telangana',
      profession: 'Architect',
      education: 'B.Arch',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=6',
      compatibilityScore: 75,
      isPremium: true,
      isVerified: true,
      lastActive: '2023-06-14T16:30:00Z',
    },
  ];
  
  const interestReceived = [
    {
      id: '6',
      name: 'Priya Malhotra',
      age: 27,
      location: 'Chennai, Tamil Nadu',
      profession: 'Doctor',
      education: 'MBBS, MD',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=7',
      compatibilityScore: 82,
      isPremium: true,
      isVerified: true,
      lastActive: '2023-06-15T08:20:00Z',
      interestDate: '2023-06-14T08:20:00Z',
    },
  ];
  
  const interestSent = [
    {
      id: '7',
      name: 'Divya Gupta',
      age: 26,
      location: 'Kolkata, West Bengal',
      profession: 'Chartered Accountant',
      education: 'B.Com, CA',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=8',
      compatibilityScore: 70,
      isPremium: false,
      isVerified: true,
      lastActive: '2023-06-14T19:10:00Z',
      interestDate: '2023-06-13T19:10:00Z',
      accepted: false,
    },
    {
      id: '8',
      name: 'Ritu Desai',
      age: 28,
      location: 'Ahmedabad, Gujarat',
      profession: 'Teacher',
      education: 'B.Ed, M.Ed',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=9',
      compatibilityScore: 65,
      isPremium: false,
      isVerified: true,
      lastActive: '2023-06-15T07:45:00Z',
      interestDate: '2023-06-12T07:45:00Z',
      accepted: true,
    },
  ];
  
  const shortlisted = [
    {
      id: '9',
      name: 'Sneha Kapoor',
      age: 27,
      location: 'Jaipur, Rajasthan',
      profession: 'Fashion Designer',
      education: 'B.Des',
      photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=10',
      compatibilityScore: 79,
      isPremium: true,
      isVerified: true,
      lastActive: '2023-06-15T12:30:00Z',
    },
  ];
  
  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };
  
  const handleSendInterest = (id: string) => {
    console.log(`Sending interest to profile ${id}`);
  };
  
  const handleAcceptInterest = (id: string) => {
    console.log(`Accepting interest from profile ${id}`);
  };
  
  const handleDeclineInterest = (id: string) => {
    console.log(`Declining interest from profile ${id}`);
  };
  
  const handleShortlist = (id: string) => {
    console.log(`Shortlisting profile ${id}`);
  };
  
  const renderProfileCard = (profile: any, type: string) => {
    return (
      <Card key={profile.id} className="mb-4">
        <CardBody className="p-0">
          <div className="flex flex-col md:flex-row">
            <div className="md:w-1/4 relative">
              <img 
                src={profile.photo} 
                alt={profile.name} 
                className="w-full h-full object-cover aspect-[3/4] md:aspect-auto"
              />
              {profile.isPremium && (
                <div className="absolute top-2 left-2">
                  <Chip color="warning" variant="flat" size="sm" startContent={<Icon icon="lucide:crown" className="text-warning" />}>
                    Premium
                  </Chip>
                </div>
              )}
              {profile.isVerified && (
                <div className="absolute top-2 right-2">
                  <Chip color="success" variant="flat" size="sm" startContent={<Icon icon="lucide:check-circle" className="text-success" />}>
                    Verified
                  </Chip>
                </div>
              )}
            </div>
            
            <div className="md:w-3/4 p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold">{profile.name}</h3>
                  <p className="text-default-500 text-sm">{profile.age} yrs, {profile.location}</p>
                </div>
                <Chip color="primary" size="sm" variant="flat">
                  {profile.compatibilityScore}% Match
                </Chip>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:briefcase" className="text-default-500" />
                  <span className="text-sm">{profile.profession}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:graduation-cap" className="text-default-500" />
                  <span className="text-sm">{profile.education}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:clock" className="text-default-500" />
                  <span className="text-sm">
                    Last active: {new Date(profile.lastActive).toLocaleDateString()}
                  </span>
                </div>
                
                {profile.interestDate && (
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:heart" className="text-default-500" />
                    <span className="text-sm">
                      Interest {type === 'received' ? 'received' : 'sent'}: {new Date(profile.interestDate).toLocaleDateString()}
                    </span>
                  </div>
                )}
              </div>
              
              <Divider className="my-4" />
              
              <div className="flex flex-wrap gap-3">
                <Link to={`/profile/${profile.id}`}>
                  <Button 
                    variant="flat" 
                    color="default"
                    startContent={<Icon icon="lucide:user" />}
                  >
                    View Profile
                  </Button>
                </Link>
                
                {type === 'received' && (
                  <>
                    <Button 
                      color="success"
                      startContent={<Icon icon="lucide:check" />}
                      onPress={() => handleAcceptInterest(profile.id)}
                    >
                      Accept
                    </Button>
                    <Button 
                      color="danger"
                      variant="flat"
                      startContent={<Icon icon="lucide:x" />}
                      onPress={() => handleDeclineInterest(profile.id)}
                    >
                      Decline
                    </Button>
                  </>
                )}
                
                {type === 'sent' && (
                  <Button 
                    color={profile.accepted ? "success" : "primary"}
                    variant="flat"
                    startContent={<Icon icon={profile.accepted ? "lucide:check" : "lucide:clock"} />}
                    isDisabled
                  >
                    {profile.accepted ? 'Accepted' : 'Pending'}
                  </Button>
                )}
                
                {(type === 'new' || type === 'viewed' || type === 'shortlisted') && (
                  <Button 
                    color="primary"
                    startContent={<Icon icon="lucide:heart" />}
                    onPress={() => handleSendInterest(profile.id)}
                  >
                    Send Interest
                  </Button>
                )}
                
                {type !== 'shortlisted' && (
                  <Button 
                    variant="light" 
                    color="default"
                    startContent={<Icon icon="lucide:star" />}
                    onPress={() => handleShortlist(profile.id)}
                  >
                    Shortlist
                  </Button>
                )}
                
                {type === 'shortlisted' && (
                  <Button 
                    variant="light" 
                    color="warning"
                    startContent={<Icon icon="lucide:star" />}
                    onPress={() => handleShortlist(profile.id)}
                  >
                    Remove
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardBody>
      </Card>
    );
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">My Matches</h1>
        
        <Tabs 
          aria-label="Match Categories" 
          selectedKey={activeTab} 
          onSelectionChange={handleTabChange}
          className="mb-6"
          color="primary"
        >
          <Tab 
            key="new" 
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:sparkles" />
                <span>New Matches</span>
                <Chip size="sm" variant="flat" color="primary">{newMatches.length}</Chip>
              </div>
            }
          >
            <div className="py-4">
              {newMatches.length > 0 ? (
                newMatches.map(profile => renderProfileCard(profile, 'new'))
              ) : (
                <div className="text-center py-8">
                  <Icon icon="lucide:search" className="text-4xl text-default-300 mx-auto mb-2" />
                  <p className="text-default-500">No new matches found</p>
                </div>
              )}
            </div>
          </Tab>
          
          <Tab 
            key="received" 
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:inbox" />
                <span>Interest Received</span>
                <Chip size="sm" variant="flat" color="primary">{interestReceived.length}</Chip>
              </div>
            }
          >
            <div className="py-4">
              {interestReceived.length > 0 ? (
                interestReceived.map(profile => renderProfileCard(profile, 'received'))
              ) : (
                <div className="text-center py-8">
                  <Icon icon="lucide:inbox" className="text-4xl text-default-300 mx-auto mb-2" />
                  <p className="text-default-500">No interests received yet</p>
                </div>
              )}
            </div>
          </Tab>
          
          <Tab 
            key="sent" 
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:send" />
                <span>Interest Sent</span>
                <Chip size="sm" variant="flat" color="primary">{interestSent.length}</Chip>
              </div>
            }
          >
            <div className="py-4">
              {interestSent.length > 0 ? (
                interestSent.map(profile => renderProfileCard(profile, 'sent'))
              ) : (
                <div className="text-center py-8">
                  <Icon icon="lucide:send" className="text-4xl text-default-300 mx-auto mb-2" />
                  <p className="text-default-500">You haven't sent any interests yet</p>
                </div>
              )}
            </div>
          </Tab>
          
          <Tab 
            key="viewed" 
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:eye" />
                <span>Recently Viewed</span>
                <Chip size="sm" variant="flat" color="primary">{recentlyViewed.length}</Chip>
              </div>
            }
          >
            <div className="py-4">
              {recentlyViewed.length > 0 ? (
                recentlyViewed.map(profile => renderProfileCard(profile, 'viewed'))
              ) : (
                <div className="text-center py-8">
                  <Icon icon="lucide:eye" className="text-4xl text-default-300 mx-auto mb-2" />
                  <p className="text-default-500">You haven't viewed any profiles yet</p>
                </div>
              )}
            </div>
          </Tab>
          
          <Tab 
            key="shortlisted" 
            title={
              <div className="flex items-center gap-2">
                <Icon icon="lucide:star" />
                <span>Shortlisted</span>
                <Chip size="sm" variant="flat" color="primary">{shortlisted.length}</Chip>
              </div>
            }
          >
            <div className="py-4">
              {shortlisted.length > 0 ? (
                shortlisted.map(profile => renderProfileCard(profile, 'shortlisted'))
              ) : (
                <div className="text-center py-8">
                  <Icon icon="lucide:star" className="text-4xl text-default-300 mx-auto mb-2" />
                  <p className="text-default-500">You haven't shortlisted any profiles yet</p>
                </div>
              )}
            </div>
          </Tab>
        </Tabs>
      </div>
    </div>
  );
};