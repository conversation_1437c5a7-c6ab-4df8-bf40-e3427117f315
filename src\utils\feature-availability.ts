// Feature availability checker and fallback manager

export interface FeatureStatus {
  available: boolean;
  reason?: string;
  fallbackData?: any;
  estimatedAvailability?: string;
}

export interface FeatureConfig {
  name: string;
  endpoint?: string;
  dependencies?: string[];
  fallbackComponent?: React.ComponentType<any>;
  fallbackData?: any;
}

// Feature registry
const FEATURES: Record<string, FeatureConfig> = {
  analytics: {
    name: 'Analytics Dashboard',
    endpoint: '/api/analytics/dashboard',
    fallbackData: {
      profileViews: 0,
      interestsSent: 0,
      interestsReceived: 0,
      mutualInterests: 0,
      messages: 0
    }
  },
  horoscope: {
    name: 'Horoscope Matching',
    endpoint: '/api/horoscope',
    fallbackData: {
      horoscopeInfo: {
        birthTime: '',
        birthPlace: '',
        rashi: '',
        nakshatra: '',
        manglikStatus: 'dont_know'
      }
    }
  },
  notifications: {
    name: 'Notifications',
    endpoint: '/api/notifications',
    fallbackData: {
      notifications: [],
      unreadCount: 0
    }
  },
  successStories: {
    name: 'Success Stories',
    endpoint: '/api/success-stories',
    fallbackData: {
      stories: [
        {
          id: 'sample-1',
          groomName: 'A<PERSON>jun',
          brideName: 'Priya',
          groomLocation: 'Mumbai',
          brideLocation: 'Delhi',
          marriageDate: '2024-02-14',
          story: 'We found each other through this amazing platform and are now happily married!',
          likes: 45,
          views: 234
        }
      ]
    }
  },
  videoCall: {
    name: 'Video Calling',
    endpoint: '/api/video-calls',
    dependencies: ['webrtc'],
    fallbackData: null
  },
  advancedSearch: {
    name: 'Advanced Search',
    endpoint: '/api/search/advanced',
    fallbackData: {
      profiles: [],
      filters: []
    }
  },
  messaging: {
    name: 'Real-time Messaging',
    endpoint: '/api/messages',
    dependencies: ['websocket'],
    fallbackData: {
      conversations: [],
      messages: []
    }
  },
  premiumFeatures: {
    name: 'Premium Features',
    endpoint: '/api/subscription/features',
    fallbackData: {
      features: {
        profileViews: 10,
        interests: 5,
        messages: 3,
        advancedSearch: false,
        contactDetails: false,
        horoscopeMatching: false
      }
    }
  }
};

// Feature availability cache
const featureCache = new Map<string, { status: FeatureStatus; timestamp: number }>();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Check if feature is available
export const checkFeatureAvailability = async (featureName: string): Promise<FeatureStatus> => {
  // Check cache first
  const cached = featureCache.get(featureName);
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.status;
  }

  const feature = FEATURES[featureName];
  if (!feature) {
    const status: FeatureStatus = {
      available: false,
      reason: 'Feature not found in registry'
    };
    featureCache.set(featureName, { status, timestamp: Date.now() });
    return status;
  }

  try {
    // Check endpoint availability
    if (feature.endpoint) {
      const response = await fetch(`${process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001'}${feature.endpoint}`, {
        method: 'HEAD',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
        }
      });

      if (response.ok || response.status === 401) { // 401 means endpoint exists but needs auth
        const status: FeatureStatus = {
          available: true,
          fallbackData: feature.fallbackData
        };
        featureCache.set(featureName, { status, timestamp: Date.now() });
        return status;
      }
    }

    // Check dependencies
    if (feature.dependencies) {
      for (const dep of feature.dependencies) {
        if (!checkDependency(dep)) {
          const status: FeatureStatus = {
            available: false,
            reason: `Missing dependency: ${dep}`,
            fallbackData: feature.fallbackData,
            estimatedAvailability: 'Next update'
          };
          featureCache.set(featureName, { status, timestamp: Date.now() });
          return status;
        }
      }
    }

    // Feature not available
    const status: FeatureStatus = {
      available: false,
      reason: 'Feature under development',
      fallbackData: feature.fallbackData,
      estimatedAvailability: 'Coming soon'
    };
    featureCache.set(featureName, { status, timestamp: Date.now() });
    return status;

  } catch (error) {
    console.warn(`Feature availability check failed for ${featureName}:`, error);
    const status: FeatureStatus = {
      available: false,
      reason: 'Network error',
      fallbackData: feature.fallbackData
    };
    featureCache.set(featureName, { status, timestamp: Date.now() });
    return status;
  }
};

// Check system dependencies
const checkDependency = (dependency: string): boolean => {
  switch (dependency) {
    case 'webrtc':
      return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    case 'websocket':
      return typeof WebSocket !== 'undefined';
    case 'geolocation':
      return 'geolocation' in navigator;
    case 'camera':
      return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    case 'notifications':
      return 'Notification' in window;
    default:
      return true;
  }
};

// Get all feature statuses
export const getAllFeatureStatuses = async (): Promise<Record<string, FeatureStatus>> => {
  const statuses: Record<string, FeatureStatus> = {};
  
  await Promise.all(
    Object.keys(FEATURES).map(async (featureName) => {
      statuses[featureName] = await checkFeatureAvailability(featureName);
    })
  );

  return statuses;
};

// Feature flag hook
export const useFeatureFlag = (featureName: string) => {
  const [status, setStatus] = React.useState<FeatureStatus>({
    available: false,
    reason: 'Checking...'
  });
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    checkFeatureAvailability(featureName)
      .then(setStatus)
      .finally(() => setLoading(false));
  }, [featureName]);

  return { ...status, loading };
};

// Feature wrapper component
interface FeatureWrapperProps {
  feature: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUnavailable?: boolean;
}

export const FeatureWrapper: React.FC<FeatureWrapperProps> = ({
  feature,
  children,
  fallback,
  showUnavailable = true
}) => {
  const { available, loading, reason, estimatedAvailability } = useFeatureFlag(feature);

  if (loading) {
    return (
      <div className="flex justify-center items-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!available) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (!showUnavailable) {
      return null;
    }

    return (
      <div className="bg-warning-50 border border-warning-200 rounded-lg p-4 text-center">
        <div className="text-warning-600 font-medium mb-1">
          {FEATURES[feature]?.name || feature} Unavailable
        </div>
        <div className="text-sm text-warning-500">
          {reason}
          {estimatedAvailability && ` • ${estimatedAvailability}`}
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

// Graceful degradation helper
export const withGracefulDegradation = <T extends object>(
  Component: React.ComponentType<T>,
  featureName: string,
  fallbackComponent?: React.ComponentType<T>
) => {
  return (props: T) => (
    <FeatureWrapper
      feature={featureName}
      fallback={fallbackComponent ? <fallbackComponent {...props} /> : undefined}
    >
      <Component {...props} />
    </FeatureWrapper>
  );
};

// Clear feature cache
export const clearFeatureCache = () => {
  featureCache.clear();
};

// Get feature info
export const getFeatureInfo = (featureName: string): FeatureConfig | null => {
  return FEATURES[featureName] || null;
};

// Register new feature
export const registerFeature = (name: string, config: FeatureConfig) => {
  FEATURES[name] = config;
};

export default {
  checkFeatureAvailability,
  getAllFeatureStatuses,
  useFeatureFlag,
  FeatureWrapper,
  withGracefulDegradation,
  clearFeatureCache,
  getFeatureInfo,
  registerFeature
};
