import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";

// API Configuration
const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:5000/api";

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }
    return Promise.reject(error);
  }
);

// API Response Types
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: any[];
}

interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      currentPage: number;
      totalPages: number;
      totalResults: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

// Auth API
export const authAPI = {
  register: async (userData: {
    name: string;
    email: string;
    password: string;
    phone?: string;
    profileType: string;
  }): Promise<ApiResponse> => {
    const response = await api.post("/auth/register", userData);
    return response.data;
  },

  login: async (credentials: {
    email: string;
    password: string;
  }): Promise<ApiResponse> => {
    const response = await api.post("/auth/login", credentials);
    return response.data;
  },

  verify: async (): Promise<ApiResponse> => {
    const response = await api.get("/auth/verify");
    return response.data;
  },

  logout: async (): Promise<ApiResponse> => {
    const response = await api.post("/auth/logout");
    return response.data;
  },

  refreshToken: async (): Promise<ApiResponse> => {
    const response = await api.post("/auth/refresh");
    return response.data;
  },
};

// User API
export const userAPI = {
  getProfile: async (userId?: string): Promise<ApiResponse> => {
    const url = userId ? `/users/${userId}` : "/users/profile";
    const response = await api.get(url);
    return response.data;
  },

  updateProfile: async (profileData: any): Promise<ApiResponse> => {
    const response = await api.put("/users/profile", profileData);
    return response.data;
  },

  updatePersonalInfo: async (personalInfo: any): Promise<ApiResponse> => {
    const response = await api.put("/profiles/personal", personalInfo);
    return response.data;
  },

  updateEducationCareer: async (educationCareer: any): Promise<ApiResponse> => {
    const response = await api.put(
      "/profiles/education-career",
      educationCareer
    );
    return response.data;
  },

  updateFamilyInfo: async (familyInfo: any): Promise<ApiResponse> => {
    const response = await api.put("/profiles/family", familyInfo);
    return response.data;
  },

  updateLifestyle: async (lifestyle: any): Promise<ApiResponse> => {
    const response = await api.put("/profiles/lifestyle", lifestyle);
    return response.data;
  },

  updateReligiousInfo: async (religiousInfo: any): Promise<ApiResponse> => {
    const response = await api.put("/profiles/religious", religiousInfo);
    return response.data;
  },

  updatePartnerPreferences: async (preferences: any): Promise<ApiResponse> => {
    const response = await api.put(
      "/profiles/partner-preferences",
      preferences
    );
    return response.data;
  },

  uploadProfilePicture: async (file: File): Promise<ApiResponse> => {
    const formData = new FormData();
    formData.append("profilePicture", file);

    const response = await api.post("/upload/profile-picture", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // Photo management
  uploadPhotos: async (formData: FormData): Promise<ApiResponse> => {
    const response = await api.post("/api/profile/photos", formData, {
      headers: { "Content-Type": "multipart/form-data" },
    });
    return response.data;
  },

  getPhotos: async (): Promise<ApiResponse> => {
    const response = await api.get("/api/profile/photos");
    return response.data;
  },

  deletePhoto: async (photoId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/api/profile/photos/${photoId}`);
    return response.data;
  },

  setProfilePicture: async (photoId: string): Promise<ApiResponse> => {
    const response = await api.put(
      `/api/profile/photos/${photoId}/profile-picture`
    );
    return response.data;
  },

  updatePhotoVisibility: async (
    photoId: string,
    visibility: string
  ): Promise<ApiResponse> => {
    const response = await api.put(
      `/api/profile/photos/${photoId}/visibility`,
      { visibility }
    );
    return response.data;
  },
};

// Search API
export const searchAPI = {
  searchProfiles: async (criteria: {
    page?: number;
    limit?: number;
    ageMin?: number;
    ageMax?: number;
    heightMin?: number;
    heightMax?: number;
    religion?: string;
    caste?: string;
    maritalStatus?: string;
    education?: string;
    occupation?: string;
    city?: string;
    state?: string;
    diet?: string;
    manglik?: string;
    withPhoto?: boolean;
    verified?: boolean;
    sortBy?: string;
  }): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/search", { params: criteria });
    return response.data;
  },

  getRecommendations: async (limit?: number): Promise<ApiResponse> => {
    const response = await api.get("/matching/recommendations", {
      params: { limit },
    });
    return response.data;
  },

  getTrendingProfiles: async (limit?: number): Promise<ApiResponse> => {
    const response = await api.get("/matching/trending", {
      params: { limit },
    });
    return response.data;
  },

  getRecentProfiles: async (
    limit?: number,
    days?: number
  ): Promise<ApiResponse> => {
    const response = await api.get("/matching/recent", {
      params: { limit, days },
    });
    return response.data;
  },
};

// Interest API
export const interestAPI = {
  sendInterest: async (
    toUserId: string,
    message?: string
  ): Promise<ApiResponse> => {
    const response = await api.post("/interests/send", {
      toUserId,
      message,
    });
    return response.data;
  },

  respondToInterest: async (
    interestId: string,
    action: "accept" | "decline",
    message?: string
  ): Promise<ApiResponse> => {
    const response = await api.put(`/interests/${interestId}/respond`, {
      action,
      message,
    });
    return response.data;
  },

  getSentInterests: async (
    page?: number,
    limit?: number
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/interests/sent", {
      params: { page, limit },
    });
    return response.data;
  },

  getReceivedInterests: async (
    page?: number,
    limit?: number
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/interests/received", {
      params: { page, limit },
    });
    return response.data;
  },

  getMutualInterests: async (
    page?: number,
    limit?: number
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/interests/mutual", {
      params: { page, limit },
    });
    return response.data;
  },

  withdrawInterest: async (interestId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/interests/${interestId}`);
    return response.data;
  },
};

// Message API
export const messageAPI = {
  getConversations: async (
    page?: number,
    limit?: number
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/messages/conversations", {
      params: { page, limit },
    });
    return response.data;
  },

  getMessages: async (
    conversationId: string,
    page?: number,
    limit?: number
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get(
      `/messages/conversations/${conversationId}/messages`,
      {
        params: { page, limit },
      }
    );
    return response.data;
  },

  sendMessage: async (messageData: {
    receiverId: string;
    content: string;
    messageType?: string;
    replyTo?: string;
  }): Promise<ApiResponse> => {
    const response = await api.post("/messages/send", messageData);
    return response.data;
  },

  markAsRead: async (conversationId: string): Promise<ApiResponse> => {
    const response = await api.put(
      `/messages/conversations/${conversationId}/read`
    );
    return response.data;
  },

  deleteMessage: async (messageId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/messages/${messageId}`);
    return response.data;
  },

  getUnreadCount: async (): Promise<ApiResponse> => {
    const response = await api.get("/messages/unread-count");
    return response.data;
  },
};

// Notification API
export const notificationAPI = {
  getNotifications: async (
    page?: number,
    limit?: number,
    type?: string,
    read?: boolean
  ): Promise<PaginatedResponse<any>> => {
    const response = await api.get("/notifications", {
      params: { page, limit, type, read },
    });
    return response.data;
  },

  markAsRead: async (notificationId: string): Promise<ApiResponse> => {
    const response = await api.put(`/notifications/${notificationId}/read`);
    return response.data;
  },

  markAllAsRead: async (): Promise<ApiResponse> => {
    const response = await api.put("/notifications/mark-all-read");
    return response.data;
  },

  getUnreadCount: async (): Promise<ApiResponse> => {
    const response = await api.get("/notifications/unread-count");
    return response.data;
  },

  deleteNotification: async (notificationId: string): Promise<ApiResponse> => {
    const response = await api.delete(`/notifications/${notificationId}`);
    return response.data;
  },
};

// Messaging API
export const messagingAPI = {
  getConversations: async (): Promise<ApiResponse> => {
    const response = await api.get("/api/conversations");
    return response.data;
  },

  getMessages: async (
    conversationId: string,
    page = 1,
    limit = 50
  ): Promise<ApiResponse> => {
    const response = await api.get(
      `/api/conversations/${conversationId}/messages`,
      {
        params: { page, limit },
      }
    );
    return response.data;
  },

  sendMessage: async (
    conversationId: string,
    messageData: { content: string; messageType: string }
  ): Promise<ApiResponse> => {
    const response = await api.post(
      `/api/conversations/${conversationId}/messages`,
      messageData
    );
    return response.data;
  },

  markMessagesAsRead: async (conversationId: string): Promise<ApiResponse> => {
    const response = await api.put(`/api/conversations/${conversationId}/read`);
    return response.data;
  },

  createConversation: async (otherUserId: string): Promise<ApiResponse> => {
    const response = await api.post("/api/conversations", { otherUserId });
    return response.data;
  },
};

// Subscription API
export const subscriptionAPI = {
  getPlans: async (): Promise<ApiResponse> => {
    const response = await api.get("/api/subscription/plans");
    return response.data;
  },

  getCurrentSubscription: async (): Promise<ApiResponse> => {
    const response = await api.get("/api/subscription/current");
    return response.data;
  },

  createSubscription: async (subscriptionData: {
    planId: string;
    duration: string;
    amount: number;
  }): Promise<ApiResponse> => {
    const response = await api.post(
      "/api/subscription/create",
      subscriptionData
    );
    return response.data;
  },

  cancelSubscription: async (): Promise<ApiResponse> => {
    const response = await api.post("/api/subscription/cancel");
    return response.data;
  },
};

// Verification API
export const verificationAPI = {
  requestPhoneVerification: async (): Promise<ApiResponse> => {
    const response = await api.post("/api/verification/phone/request");
    return response.data;
  },

  verifyPhone: async (otp: string): Promise<ApiResponse> => {
    const response = await api.post("/api/verification/phone/verify", { otp });
    return response.data;
  },

  requestEmailVerification: async (): Promise<ApiResponse> => {
    const response = await api.post("/api/verification/email/request");
    return response.data;
  },

  verifyEmail: async (token: string): Promise<ApiResponse> => {
    const response = await api.post("/api/verification/email/verify", {
      token,
    });
    return response.data;
  },
};

// Health check
export const healthAPI = {
  check: async (): Promise<ApiResponse> => {
    const response = await api.get("/health");
    return response.data;
  },
};

// Export the main API instance
export default api;
