const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const verificationStatusSchema = new mongoose.Schema({
  email: { type: Boolean, default: false },
  phone: { type: Boolean, default: false },
  photo: { type: Boolean, default: false },
  document: { type: Boolean, default: false }
});

const personalInfoSchema = new mongoose.Schema({
  dateOfBirth: Date,
  age: Number,
  height: String,
  weight: String,
  bodyType: String,
  complexion: String,
  physicalStatus: String,
  maritalStatus: {
    type: String,
    enum: ['never_married', 'divorced', 'widowed', 'separated'],
    default: 'never_married'
  },
  motherTongue: String,
  knownLanguages: [String],
  country: String,
  state: String,
  city: String,
  pincode: String,
  willingToRelocate: Boolean,
  aboutMe: String
});

const familyInfoSchema = new mongoose.Schema({
  familyType: {
    type: String,
    enum: ['nuclear', 'joint', 'others']
  },
  familyStatus: {
    type: String,
    enum: ['middle_class', 'upper_middle_class', 'rich', 'affluent']
  },
  familyValues: {
    type: String,
    enum: ['orthodox', 'traditional', 'moderate', 'liberal']
  },
  fatherOccupation: String,
  motherOccupation: String,
  fatherLiving: Boolean,
  motherLiving: Boolean,
  brothers: Number,
  sisters: Number,
  marriedBrothers: Number,
  marriedSisters: Number,
  familyIncome: String,
  familyProperty: String
});

const educationCareerSchema = new mongoose.Schema({
  highestEducation: String,
  educationDetails: String,
  institution: String,
  yearOfPassing: Number,
  professionalQualification: String,
  occupation: String,
  designation: String,
  company: String,
  workExperience: String,
  annualIncome: String,
  workLocation: String,
  careerAspiration: String,
  willingToRelocateForWork: Boolean
});

const lifestyleSchema = new mongoose.Schema({
  diet: {
    type: String,
    enum: ['vegetarian', 'non_vegetarian', 'vegan', 'jain_food']
  },
  smoking: {
    type: String,
    enum: ['never', 'occasionally', 'regularly', 'trying_to_quit']
  },
  drinking: {
    type: String,
    enum: ['never', 'occasionally', 'socially', 'regularly']
  },
  hobbies: [String],
  interests: [String],
  musicPreference: [String],
  sportsInterests: [String],
  fitnessRoutine: String
});

const religiousInfoSchema = new mongoose.Schema({
  religion: {
    type: String,
    enum: ['hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain', 'parsi', 'jewish', 'other']
  },
  caste: String,
  subCaste: String,
  gothra: String,
  star: String,
  rashi: String,
  manglik: {
    type: String,
    enum: ['yes', 'no', 'anshik']
  }
});

const partnerPreferencesSchema = new mongoose.Schema({
  ageRange: {
    min: Number,
    max: Number
  },
  heightRange: {
    min: String,
    max: String
  },
  maritalStatus: [String],
  education: [String],
  occupation: [String],
  incomeRange: {
    min: String,
    max: String
  },
  location: [String],
  caste: [String],
  religion: [String],
  diet: [String],
  smoking: [String],
  drinking: [String],
  manglik: String,
  specialRequirements: String
});

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  phone: {
    type: String,
    required: true,
    unique: true
  },
  profileType: {
    type: String,
    enum: ['self', 'son', 'daughter', 'brother', 'sister', 'relative', 'friend'],
    default: 'self'
  },
  membershipType: {
    type: String,
    enum: ['free', 'silver', 'gold', 'platinum'],
    default: 'free'
  },
  profilePicture: String,
  profilePhotos: [String],
  verificationStatus: verificationStatusSchema,
  profileCompleted: {
    type: Boolean,
    default: false
  },
  profileCompletionPercentage: {
    type: Number,
    default: 0
  },
  personalInfo: personalInfoSchema,
  familyInfo: familyInfoSchema,
  educationCareer: educationCareerSchema,
  lifestyle: lifestyleSchema,
  religiousInfo: religiousInfoSchema,
  partnerPreferences: partnerPreferencesSchema,
  profileViews: {
    type: Number,
    default: 0
  },
  lastActive: {
    type: Date,
    default: Date.now
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isBlocked: {
    type: Boolean,
    default: false
  },
  blockedUsers: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  shortlistedProfiles: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  emailVerificationToken: String,
  phoneVerificationOTP: String,
  phoneVerificationExpires: Date,
  passwordResetToken: String,
  passwordResetExpires: Date,
  subscriptionExpiry: Date,
  premiumFeatures: {
    unlimitedViews: Boolean,
    advancedSearch: Boolean,
    priorityListing: Boolean,
    directMessaging: Boolean,
    contactDetails: Boolean,
    horoscopeMatching: Boolean,
    relationshipManager: Boolean
  }
}, {
  timestamps: true
});

// Index for search optimization
userSchema.index({ 'personalInfo.age': 1 });
userSchema.index({ 'personalInfo.city': 1 });
userSchema.index({ 'religiousInfo.religion': 1 });
userSchema.index({ 'religiousInfo.caste': 1 });
userSchema.index({ 'educationCareer.occupation': 1 });
userSchema.index({ 'educationCareer.annualIncome': 1 });
userSchema.index({ membershipType: 1 });
userSchema.index({ isActive: 1 });
userSchema.index({ lastActive: -1 });

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Calculate profile completion percentage
userSchema.methods.calculateProfileCompletion = function() {
  let completedFields = 0;
  const totalFields = 8;

  if (this.name && this.email && this.phone) completedFields++;
  if (this.profilePicture) completedFields++;
  if (this.personalInfo && Object.keys(this.personalInfo).length > 5) completedFields++;
  if (this.familyInfo && Object.keys(this.familyInfo).length > 3) completedFields++;
  if (this.educationCareer && Object.keys(this.educationCareer).length > 3) completedFields++;
  if (this.lifestyle && Object.keys(this.lifestyle).length > 2) completedFields++;
  if (this.religiousInfo && Object.keys(this.religiousInfo).length > 2) completedFields++;
  if (this.partnerPreferences && Object.keys(this.partnerPreferences).length > 3) completedFields++;

  this.profileCompletionPercentage = Math.round((completedFields / totalFields) * 100);
  this.profileCompleted = this.profileCompletionPercentage >= 80;
};

// Update last active timestamp
userSchema.methods.updateLastActive = function() {
  this.lastActive = new Date();
  return this.save();
};

module.exports = mongoose.model('User', userSchema);
