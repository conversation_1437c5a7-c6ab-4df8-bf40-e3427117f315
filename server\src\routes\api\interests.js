const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const { authenticateToken } = require('../../middleware/auth');
const { rateLimits } = require('../../middleware/rateLimiting');
const User = require('../../models/sequelize/User');
const { body, query, validationResult } = require('express-validator');

// In-memory storage for interests (in production, use a proper database table)
let interests = [];
let interestIdCounter = 1;

// Helper function to generate interest ID
const generateInterestId = () => {
  return `interest_${interestIdCounter++}_${Date.now()}`;
};

// Send interest
router.post('/send', authenticateToken, rateLimits.interests, [
  body('toUserId').notEmpty().withMessage('Target user ID is required'),
  body('interestType').optional().isIn(['like', 'super_like', 'premium_interest']),
  body('message').optional().isLength({ max: 500 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const fromUserId = req.user.id;
    const { toUserId, interestType = 'like', message } = req.body;

    if (fromUserId === toUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send interest to yourself'
      });
    }

    // Check if target user exists
    const targetUser = await User.findOne({
      where: {
        id: toUserId,
        isActive: true,
        isBlocked: false
      }
    });

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if interest already exists
    const existingInterest = interests.find(i => 
      i.fromUserId === fromUserId && i.toUserId === toUserId
    );

    if (existingInterest) {
      return res.status(409).json({
        success: false,
        message: 'Interest already sent to this user'
      });
    }

    // Create interest
    const interest = {
      id: generateInterestId(),
      fromUserId,
      toUserId,
      interestType,
      status: 'pending',
      message: message || '',
      sentAt: new Date().toISOString(),
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      isMutual: false
    };

    interests.push(interest);

    // Check for mutual interest
    const mutualInterest = interests.find(i => 
      i.fromUserId === toUserId && i.toUserId === fromUserId && i.status === 'pending'
    );

    if (mutualInterest) {
      interest.isMutual = true;
      mutualInterest.isMutual = true;
    }

    // Get sender info for response
    const senderUser = await User.findByPk(fromUserId, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    res.status(201).json({
      success: true,
      message: 'Interest sent successfully',
      data: { 
        interest: {
          ...interest,
          user: {
            id: targetUser.id,
            name: targetUser.personalInfo?.firstName && targetUser.personalInfo?.lastName ?
              `${targetUser.personalInfo.firstName} ${targetUser.personalInfo.lastName}` : 'Unknown',
            profilePicture: targetUser.profilePicture
          }
        },
        isMutual: interest.isMutual
      }
    });

  } catch (error) {
    console.error('Send interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send interest'
    });
  }
});

// Get interests (sent and received)
router.get('/', authenticateToken, [
  query('type').optional().isIn(['sent', 'received', 'all'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { type = 'all' } = req.query;

    let userInterests = {
      sent: [],
      received: []
    };

    if (type === 'all' || type === 'sent') {
      const sentInterests = interests.filter(i => i.fromUserId === userId);
      
      for (const interest of sentInterests) {
        const targetUser = await User.findByPk(interest.toUserId, {
          attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
        });

        if (targetUser) {
          const personal = targetUser.personalInfo || {};
          const photos = targetUser.profilePhotos || [];
          const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

          userInterests.sent.push({
            ...interest,
            user: {
              id: targetUser.id,
              name: personal.firstName && personal.lastName ? 
                `${personal.firstName} ${personal.lastName}` : 'Unknown',
              age: personal.age,
              location: personal.city && personal.state ? 
                `${personal.city}, ${personal.state}` : '',
              profilePicture: profilePicture?.filePath,
              membershipType: targetUser.membershipType
            }
          });
        }
      }
    }

    if (type === 'all' || type === 'received') {
      const receivedInterests = interests.filter(i => i.toUserId === userId);
      
      for (const interest of receivedInterests) {
        const senderUser = await User.findByPk(interest.fromUserId, {
          attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
        });

        if (senderUser) {
          const personal = senderUser.personalInfo || {};
          const professional = senderUser.educationCareer || {};
          const photos = senderUser.profilePhotos || [];
          const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

          userInterests.received.push({
            ...interest,
            user: {
              id: senderUser.id,
              name: personal.firstName && personal.lastName ? 
                `${personal.firstName} ${personal.lastName}` : 'Unknown',
              age: personal.age,
              location: personal.city && personal.state ? 
                `${personal.city}, ${personal.state}` : '',
              occupation: professional.occupation,
              education: professional.highestEducation,
              profilePicture: profilePicture?.filePath,
              membershipType: senderUser.membershipType,
              verificationStatus: {
                photo: senderUser.photoVerifiedAt ? true : false,
                phone: senderUser.phoneVerifiedAt ? true : false,
                email: senderUser.emailVerifiedAt ? true : false
              }
            }
          });
        }
      }
    }

    res.json({
      success: true,
      data: userInterests
    });

  } catch (error) {
    console.error('Get interests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get interests'
    });
  }
});

// Respond to interest
router.put('/:interestId/respond', authenticateToken, [
  body('status').isIn(['accepted', 'declined']).withMessage('Status must be accepted or declined'),
  body('responseMessage').optional().isLength({ max: 500 }).trim()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { interestId } = req.params;
    const { status, responseMessage } = req.body;
    const userId = req.user.id;

    const interest = interests.find(i => i.id === interestId && i.toUserId === userId);
    
    if (!interest) {
      return res.status(404).json({
        success: false,
        message: 'Interest not found'
      });
    }

    if (interest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Interest has already been responded to'
      });
    }

    // Update interest
    interest.status = status;
    interest.responseMessage = responseMessage || '';
    interest.respondedAt = new Date().toISOString();

    // Get user info for response
    const responderUser = await User.findByPk(userId, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    res.json({
      success: true,
      message: `Interest ${status} successfully`,
      data: { 
        interest: {
          ...interest,
          user: {
            id: responderUser.id,
            name: responderUser.personalInfo?.firstName && responderUser.personalInfo?.lastName ?
              `${responderUser.personalInfo.firstName} ${responderUser.personalInfo.lastName}` : 'Unknown'
          }
        }
      }
    });

  } catch (error) {
    console.error('Respond to interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to respond to interest'
    });
  }
});

// Withdraw interest
router.delete('/:interestId', authenticateToken, async (req, res) => {
  try {
    const { interestId } = req.params;
    const userId = req.user.id;

    const interestIndex = interests.findIndex(i => 
      i.id === interestId && i.fromUserId === userId && i.status === 'pending'
    );

    if (interestIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Interest not found or cannot be withdrawn'
      });
    }

    interests.splice(interestIndex, 1);

    res.json({
      success: true,
      message: 'Interest withdrawn successfully'
    });

  } catch (error) {
    console.error('Withdraw interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to withdraw interest'
    });
  }
});

// Get mutual interests
router.get('/mutual', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Find mutual interests
    const mutualInterests = interests.filter(i => 
      (i.fromUserId === userId || i.toUserId === userId) && i.isMutual
    );

    const formattedInterests = [];

    for (const interest of mutualInterests) {
      const otherUserId = interest.fromUserId === userId ? interest.toUserId : interest.fromUserId;
      const otherUser = await User.findByPk(otherUserId, {
        attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
      });

      if (otherUser) {
        const personal = otherUser.personalInfo || {};
        const photos = otherUser.profilePhotos || [];
        const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

        formattedInterests.push({
          ...interest,
          user: {
            id: otherUser.id,
            name: personal.firstName && personal.lastName ? 
              `${personal.firstName} ${personal.lastName}` : 'Unknown',
            age: personal.age,
            location: personal.city && personal.state ? 
              `${personal.city}, ${personal.state}` : '',
            profilePicture: profilePicture?.filePath,
            membershipType: otherUser.membershipType
          }
        });
      }
    }

    res.json({
      success: true,
      data: { interests: formattedInterests }
    });

  } catch (error) {
    console.error('Get mutual interests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get mutual interests'
    });
  }
});

// Get interest statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = {
      sent: {
        total: interests.filter(i => i.fromUserId === userId).length,
        pending: interests.filter(i => i.fromUserId === userId && i.status === 'pending').length,
        accepted: interests.filter(i => i.fromUserId === userId && i.status === 'accepted').length,
        declined: interests.filter(i => i.fromUserId === userId && i.status === 'declined').length
      },
      received: {
        total: interests.filter(i => i.toUserId === userId).length,
        pending: interests.filter(i => i.toUserId === userId && i.status === 'pending').length,
        accepted: interests.filter(i => i.toUserId === userId && i.status === 'accepted').length,
        declined: interests.filter(i => i.toUserId === userId && i.status === 'declined').length
      },
      mutual: interests.filter(i => 
        (i.fromUserId === userId || i.toUserId === userId) && i.isMutual
      ).length
    };

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get interest stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get interest statistics'
    });
  }
});

module.exports = router;
