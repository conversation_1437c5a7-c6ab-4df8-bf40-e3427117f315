const mongoose = require('mongoose');

const interestSchema = new mongoose.Schema({
  from: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'declined', 'withdrawn'],
    default: 'pending'
  },
  message: {
    type: String,
    maxlength: 500
  },
  isPremium: {
    type: Boolean,
    default: false
  },
  interestType: {
    type: String,
    enum: ['express_interest', 'send_message', 'request_contact', 'request_photo'],
    default: 'express_interest'
  },
  responseMessage: {
    type: String,
    maxlength: 500
  },
  respondedAt: Date,
  expiresAt: {
    type: Date,
    default: function() {
      return new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
    }
  },
  viewedAt: Date,
  reminderSent: {
    type: Boolean,
    default: false
  },
  reminderCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Indexes for performance
interestSchema.index({ from: 1, to: 1 }, { unique: true });
interestSchema.index({ to: 1, status: 1 });
interestSchema.index({ from: 1, status: 1 });
interestSchema.index({ createdAt: -1 });
interestSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

// Prevent duplicate interests
interestSchema.pre('save', async function(next) {
  if (this.isNew) {
    const existingInterest = await this.constructor.findOne({
      from: this.from,
      to: this.to,
      status: { $in: ['pending', 'accepted'] }
    });
    
    if (existingInterest) {
      const error = new Error('Interest already exists between these users');
      error.statusCode = 400;
      return next(error);
    }
  }
  next();
});

// Static method to get interest statistics
interestSchema.statics.getInterestStats = async function(userId) {
  const stats = await this.aggregate([
    {
      $match: {
        $or: [{ from: mongoose.Types.ObjectId(userId) }, { to: mongoose.Types.ObjectId(userId) }]
      }
    },
    {
      $group: {
        _id: null,
        sentInterests: {
          $sum: {
            $cond: [{ $eq: ['$from', mongoose.Types.ObjectId(userId)] }, 1, 0]
          }
        },
        receivedInterests: {
          $sum: {
            $cond: [{ $eq: ['$to', mongoose.Types.ObjectId(userId)] }, 1, 0]
          }
        },
        acceptedSent: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$from', mongoose.Types.ObjectId(userId)] },
                  { $eq: ['$status', 'accepted'] }
                ]
              },
              1,
              0
            ]
          }
        },
        acceptedReceived: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$to', mongoose.Types.ObjectId(userId)] },
                  { $eq: ['$status', 'accepted'] }
                ]
              },
              1,
              0
            ]
          }
        },
        pendingReceived: {
          $sum: {
            $cond: [
              {
                $and: [
                  { $eq: ['$to', mongoose.Types.ObjectId(userId)] },
                  { $eq: ['$status', 'pending'] }
                ]
              },
              1,
              0
            ]
          }
        }
      }
    }
  ]);

  return stats[0] || {
    sentInterests: 0,
    receivedInterests: 0,
    acceptedSent: 0,
    acceptedReceived: 0,
    pendingReceived: 0
  };
};

// Instance method to check if interest can be withdrawn
interestSchema.methods.canWithdraw = function() {
  return this.status === 'pending' && 
         new Date() < this.expiresAt;
};

// Instance method to check if interest has expired
interestSchema.methods.isExpired = function() {
  return new Date() > this.expiresAt;
};

module.exports = mongoose.model('Interest', interestSchema);
