const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { UserSubscription } = require('../models/Subscription');

// Authenticate JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findById(decoded.userId).select('-password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid token - user not found'
      });
    }

    if (user.isBlocked) {
      return res.status(403).json({
        success: false,
        message: 'Account has been blocked'
      });
    }

    // Update last active timestamp
    user.updateLastActive();

    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token expired'
      });
    }

    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      message: 'Authentication error'
    });
  }
};

// Check if user has premium subscription
const requirePremium = async (req, res, next) => {
  try {
    const subscription = await UserSubscription.getActiveSubscription(req.user._id);
    
    if (!subscription || subscription.plan.name === 'free') {
      return res.status(403).json({
        success: false,
        message: 'Premium subscription required',
        code: 'PREMIUM_REQUIRED'
      });
    }

    req.subscription = subscription;
    next();
  } catch (error) {
    console.error('Premium check error:', error);
    res.status(500).json({
      success: false,
      message: 'Subscription verification error'
    });
  }
};

// Check specific premium feature
const requireFeature = (featureName) => {
  return async (req, res, next) => {
    try {
      const subscription = await UserSubscription.getActiveSubscription(req.user._id);
      
      if (!subscription) {
        return res.status(403).json({
          success: false,
          message: 'Subscription required',
          code: 'SUBSCRIPTION_REQUIRED'
        });
      }

      const hasFeature = await subscription.hasFeature(featureName);
      
      if (!hasFeature) {
        return res.status(403).json({
          success: false,
          message: `Feature '${featureName}' not available in your plan`,
          code: 'FEATURE_NOT_AVAILABLE'
        });
      }

      req.subscription = subscription;
      next();
    } catch (error) {
      console.error('Feature check error:', error);
      res.status(500).json({
        success: false,
        message: 'Feature verification error'
      });
    }
  };
};

// Check usage limits
const checkUsageLimit = (featureName) => {
  return async (req, res, next) => {
    try {
      const subscription = await UserSubscription.getActiveSubscription(req.user._id);
      
      if (!subscription) {
        // Free users have basic limits
        return res.status(403).json({
          success: false,
          message: 'Subscription required for this feature',
          code: 'SUBSCRIPTION_REQUIRED'
        });
      }

      const canUse = await subscription.canUseFeature(featureName);
      
      if (!canUse) {
        return res.status(429).json({
          success: false,
          message: `Daily limit exceeded for ${featureName}`,
          code: 'USAGE_LIMIT_EXCEEDED'
        });
      }

      req.subscription = subscription;
      next();
    } catch (error) {
      console.error('Usage limit check error:', error);
      res.status(500).json({
        success: false,
        message: 'Usage verification error'
      });
    }
  };
};

// Admin authentication
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Check if user is admin (you can implement your own admin logic)
    if (req.user.email !== process.env.ADMIN_EMAIL) {
      return res.status(403).json({
        success: false,
        message: 'Admin access required'
      });
    }

    next();
  } catch (error) {
    console.error('Admin check error:', error);
    res.status(500).json({
      success: false,
      message: 'Admin verification error'
    });
  }
};

// Profile completion check
const requireCompleteProfile = (req, res, next) => {
  if (!req.user.profileCompleted) {
    return res.status(400).json({
      success: false,
      message: 'Please complete your profile first',
      code: 'PROFILE_INCOMPLETE',
      completionPercentage: req.user.profileCompletionPercentage
    });
  }
  next();
};

// Email verification check
const requireEmailVerification = (req, res, next) => {
  if (!req.user.verificationStatus.email) {
    return res.status(400).json({
      success: false,
      message: 'Please verify your email address first',
      code: 'EMAIL_NOT_VERIFIED'
    });
  }
  next();
};

// Phone verification check
const requirePhoneVerification = (req, res, next) => {
  if (!req.user.verificationStatus.phone) {
    return res.status(400).json({
      success: false,
      message: 'Please verify your phone number first',
      code: 'PHONE_NOT_VERIFIED'
    });
  }
  next();
};

// Optional authentication (doesn't fail if no token)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (token) {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const user = await User.findById(decoded.userId).select('-password');
      
      if (user && !user.isBlocked) {
        req.user = user;
        user.updateLastActive();
      }
    }
    
    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

module.exports = {
  authenticateToken,
  requirePremium,
  requireFeature,
  checkUsageLimit,
  requireAdmin,
  requireCompleteProfile,
  requireEmailVerification,
  requirePhoneVerification,
  optionalAuth
};
