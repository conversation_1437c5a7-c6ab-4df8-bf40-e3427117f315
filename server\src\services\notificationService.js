const Notification = require('../models/Notification');
const { sendEmail } = require('./emailService');
const { sendSMS } = require('./smsService');

class NotificationService {
  constructor() {
    this.templates = {
      new_interest: {
        title: 'New Interest Received!',
        message: '{senderN<PERSON>} has expressed interest in your profile',
        priority: 'high',
        category: 'social'
      },
      interest_accepted: {
        title: 'Interest Accepted!',
        message: '{receiver<PERSON><PERSON>} has accepted your interest',
        priority: 'high',
        category: 'social'
      },
      interest_declined: {
        title: 'Interest Response',
        message: '{receiverN<PERSON>} has declined your interest',
        priority: 'medium',
        category: 'social'
      },
      new_message: {
        title: 'New Message',
        message: 'You have a new message from {senderName}',
        priority: 'high',
        category: 'social'
      },
      profile_viewed: {
        title: 'Profile Viewed',
        message: '{viewerN<PERSON>} viewed your profile',
        priority: 'low',
        category: 'social'
      },
      subscription_expiring: {
        title: 'Subscription Expiring Soon',
        message: 'Your {planName} subscription expires in {days} days',
        priority: 'medium',
        category: 'system'
      },
      subscription_expired: {
        title: 'Subscription Expired',
        message: 'Your {planName} subscription has expired',
        priority: 'high',
        category: 'system'
      },
      profile_verified: {
        title: 'Profile Verified!',
        message: 'Your profile has been successfully verified',
        priority: 'medium',
        category: 'system'
      },
      new_match: {
        title: 'New Match Found!',
        message: 'We found a {compatibility}% compatible match for you',
        priority: 'high',
        category: 'social'
      },
      birthday_reminder: {
        title: 'Birthday Reminder',
        message: "It's {name}'s birthday today!",
        priority: 'low',
        category: 'social'
      }
    };
  }

  // Create and send notification
  async createNotification(type, recipientId, data = {}, options = {}) {
    try {
      const template = this.templates[type];
      if (!template) {
        throw new Error(`Unknown notification type: ${type}`);
      }

      // Process template with data
      const title = this.processTemplate(template.title, data);
      const message = this.processTemplate(template.message, data);

      const notificationData = {
        recipient: recipientId,
        sender: data.senderId,
        type,
        title,
        message,
        priority: options.priority || template.priority,
        category: options.category || template.category,
        data: data.additionalData || {},
        actionUrl: data.actionUrl,
        actionText: data.actionText,
        imageUrl: data.imageUrl,
        expiresAt: options.expiresAt
      };

      const notification = await Notification.createNotification(notificationData);

      // Send via other channels if requested
      if (options.sendEmail) {
        await this.sendEmailNotification(notification, data);
      }

      if (options.sendSMS) {
        await this.sendSMSNotification(notification, data);
      }

      return notification;
    } catch (error) {
      console.error('Error creating notification:', error);
      throw error;
    }
  }

  // Process template with data
  processTemplate(template, data) {
    let processed = template;
    
    Object.keys(data).forEach(key => {
      const placeholder = `{${key}}`;
      processed = processed.replace(new RegExp(placeholder, 'g'), data[key]);
    });

    return processed;
  }

  // Send email notification
  async sendEmailNotification(notification, data) {
    try {
      const User = require('../models/User');
      const recipient = await User.findById(notification.recipient);
      
      if (!recipient || !recipient.email) {
        throw new Error('Recipient email not found');
      }

      await sendEmail({
        to: recipient.email,
        subject: notification.title,
        template: 'notification',
        data: {
          name: recipient.name,
          title: notification.title,
          message: notification.message,
          actionUrl: notification.actionUrl,
          actionText: notification.actionText || 'View Details'
        }
      });

      // Update delivery status
      notification.deliveryStatus.email.sent = true;
      notification.deliveryStatus.email.sentAt = new Date();
      await notification.save();

    } catch (error) {
      console.error('Email notification failed:', error);
      notification.deliveryStatus.email.error = error.message;
      await notification.save();
    }
  }

  // Send SMS notification
  async sendSMSNotification(notification, data) {
    try {
      const User = require('../models/User');
      const recipient = await User.findById(notification.recipient);
      
      if (!recipient || !recipient.phone) {
        throw new Error('Recipient phone not found');
      }

      const smsMessage = `${notification.title}: ${notification.message}`;
      
      await sendSMS({
        to: recipient.phone,
        message: smsMessage
      });

      // Update delivery status
      notification.deliveryStatus.sms.sent = true;
      notification.deliveryStatus.sms.sentAt = new Date();
      await notification.save();

    } catch (error) {
      console.error('SMS notification failed:', error);
      notification.deliveryStatus.sms.error = error.message;
      await notification.save();
    }
  }

  // Bulk notification methods
  async sendBulkNotification(type, recipients, data = {}, options = {}) {
    const notifications = [];
    
    for (const recipientId of recipients) {
      try {
        const notification = await this.createNotification(type, recipientId, data, options);
        notifications.push(notification);
      } catch (error) {
        console.error(`Failed to send notification to ${recipientId}:`, error);
      }
    }

    return notifications;
  }

  // Specific notification methods
  async notifyNewInterest(recipientId, senderName, senderId, profileUrl) {
    return this.createNotification('new_interest', recipientId, {
      senderName,
      senderId,
      actionUrl: profileUrl,
      actionText: 'View Profile'
    }, {
      sendEmail: true
    });
  }

  async notifyInterestAccepted(recipientId, receiverName, chatUrl) {
    return this.createNotification('interest_accepted', recipientId, {
      receiverName,
      actionUrl: chatUrl,
      actionText: 'Start Chatting'
    }, {
      sendEmail: true,
      sendSMS: true
    });
  }

  async notifyNewMessage(recipientId, senderName, senderId, messagePreview, chatUrl) {
    return this.createNotification('new_message', recipientId, {
      senderName,
      senderId,
      messagePreview,
      actionUrl: chatUrl,
      actionText: 'Reply'
    });
  }

  async notifyProfileViewed(recipientId, viewerName, viewerId, profileUrl) {
    return this.createNotification('profile_viewed', recipientId, {
      viewerName,
      viewerId,
      actionUrl: profileUrl,
      actionText: 'View Profile'
    });
  }

  async notifySubscriptionExpiring(recipientId, planName, daysRemaining, renewUrl) {
    return this.createNotification('subscription_expiring', recipientId, {
      planName,
      days: daysRemaining,
      actionUrl: renewUrl,
      actionText: 'Renew Now'
    }, {
      sendEmail: true,
      sendSMS: true
    });
  }

  async notifyNewMatch(recipientId, compatibility, matchProfileUrl) {
    return this.createNotification('new_match', recipientId, {
      compatibility,
      actionUrl: matchProfileUrl,
      actionText: 'View Match'
    }, {
      sendEmail: true
    });
  }

  // System notifications
  async sendSystemAnnouncement(title, message, recipients = [], options = {}) {
    const User = require('../models/User');
    
    // If no specific recipients, send to all active users
    if (recipients.length === 0) {
      const allUsers = await User.find({ isActive: true }).select('_id');
      recipients = allUsers.map(user => user._id);
    }

    return this.sendBulkNotification('system_announcement', recipients, {
      additionalData: { title, message }
    }, {
      priority: 'medium',
      category: 'system',
      expiresAt: options.expiresAt,
      sendEmail: options.sendEmail
    });
  }

  // Cleanup old notifications
  async cleanupOldNotifications() {
    try {
      const result = await Notification.cleanupOldNotifications();
      console.log(`Cleaned up ${result.deletedCount} old notifications`);
      return result;
    } catch (error) {
      console.error('Error cleaning up notifications:', error);
      throw error;
    }
  }

  // Get notification statistics
  async getNotificationStats(userId) {
    const stats = await Notification.aggregate([
      { $match: { recipient: mongoose.Types.ObjectId(userId) } },
      {
        $group: {
          _id: '$type',
          total: { $sum: 1 },
          unread: {
            $sum: { $cond: [{ $eq: ['$read', false] }, 1, 0] }
          }
        }
      }
    ]);

    const totalUnread = await Notification.getUnreadCount(userId);

    return {
      totalUnread,
      byType: stats
    };
  }
}

module.exports = new NotificationService();
