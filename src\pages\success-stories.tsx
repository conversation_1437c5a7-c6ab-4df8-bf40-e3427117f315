// This file is missing, creating it now
import React from 'react';
import { 
  <PERSON>, 
  Card<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>ooter,
  Button, 
  Image,
  Pagination
} from '@heroui/react';
import { Icon } from '@iconify/react';

export const SuccessStoriesPage: React.FC = () => {
  const [currentPage, setCurrentPage] = React.useState(1);
  
  // Mock success stories data
  const successStories = [
    {
      id: '1',
      coupleNames: '<PERSON><PERSON> & <PERSON>riya',
      marriageDate: '2023-11-15',
      story: 'We connected on BharatMatrimony and after a few meetings, we knew we were meant to be together. Our families met and everything fell into place perfectly. We got married in a traditional ceremony surrounded by our loved ones. We are grateful to BharatMatrimony for helping us find each other.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=11'],
      testimonial: 'BharatMatrimony made our journey to finding love so smooth and wonderful!',
      location: 'Mumbai, Maharashtra',
      createdAt: '2023-12-10',
    },
    {
      id: '2',
      coupleNames: '<PERSON><PERSON><PERSON>',
      marriageDate: '2023-09-22',
      story: 'Despite living in different cities, we found each other on BharatMatrimony. After months of calls and video chats, we decided to take the next step. Our families were supportive, and we got married in a beautiful ceremony. We now live together in Bangalore and couldn\'t be happier.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=12'],
      testimonial: 'Distance was never a barrier for us, thanks to BharatMatrimony!',
      location: 'Bangalore, Karnataka',
      createdAt: '2023-10-15',
    },
    {
      id: '3',
      coupleNames: 'Arjun & Nisha',
      marriageDate: '2023-12-05',
      story: 'Our parents created our profiles on BharatMatrimony. We connected instantly and after meeting a few times, we knew we wanted to spend our lives together. The platform made it easy for our families to communicate and arrange meetings. We had a beautiful wedding ceremony and are now enjoying our married life.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=13'],
      testimonial: 'From strangers to soulmates, our journey was beautiful!',
      location: 'Delhi, NCR',
      createdAt: '2024-01-20',
    },
    {
      id: '4',
      coupleNames: 'Suresh & Kavita',
      marriageDate: '2023-10-18',
      story: 'We were both looking for someone who shared our values and interests. BharatMatrimony\'s matching algorithm brought us together, and we hit it off immediately. After a few months of getting to know each other and our families, we decided to get married. We are grateful for the platform that helped us find our perfect match.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=14'],
      testimonial: 'BharatMatrimony\'s matching algorithm really works!',
      location: 'Chennai, Tamil Nadu',
      createdAt: '2023-11-25',
    },
    {
      id: '5',
      coupleNames: 'Aditya & Sneha',
      marriageDate: '2024-01-30',
      story: 'We both had specific criteria for our life partners, and BharatMatrimony helped us find exactly what we were looking for. After connecting on the platform, we met in person and felt an instant connection. Our families got along well, and we had a beautiful wedding ceremony. We are now starting our new life together.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=15'],
      testimonial: 'Finding the right partner was easy with BharatMatrimony!',
      location: 'Pune, Maharashtra',
      createdAt: '2024-02-15',
    },
    {
      id: '6',
      coupleNames: 'Rajesh & Pooja',
      marriageDate: '2023-08-12',
      story: 'We were both busy professionals who didn\'t have time to meet people through traditional means. BharatMatrimony made it easy for us to connect and get to know each other. We found that we had a lot in common and decided to take the next step. Our wedding was a beautiful blend of traditional and modern elements.',
      photos: ['https://img.heroui.chat/image/avatar?w=400&h=400&u=16'],
      testimonial: 'BharatMatrimony is perfect for busy professionals like us!',
      location: 'Hyderabad, Telangana',
      createdAt: '2023-09-05',
    },
  ];
  
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };
  
  const itemsPerPage = 3;
  const totalPages = Math.ceil(successStories.length / itemsPerPage);
  const currentStories = successStories.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">Success Stories</h1>
          <p className="text-default-600 max-w-2xl mx-auto">
            Read inspiring stories of couples who found their perfect match on BharatMatrimony and began their journey of love and companionship.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {currentStories.map((story) => (
            <Card key={story.id} className="overflow-hidden">
              <CardHeader className="p-0">
                <Image
                  src={story.photos[0]}
                  alt={story.coupleNames}
                  className="w-full h-64 object-cover"
                />
              </CardHeader>
              <CardBody>
                <h2 className="text-xl font-semibold mb-2">{story.coupleNames}</h2>
                <div className="flex items-center gap-2 text-default-500 text-sm mb-4">
                  <Icon icon="lucide:map-pin" />
                  <span>{story.location}</span>
                  <span className="mx-2">•</span>
                  <Icon icon="lucide:calendar" />
                  <span>Married on {formatDate(story.marriageDate)}</span>
                </div>
                <p className="text-default-700 line-clamp-4 mb-4">{story.story}</p>
                <div className="bg-primary-50 p-3 rounded-lg italic text-sm">
                  <p className="text-primary-700">"{story.testimonial}"</p>
                </div>
              </CardBody>
              <CardFooter>
                <Button 
                  color="primary" 
                  variant="flat" 
                  className="w-full"
                >
                  Read Full Story
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="flex justify-center">
          <Pagination 
            total={totalPages} 
            initialPage={1}
            page={currentPage}
            onChange={setCurrentPage}
            showControls
          />
        </div>
        
        <div className="mt-16 bg-primary-50 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Share Your Success Story</h2>
          <p className="text-default-600 max-w-2xl mx-auto mb-6">
            Found your perfect match on BharatMatrimony? We'd love to hear your story and share it with others to inspire them on their journey to finding love.
          </p>
          <Button 
            color="primary" 
            size="lg"
          >
            Submit Your Story
          </Button>
        </div>
      </div>
    </div>
  );
};