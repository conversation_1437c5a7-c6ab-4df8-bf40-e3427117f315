#!/usr/bin/env node

/**
 * Comprehensive API Test Script
 * Tests all API endpoints to ensure they're working correctly
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3001';
let authToken = null;
let testUserId = null;

// Test configuration
const testConfig = {
  email: '<EMAIL>',
  password: 'testpassword123',
  name: 'Test User',
  phone: '+919876543999'
};

// Helper function to make API requests
const apiRequest = async (method, endpoint, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };

    if (authToken) {
      config.headers.Authorization = `Bearer ${authToken}`;
    }

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data, status: response.status };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message,
      status: error.response?.status || 500
    };
  }
};

// Test functions
const testHealthCheck = async () => {
  console.log('\n🏥 Testing Health Check...');
  const result = await apiRequest('GET', '/health');
  
  if (result.success) {
    console.log('✅ Health check passed');
    return true;
  } else {
    console.log('❌ Health check failed:', result.error);
    return false;
  }
};

const testAuthentication = async () => {
  console.log('\n🔐 Testing Authentication...');
  
  // Test registration
  console.log('📝 Testing registration...');
  const registerResult = await apiRequest('POST', '/api/auth/register', {
    email: testConfig.email,
    password: testConfig.password,
    name: testConfig.name,
    phone: testConfig.phone,
    dateOfBirth: '1990-01-01',
    gender: 'male'
  });

  if (registerResult.success) {
    console.log('✅ Registration successful');
    testUserId = registerResult.data.data?.user?.id;
  } else if (registerResult.status === 409) {
    console.log('⚠️  User already exists, proceeding with login');
  } else {
    console.log('❌ Registration failed:', registerResult.error);
    return false;
  }

  // Test login
  console.log('🔑 Testing login...');
  const loginResult = await apiRequest('POST', '/api/auth/login', {
    email: testConfig.email,
    password: testConfig.password
  });

  if (loginResult.success) {
    console.log('✅ Login successful');
    authToken = loginResult.data.data?.token;
    testUserId = testUserId || loginResult.data.data?.user?.id;
    return true;
  } else {
    console.log('❌ Login failed:', loginResult.error);
    return false;
  }
};

const testUserProfile = async () => {
  console.log('\n👤 Testing User Profile...');
  
  // Get profile
  console.log('📋 Testing get profile...');
  const profileResult = await apiRequest('GET', '/api/users/profile');
  
  if (profileResult.success) {
    console.log('✅ Get profile successful');
  } else {
    console.log('❌ Get profile failed:', profileResult.error);
    return false;
  }

  // Update personal details
  console.log('✏️  Testing update personal details...');
  const updateResult = await apiRequest('PUT', '/api/users/profile/personal', {
    firstName: 'Test',
    lastName: 'User',
    age: 30,
    height: 175,
    city: 'Mumbai',
    state: 'Maharashtra'
  });

  if (updateResult.success) {
    console.log('✅ Update personal details successful');
  } else {
    console.log('❌ Update personal details failed:', updateResult.error);
  }

  return true;
};

const testSearch = async () => {
  console.log('\n🔍 Testing Search...');
  
  // Basic search
  console.log('🔎 Testing basic search...');
  const searchResult = await apiRequest('GET', '/api/search?page=1&limit=5');
  
  if (searchResult.success) {
    console.log('✅ Basic search successful');
    console.log(`   Found ${searchResult.data.data?.profiles?.length || 0} profiles`);
  } else {
    console.log('❌ Basic search failed:', searchResult.error);
    return false;
  }

  // Advanced search
  console.log('🔍 Testing advanced search...');
  const advancedSearchResult = await apiRequest('GET', '/api/search?ageMin=25&ageMax=35&religion=hindu');
  
  if (advancedSearchResult.success) {
    console.log('✅ Advanced search successful');
  } else {
    console.log('❌ Advanced search failed:', advancedSearchResult.error);
  }

  // Recommendations
  console.log('💡 Testing recommendations...');
  const recommendationsResult = await apiRequest('GET', '/api/search/recommendations');
  
  if (recommendationsResult.success) {
    console.log('✅ Recommendations successful');
  } else {
    console.log('❌ Recommendations failed:', recommendationsResult.error);
  }

  return true;
};

const testInterests = async () => {
  console.log('\n💝 Testing Interests...');
  
  // Get interests
  console.log('📋 Testing get interests...');
  const getInterestsResult = await apiRequest('GET', '/api/interests');
  
  if (getInterestsResult.success) {
    console.log('✅ Get interests successful');
  } else {
    console.log('❌ Get interests failed:', getInterestsResult.error);
    return false;
  }

  // Get interest stats
  console.log('📊 Testing interest stats...');
  const statsResult = await apiRequest('GET', '/api/interests/stats');
  
  if (statsResult.success) {
    console.log('✅ Interest stats successful');
  } else {
    console.log('❌ Interest stats failed:', statsResult.error);
  }

  return true;
};

const testMessages = async () => {
  console.log('\n💬 Testing Messages...');
  
  // Get conversations
  console.log('📋 Testing get conversations...');
  const conversationsResult = await apiRequest('GET', '/api/messages/conversations');
  
  if (conversationsResult.success) {
    console.log('✅ Get conversations successful');
  } else {
    console.log('❌ Get conversations failed:', conversationsResult.error);
    return false;
  }

  // Get message stats
  console.log('📊 Testing message stats...');
  const statsResult = await apiRequest('GET', '/api/messages/stats');
  
  if (statsResult.success) {
    console.log('✅ Message stats successful');
  } else {
    console.log('❌ Message stats failed:', statsResult.error);
  }

  return true;
};

const testSubscriptions = async () => {
  console.log('\n💳 Testing Subscriptions...');
  
  // Get subscription plans
  console.log('📋 Testing get subscription plans...');
  const plansResult = await apiRequest('GET', '/api/subscription/plans');
  
  if (plansResult.success) {
    console.log('✅ Get subscription plans successful');
    console.log(`   Found ${plansResult.data.data?.plans?.length || 0} plans`);
  } else {
    console.log('❌ Get subscription plans failed:', plansResult.error);
    return false;
  }

  // Get current subscription
  console.log('📋 Testing get current subscription...');
  const currentResult = await apiRequest('GET', '/api/subscription/current');
  
  if (currentResult.success) {
    console.log('✅ Get current subscription successful');
  } else {
    console.log('❌ Get current subscription failed:', currentResult.error);
  }

  // Get subscription features
  console.log('🎯 Testing get subscription features...');
  const featuresResult = await apiRequest('GET', '/api/subscription/features');
  
  if (featuresResult.success) {
    console.log('✅ Get subscription features successful');
  } else {
    console.log('❌ Get subscription features failed:', featuresResult.error);
  }

  return true;
};

const testAnalytics = async () => {
  console.log('\n📊 Testing Analytics...');
  
  // Get dashboard stats
  console.log('📋 Testing dashboard stats...');
  const dashboardResult = await apiRequest('GET', '/api/analytics/dashboard');
  
  if (dashboardResult.success) {
    console.log('✅ Dashboard stats successful');
  } else {
    console.log('❌ Dashboard stats failed:', dashboardResult.error);
    return false;
  }

  // Get profile views
  console.log('👁️  Testing profile views analytics...');
  const viewsResult = await apiRequest('GET', '/api/analytics/profile-views');
  
  if (viewsResult.success) {
    console.log('✅ Profile views analytics successful');
  } else {
    console.log('❌ Profile views analytics failed:', viewsResult.error);
  }

  return true;
};

// Main test runner
const runTests = async () => {
  console.log('🧪 Starting Comprehensive API Tests');
  console.log(`🎯 Target: ${BASE_URL}`);
  console.log('=' * 50);

  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'Authentication', fn: testAuthentication },
    { name: 'User Profile', fn: testUserProfile },
    { name: 'Search', fn: testSearch },
    { name: 'Interests', fn: testInterests },
    { name: 'Messages', fn: testMessages },
    { name: 'Subscriptions', fn: testSubscriptions },
    { name: 'Analytics', fn: testAnalytics }
  ];

  let passed = 0;
  let failed = 0;

  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} test crashed:`, error.message);
      failed++;
    }
  }

  console.log('\n' + '=' * 50);
  console.log('🏁 Test Results Summary');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);

  if (failed === 0) {
    console.log('\n🎉 All tests passed! API is working correctly.');
    process.exit(0);
  } else {
    console.log('\n⚠️  Some tests failed. Please check the logs above.');
    process.exit(1);
  }
};

// Run tests if called directly
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 Test runner crashed:', error);
    process.exit(1);
  });
}

module.exports = { runTests };
