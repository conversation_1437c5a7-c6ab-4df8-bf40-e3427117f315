// This file is missing, creating it now
import React from 'react';
import { 
  <PERSON>, 
  Card<PERSON>ody, 
  CardHeader, 
  <PERSON>ton, 
  Tabs, 
  Tab, 
  Input,
  Switch,
  Select,
  SelectItem,
  Divider,
  Checkbox
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = React.useState('account');
  
  // Account settings
  const [email, setEmail] = React.useState(user?.email || '');
  const [phone, setPhone] = React.useState(user?.phone || '');
  const [password, setPassword] = React.useState('');
  const [newPassword, setNewPassword] = React.useState('');
  const [confirmPassword, setConfirmPassword] = React.useState('');
  
  // Privacy settings
  const [profileVisibility, setProfileVisibility] = React.useState('all');
  const [showOnlineStatus, setShowOnlineStatus] = React.useState(true);
  const [showLastActive, setShowLastActive] = React.useState(true);
  const [showContactInfo, setShowContactInfo] = React.useState(false);
  
  // Notification settings
  const [emailNotifications, setEmailNotifications] = React.useState(true);
  const [smsNotifications, setSmsNotifications] = React.useState(true);
  const [pushNotifications, setPushNotifications] = React.useState(true);
  const [notifyNewMatches, setNotifyNewMatches] = React.useState(true);
  const [notifyInterests, setNotifyInterests] = React.useState(true);
  const [notifyMessages, setNotifyMessages] = React.useState(true);
  const [notifyProfileViews, setNotifyProfileViews] = React.useState(true);
  const [notifyPromotions, setNotifyPromotions] = React.useState(false);
  
  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };
  
  const handleSaveAccount = () => {
    console.log('Saving account settings');
    // Implement save logic
  };
  
  const handleSavePrivacy = () => {
    console.log('Saving privacy settings');
    // Implement save logic
  };
  
  const handleSaveNotifications = () => {
    console.log('Saving notification settings');
    // Implement save logic
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Settings</h1>
        
        <Card>
          <CardBody>
            <Tabs 
              aria-label="Settings" 
              selectedKey={activeTab} 
              onSelectionChange={handleTabChange}
              className="w-full"
            >
              <Tab 
                key="account" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:user" />
                    <span>Account</span>
                  </div>
                }
              >
                <div className="py-4 max-w-2xl mx-auto">
                  <h2 className="text-xl font-semibold mb-6">Account Settings</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-semibold mb-4">Personal Information</h3>
                      <div className="space-y-4">
                        <Input
                          label="Email"
                          type="email"
                          value={email}
                          onValueChange={setEmail}
                          startContent={<Icon icon="lucide:mail" className="text-default-400" />}
                        />
                        
                        <Input
                          label="Phone Number"
                          type="tel"
                          value={phone}
                          onValueChange={setPhone}
                          startContent={<Icon icon="lucide:phone" className="text-default-400" />}
                        />
                      </div>
                    </div>
                    
                    <Divider />
                    
                    <div>
                      <h3 className="text-md font-semibold mb-4">Change Password</h3>
                      <div className="space-y-4">
                        <Input
                          label="Current Password"
                          type="password"
                          value={password}
                          onValueChange={setPassword}
                          startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                        />
                        
                        <Input
                          label="New Password"
                          type="password"
                          value={newPassword}
                          onValueChange={setNewPassword}
                          startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                        />
                        
                        <Input
                          label="Confirm New Password"
                          type="password"
                          value={confirmPassword}
                          onValueChange={setConfirmPassword}
                          startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                        />
                      </div>
                    </div>
                    
                    <Divider />
                    
                    <div>
                      <h3 className="text-md font-semibold mb-4">Account Actions</h3>
                      <div className="space-y-4">
                        <Button 
                          color="danger" 
                          variant="flat"
                          startContent={<Icon icon="lucide:trash-2" />}
                        >
                          Delete Account
                        </Button>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <Button 
                        color="primary"
                        onPress={handleSaveAccount}
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </div>
              </Tab>
              
              <Tab 
                key="privacy" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:shield" />
                    <span>Privacy</span>
                  </div>
                }
              >
                <div className="py-4 max-w-2xl mx-auto">
                  <h2 className="text-xl font-semibold mb-6">Privacy Settings</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-semibold mb-4">Profile Visibility</h3>
                      <div className="space-y-4">
                        <Select
                          label="Who can view your profile"
                          selectedKeys={[profileVisibility]}
                          onSelectionChange={(keys) => {
                            const selected = Array.from(keys)[0] as string;
                            setProfileVisibility(selected);
                          }}
                        >
                          <SelectItem key="all" value="all">All Members</SelectItem>
                          <SelectItem key="premium" value="premium">Premium Members Only</SelectItem>
                          <SelectItem key="interests" value="interests">Members who sent interest</SelectItem>
                          <SelectItem key="none" value="none">No one (Hide profile)</SelectItem>
                        </Select>
                        
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">Show Online Status</p>
                            <p className="text-xs text-default-500">Let others know when you're online</p>
                          </div>
                          <Switch 
                            isSelected={showOnlineStatus}
                            onValueChange={setShowOnlineStatus}
                          />
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">Show Last Active Status</p>
                            <p className="text-xs text-default-500">Show when you were last active on the platform</p>
                          </div>
                          <Switch 
                            isSelected={showLastActive}
                            onValueChange={setShowLastActive}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <Divider />
                    
                    <div>
                      <h3 className="text-md font-semibold mb-4">Contact Information</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">Show Contact Information</p>
                            <p className="text-xs text-default-500">Allow members to see your phone and email</p>
                          </div>
                          <Switch 
                            isSelected={showContactInfo}
                            onValueChange={setShowContactInfo}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <Button 
                        color="primary"
                        onPress={handleSavePrivacy}
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </div>
              </Tab>
              
              <Tab 
                key="notifications" 
                title={
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:bell" />
                    <span>Notifications</span>
                  </div>
                }
              >
                <div className="py-4 max-w-2xl mx-auto">
                  <h2 className="text-xl font-semibold mb-6">Notification Settings</h2>
                  
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-md font-semibold mb-4">Notification Channels</h3>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">Email Notifications</p>
                            <p className="text-xs text-default-500">Receive notifications via email</p>
                          </div>
                          <Switch 
                            isSelected={emailNotifications}
                            onValueChange={setEmailNotifications}
                          />
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">SMS Notifications</p>
                            <p className="text-xs text-default-500">Receive notifications via SMS</p>
                          </div>
                          <Switch 
                            isSelected={smsNotifications}
                            onValueChange={setSmsNotifications}
                          />
                        </div>
                        
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-sm font-medium">Push Notifications</p>
                            <p className="text-xs text-default-500">Receive notifications on your device</p>
                          </div>
                          <Switch 
                            isSelected={pushNotifications}
                            onValueChange={setPushNotifications}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <Divider />
                    
                    <div>
                      <h3 className="text-md font-semibold mb-4">Notification Types</h3>
                      <div className="space-y-4">
                        <Checkbox 
                          isSelected={notifyNewMatches}
                          onValueChange={setNotifyNewMatches}
                        >
                          <div>
                            <p className="text-sm font-medium">New Matches</p>
                            <p className="text-xs text-default-500">Get notified when you have new matches</p>
                          </div>
                        </Checkbox>
                        
                        <Checkbox 
                          isSelected={notifyInterests}
                          onValueChange={setNotifyInterests}
                        >
                          <div>
                            <p className="text-sm font-medium">Interests</p>
                            <p className="text-xs text-default-500">Get notified when someone sends or accepts your interest</p>
                          </div>
                        </Checkbox>
                        
                        <Checkbox 
                          isSelected={notifyMessages}
                          onValueChange={setNotifyMessages}
                        >
                          <div>
                            <p className="text-sm font-medium">Messages</p>
                            <p className="text-xs text-default-500">Get notified when you receive new messages</p>
                          </div>
                        </Checkbox>
                        
                        <Checkbox 
                          isSelected={notifyProfileViews}
                          onValueChange={setNotifyProfileViews}
                        >
                          <div>
                            <p className="text-sm font-medium">Profile Views</p>
                            <p className="text-xs text-default-500">Get notified when someone views your profile</p>
                          </div>
                        </Checkbox>
                        
                        <Checkbox 
                          isSelected={notifyPromotions}
                          onValueChange={setNotifyPromotions}
                        >
                          <div>
                            <p className="text-sm font-medium">Promotions & Updates</p>
                            <p className="text-xs text-default-500">Get notified about special offers and platform updates</p>
                          </div>
                        </Checkbox>
                      </div>
                    </div>
                    
                    <div className="flex justify-end">
                      <Button 
                        color="primary"
                        onPress={handleSaveNotifications}
                      >
                        Save Changes
                      </Button>
                    </div>
                  </div>
                </div>
              </Tab>
            </Tabs>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};