const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');

class SQLiteDatabase {
  constructor() {
    this.dbPath = process.env.SQLITE_DB_PATH || './database/matrimony.db';
    this.db = null;
  }

  async initialize() {
    try {
      // Ensure database directory exists
      const dbDir = path.dirname(this.dbPath);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // Connect to database
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('❌ SQLite connection error:', err.message);
          throw err;
        }
        console.log('✅ Connected to SQLite database');
      });

      // Enable foreign keys
      await this.run('PRAGMA foreign_keys = ON');
      
      // Create tables
      await this.createTables();
      
      // Seed initial data
      await this.seedInitialData();

      console.log('✅ SQLite database initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ SQLite initialization failed:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        password TEXT NOT NULL,
        phone TEXT,
        profile_type TEXT DEFAULT 'self',
        membership_type TEXT DEFAULT 'free',
        is_active BOOLEAN DEFAULT 1,
        is_blocked BOOLEAN DEFAULT 0,
        profile_completed BOOLEAN DEFAULT 0,
        profile_completion_percentage INTEGER DEFAULT 0,
        profile_picture TEXT,
        profile_photos TEXT, -- JSON array
        personal_info TEXT, -- JSON object
        family_info TEXT, -- JSON object
        education_career TEXT, -- JSON object
        lifestyle TEXT, -- JSON object
        religious_info TEXT, -- JSON object
        partner_preferences TEXT, -- JSON object
        verification_status TEXT, -- JSON object
        premium_features TEXT, -- JSON object
        horoscope TEXT, -- JSON object
        blocked_users TEXT, -- JSON array
        shortlisted_profiles TEXT, -- JSON array
        notification_preferences TEXT, -- JSON object
        profile_views INTEGER DEFAULT 0,
        last_active DATETIME DEFAULT CURRENT_TIMESTAMP,
        subscription_expiry DATETIME,
        email_verification_token TEXT,
        email_verified_at DATETIME,
        phone_verification_otp TEXT,
        phone_verification_otp_expiry DATETIME,
        phone_verified_at DATETIME,
        password_reset_token TEXT,
        password_reset_expiry DATETIME,
        is_admin BOOLEAN DEFAULT 0,
        block_reason TEXT,
        blocked_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Interests table
      `CREATE TABLE IF NOT EXISTS interests (
        id TEXT PRIMARY KEY,
        from_user_id TEXT NOT NULL,
        to_user_id TEXT NOT NULL,
        type TEXT DEFAULT 'like',
        status TEXT DEFAULT 'pending',
        message TEXT,
        response_message TEXT,
        responded_at DATETIME,
        expires_at DATETIME,
        viewed_at DATETIME,
        metadata TEXT, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (to_user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE(from_user_id, to_user_id)
      )`,

      // Conversations table
      `CREATE TABLE IF NOT EXISTS conversations (
        id TEXT PRIMARY KEY,
        participants TEXT NOT NULL, -- JSON array of user IDs
        last_message_id TEXT,
        last_message_time DATETIME,
        is_active BOOLEAN DEFAULT 1,
        unread_count TEXT, -- JSON object {userId: count}
        muted_by TEXT, -- JSON array
        blocked_by TEXT, -- JSON array
        metadata TEXT, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Messages table
      `CREATE TABLE IF NOT EXISTS messages (
        id TEXT PRIMARY KEY,
        conversation_id TEXT NOT NULL,
        sender_id TEXT NOT NULL,
        receiver_id TEXT NOT NULL,
        content TEXT NOT NULL,
        message_type TEXT DEFAULT 'text',
        attachment_url TEXT,
        reply_to_id TEXT,
        read_at DATETIME,
        delivered_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        deleted BOOLEAN DEFAULT 0,
        deleted_at DATETIME,
        deleted_by TEXT,
        metadata TEXT, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (reply_to_id) REFERENCES messages(id) ON DELETE SET NULL,
        FOREIGN KEY (deleted_by) REFERENCES users(id) ON DELETE SET NULL
      )`,

      // Notifications table
      `CREATE TABLE IF NOT EXISTS notifications (
        id TEXT PRIMARY KEY,
        recipient_id TEXT NOT NULL,
        sender_id TEXT,
        type TEXT NOT NULL,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        data TEXT, -- JSON object
        read BOOLEAN DEFAULT 0,
        read_at DATETIME,
        priority TEXT DEFAULT 'medium',
        category TEXT DEFAULT 'social',
        action_url TEXT,
        action_text TEXT,
        image_url TEXT,
        expires_at DATETIME,
        delivery_status TEXT, -- JSON object
        metadata TEXT, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (recipient_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE SET NULL
      )`,

      // Success stories table
      `CREATE TABLE IF NOT EXISTS success_stories (
        id TEXT PRIMARY KEY,
        couple_names TEXT NOT NULL,
        story TEXT NOT NULL,
        testimonial TEXT,
        marriage_date DATE NOT NULL,
        wedding_location TEXT,
        photos TEXT, -- JSON array
        how_they_met TEXT DEFAULT 'through_platform',
        timeline_events TEXT, -- JSON array
        tags TEXT, -- JSON array
        groom_id TEXT,
        bride_id TEXT,
        submitted_by TEXT NOT NULL,
        is_public BOOLEAN DEFAULT 0,
        is_approved BOOLEAN DEFAULT 0,
        approved_by TEXT,
        approved_at DATETIME,
        featured BOOLEAN DEFAULT 0,
        featured_until DATETIME,
        views INTEGER DEFAULT 0,
        likes TEXT, -- JSON array of user IDs
        like_count INTEGER DEFAULT 0,
        comments TEXT, -- JSON array
        comment_count INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (groom_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (bride_id) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (submitted_by) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL
      )`,

      // Video calls table
      `CREATE TABLE IF NOT EXISTS video_calls (
        id TEXT PRIMARY KEY,
        caller_id TEXT NOT NULL,
        callee_id TEXT NOT NULL,
        room_id TEXT UNIQUE NOT NULL,
        status TEXT DEFAULT 'initiated',
        call_type TEXT DEFAULT 'video',
        duration INTEGER DEFAULT 0,
        start_time DATETIME,
        end_time DATETIME,
        quality TEXT, -- JSON object
        recording TEXT, -- JSON object
        metadata TEXT, -- JSON object
        scheduled_for DATETIME,
        is_scheduled BOOLEAN DEFAULT 0,
        reminder_sent BOOLEAN DEFAULT 0,
        privacy TEXT, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (caller_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (callee_id) REFERENCES users(id) ON DELETE CASCADE
      )`
    ];

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_users_membership ON users(membership_type)',
      'CREATE INDEX IF NOT EXISTS idx_users_last_active ON users(last_active)',
      'CREATE INDEX IF NOT EXISTS idx_interests_from_user ON interests(from_user_id)',
      'CREATE INDEX IF NOT EXISTS idx_interests_to_user ON interests(to_user_id)',
      'CREATE INDEX IF NOT EXISTS idx_interests_status ON interests(status)',
      'CREATE INDEX IF NOT EXISTS idx_messages_conversation ON messages(conversation_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_sender ON messages(sender_id)',
      'CREATE INDEX IF NOT EXISTS idx_messages_created ON messages(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_recipient ON notifications(recipient_id)',
      'CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read)',
      'CREATE INDEX IF NOT EXISTS idx_video_calls_caller ON video_calls(caller_id)',
      'CREATE INDEX IF NOT EXISTS idx_video_calls_callee ON video_calls(callee_id)'
    ];

    for (const index of indexes) {
      await this.run(index);
    }

    console.log('✅ SQLite tables and indexes created');
  }

  async seedInitialData() {
    try {
      // Check if admin user exists
      const adminExists = await this.get('SELECT id FROM users WHERE is_admin = 1 LIMIT 1');
      
      if (!adminExists) {
        // Create admin user
        const adminPassword = await bcrypt.hash(process.env.ADMIN_PASSWORD || 'admin123456', 12);
        const adminId = 'admin-' + Date.now();
        
        await this.run(`
          INSERT INTO users (
            id, name, email, password, phone, is_admin, profile_completed,
            profile_completion_percentage, membership_type, verification_status,
            personal_info, education_career, religious_info, created_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          adminId,
          'Admin User',
          process.env.ADMIN_EMAIL || '<EMAIL>',
          adminPassword,
          '+91 9999999999',
          1,
          1,
          100,
          'platinum',
          JSON.stringify({ email: true, phone: true, photo: true, document: true }),
          JSON.stringify({
            dateOfBirth: '1990-01-01',
            age: 34,
            height: 175,
            maritalStatus: 'never_married',
            motherTongue: 'hindi',
            city: 'Mumbai',
            state: 'Maharashtra',
            country: 'India'
          }),
          JSON.stringify({
            highestEducation: 'Masters',
            occupation: 'System Administrator',
            annualIncome: '10+ Lakhs'
          }),
          JSON.stringify({
            religion: 'hindu',
            caste: 'other',
            manglik: 'no'
          }),
          new Date().toISOString()
        ]);

        console.log('✅ Admin user created');
      }

      // Seed sample data if requested
      if (process.env.SEED_DATA === 'true') {
        await this.seedSampleUsers();
      }

    } catch (error) {
      console.error('❌ Error seeding initial data:', error);
    }
  }

  async seedSampleUsers() {
    const sampleUsers = [
      {
        name: 'Anjali Sharma',
        email: '<EMAIL>',
        phone: '+91 9876543210',
        membershipType: 'gold',
        personalInfo: {
          age: 28,
          height: 165,
          maritalStatus: 'never_married',
          motherTongue: 'hindi',
          city: 'Mumbai',
          state: 'Maharashtra',
          country: 'India'
        },
        educationCareer: {
          highestEducation: 'B.Tech',
          occupation: 'Software Engineer',
          annualIncome: '8-10 Lakhs'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'brahmin',
          manglik: 'no'
        }
      },
      {
        name: 'Neha Patel',
        email: '<EMAIL>',
        phone: '+91 9876543211',
        membershipType: 'silver',
        personalInfo: {
          age: 26,
          height: 160,
          maritalStatus: 'never_married',
          motherTongue: 'gujarati',
          city: 'Bangalore',
          state: 'Karnataka',
          country: 'India'
        },
        educationCareer: {
          highestEducation: 'M.Sc',
          occupation: 'Data Scientist',
          annualIncome: '6-8 Lakhs'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'patel',
          manglik: 'anshik'
        }
      }
    ];

    for (const userData of sampleUsers) {
      const userId = 'user-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
      const hashedPassword = await bcrypt.hash('password123', 12);

      await this.run(`
        INSERT INTO users (
          id, name, email, password, phone, membership_type,
          profile_completed, profile_completion_percentage,
          personal_info, education_career, religious_info,
          verification_status, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        userId,
        userData.name,
        userData.email,
        hashedPassword,
        userData.phone,
        userData.membershipType,
        1,
        85,
        JSON.stringify(userData.personalInfo),
        JSON.stringify(userData.educationCareer),
        JSON.stringify(userData.religiousInfo),
        JSON.stringify({ email: true, phone: true, photo: true, document: false }),
        new Date().toISOString()
      ]);
    }

    console.log(`✅ Created ${sampleUsers.length} sample users`);
  }

  // Helper methods for database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) reject(err);
        else resolve({ id: this.lastID, changes: this.changes });
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) reject(err);
        else resolve(rows);
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) reject(err);
        else {
          console.log('✅ SQLite database connection closed');
          resolve();
        }
      });
    });
  }
}

module.exports = SQLiteDatabase;
