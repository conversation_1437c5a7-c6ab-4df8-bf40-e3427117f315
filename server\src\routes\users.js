const express = require("express");
const { body, validationResult } = require("express-validator");
const User = require("../models/User");
const { authenticateToken } = require("../middleware/auth");

const router = express.Router();

// Update profile picture
router.put(
  "/profile-picture",
  [
    authenticateToken,
    body("profilePicture").isURL().withMessage("Invalid profile picture URL"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { profilePicture } = req.body;
      const user = req.user;

      user.profilePicture = profilePicture;
      user.calculateProfileCompletion();

      await user.save();

      res.json({
        success: true,
        message: "Profile picture updated successfully",
        data: {
          profilePicture: user.profilePicture,
          profileCompletionPercentage: user.profileCompletionPercentage,
        },
      });
    } catch (error) {
      console.error("Update profile picture error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update profile picture",
      });
    }
  }
);

// Add profile photos
router.post(
  "/profile-photos",
  [
    authenticateToken,
    body("photos")
      .isArray({ min: 1, max: 10 })
      .withMessage("Photos must be an array with 1-10 items"),
    body("photos.*").isURL().withMessage("Each photo must be a valid URL"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { photos } = req.body;
      const user = req.user;

      // Add new photos to existing ones
      user.profilePhotos = [...(user.profilePhotos || []), ...photos];

      // Limit to maximum 10 photos
      if (user.profilePhotos.length > 10) {
        user.profilePhotos = user.profilePhotos.slice(0, 10);
      }

      await user.save();

      res.json({
        success: true,
        message: "Profile photos added successfully",
        data: {
          profilePhotos: user.profilePhotos,
        },
      });
    } catch (error) {
      console.error("Add profile photos error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to add profile photos",
      });
    }
  }
);

// Remove profile photo
router.delete("/profile-photos", authenticateToken, async (req, res) => {
  try {
    const { photoUrl } = req.body;
    const user = req.user;

    if (!user.profilePhotos || !user.profilePhotos.includes(photoUrl)) {
      return res.status(404).json({
        success: false,
        message: "Photo not found",
      });
    }

    user.profilePhotos = user.profilePhotos.filter(
      (photo) => photo !== photoUrl
    );
    await user.save();

    res.json({
      success: true,
      message: "Profile photo removed successfully",
      data: {
        profilePhotos: user.profilePhotos,
      },
    });
  } catch (error) {
    console.error("Remove profile photo error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to remove profile photo",
    });
  }
});

// Block user
router.post("/block/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    if (currentUser._id.toString() === userId) {
      return res.status(400).json({
        success: false,
        message: "You cannot block yourself",
      });
    }

    const userToBlock = await User.findById(userId);
    if (!userToBlock) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (!currentUser.blockedUsers.includes(userId)) {
      currentUser.blockedUsers.push(userId);
      await currentUser.save();
    }

    res.json({
      success: true,
      message: "User blocked successfully",
    });
  } catch (error) {
    console.error("Block user error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to block user",
    });
  }
});

// Unblock user
router.delete("/block/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    currentUser.blockedUsers = currentUser.blockedUsers.filter(
      (id) => id.toString() !== userId
    );
    await currentUser.save();

    res.json({
      success: true,
      message: "User unblocked successfully",
    });
  } catch (error) {
    console.error("Unblock user error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to unblock user",
    });
  }
});

// Get blocked users
router.get("/blocked", authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate(
      "blockedUsers",
      "name profilePicture personalInfo.age personalInfo.city"
    );

    res.json({
      success: true,
      data: {
        blockedUsers: user.blockedUsers,
      },
    });
  } catch (error) {
    console.error("Get blocked users error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get blocked users",
    });
  }
});

// Shortlist profile
router.post("/shortlist/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    if (currentUser._id.toString() === userId) {
      return res.status(400).json({
        success: false,
        message: "You cannot shortlist yourself",
      });
    }

    const userToShortlist = await User.findById(userId);
    if (!userToShortlist) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    if (!currentUser.shortlistedProfiles.includes(userId)) {
      currentUser.shortlistedProfiles.push(userId);
      await currentUser.save();
    }

    res.json({
      success: true,
      message: "Profile shortlisted successfully",
    });
  } catch (error) {
    console.error("Shortlist profile error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to shortlist profile",
    });
  }
});

// Remove from shortlist
router.delete("/shortlist/:userId", authenticateToken, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    currentUser.shortlistedProfiles = currentUser.shortlistedProfiles.filter(
      (id) => id.toString() !== userId
    );
    await currentUser.save();

    res.json({
      success: true,
      message: "Profile removed from shortlist successfully",
    });
  } catch (error) {
    console.error("Remove from shortlist error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to remove from shortlist",
    });
  }
});

// Get shortlisted profiles
router.get("/shortlist", authenticateToken, async (req, res) => {
  try {
    const user = await User.findById(req.user._id).populate(
      "shortlistedProfiles",
      "name profilePicture membershipType verificationStatus personalInfo familyInfo educationCareer lifestyle religiousInfo profileViews lastActive"
    );

    res.json({
      success: true,
      data: {
        shortlistedProfiles: user.shortlistedProfiles,
      },
    });
  } catch (error) {
    console.error("Get shortlisted profiles error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get shortlisted profiles",
    });
  }
});

// Update account settings
router.put(
  "/settings",
  [
    authenticateToken,
    body("email")
      .optional()
      .isEmail()
      .normalizeEmail()
      .withMessage("Invalid email"),
    body("phone")
      .optional()
      .isMobilePhone("en-IN")
      .withMessage("Invalid phone number"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { email, phone, profileType } = req.body;
      const user = req.user;

      // Check if email is already taken by another user
      if (email && email !== user.email) {
        const existingUser = await User.findOne({
          email,
          _id: { $ne: user._id },
        });
        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: "Email already taken by another user",
          });
        }
        user.email = email;
        user.verificationStatus.email = false; // Reset email verification
      }

      // Check if phone is already taken by another user
      if (phone && phone !== user.phone) {
        const existingUser = await User.findOne({
          phone,
          _id: { $ne: user._id },
        });
        if (existingUser) {
          return res.status(400).json({
            success: false,
            message: "Phone number already taken by another user",
          });
        }
        user.phone = phone;
        user.verificationStatus.phone = false; // Reset phone verification
      }

      if (profileType) {
        user.profileType = profileType;
      }

      await user.save();

      res.json({
        success: true,
        message: "Account settings updated successfully",
        data: {
          user: {
            email: user.email,
            phone: user.phone,
            profileType: user.profileType,
            verificationStatus: user.verificationStatus,
          },
        },
      });
    } catch (error) {
      console.error("Update settings error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update account settings",
      });
    }
  }
);

// Deactivate account
router.put("/deactivate", authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    user.isActive = false;
    await user.save();

    res.json({
      success: true,
      message: "Account deactivated successfully",
    });
  } catch (error) {
    console.error("Deactivate account error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to deactivate account",
    });
  }
});

// Reactivate account
router.put("/reactivate", authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    user.isActive = true;
    await user.save();

    res.json({
      success: true,
      message: "Account reactivated successfully",
    });
  } catch (error) {
    console.error("Reactivate account error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to reactivate account",
    });
  }
});

module.exports = router;
