const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const { authenticateToken } = require("../middleware/auth");

const router = express.Router();

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, "../../uploads");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
}

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    let uploadPath = uploadsDir;

    // Create subdirectories based on file type
    if (
      file.fieldname === "profilePicture" ||
      file.fieldname === "profilePhotos"
    ) {
      uploadPath = path.join(uploadsDir, "profiles");
    } else if (file.fieldname === "documents") {
      uploadPath = path.join(uploadsDir, "documents");
    } else if (file.fieldname === "horoscope") {
      uploadPath = path.join(uploadsDir, "horoscope");
    }

    // Create directory if it doesn't exist
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // Generate unique filename
    const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
    const extension = path.extname(file.originalname);
    const filename = `${req.user._id}-${uniqueSuffix}${extension}`;
    cb(null, filename);
  },
});

// File filter
const fileFilter = (req, file, cb) => {
  // Check file type based on field name
  if (
    file.fieldname === "profilePicture" ||
    file.fieldname === "profilePhotos"
  ) {
    // Only allow image files
    if (file.mimetype.startsWith("image/")) {
      cb(null, true);
    } else {
      cb(new Error("Only image files are allowed for profile pictures"), false);
    }
  } else if (file.fieldname === "documents") {
    // Allow images and PDFs for documents
    if (
      file.mimetype.startsWith("image/") ||
      file.mimetype === "application/pdf"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error("Only image and PDF files are allowed for documents"),
        false
      );
    }
  } else if (file.fieldname === "horoscope") {
    // Allow images and PDFs for horoscope
    if (
      file.mimetype.startsWith("image/") ||
      file.mimetype === "application/pdf"
    ) {
      cb(null, true);
    } else {
      cb(
        new Error("Only image and PDF files are allowed for horoscope"),
        false
      );
    }
  } else {
    cb(new Error("Invalid field name"), false);
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: parseInt(process.env.MAX_FILE_SIZE) || 5 * 1024 * 1024, // 5MB default
    files: 10, // Maximum 10 files per request
  },
});

// Upload profile picture
router.post(
  "/profile-picture",
  [authenticateToken, upload.single("profilePicture")],
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      const fileUrl = `/uploads/profiles/${req.file.filename}`;

      res.json({
        success: true,
        message: "Profile picture uploaded successfully",
        data: {
          url: fileUrl,
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
        },
      });
    } catch (error) {
      console.error("Upload profile picture error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload profile picture",
      });
    }
  }
);

// Upload multiple profile photos
router.post(
  "/profile-photos",
  [authenticateToken, upload.array("profilePhotos", 10)],
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No files uploaded",
        });
      }

      const uploadedFiles = req.files.map((file) => ({
        url: `/uploads/profiles/${file.filename}`,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
      }));

      res.json({
        success: true,
        message: "Profile photos uploaded successfully",
        data: {
          files: uploadedFiles,
          count: uploadedFiles.length,
        },
      });
    } catch (error) {
      console.error("Upload profile photos error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload profile photos",
      });
    }
  }
);

// Upload verification documents
router.post(
  "/documents",
  [authenticateToken, upload.array("documents", 5)],
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No files uploaded",
        });
      }

      const { documentType } = req.body; // e.g., 'id_proof', 'education_certificate', 'income_proof'

      const uploadedFiles = req.files.map((file) => ({
        url: `/uploads/documents/${file.filename}`,
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        documentType,
      }));

      res.json({
        success: true,
        message: "Documents uploaded successfully",
        data: {
          files: uploadedFiles,
          count: uploadedFiles.length,
          documentType,
        },
      });
    } catch (error) {
      console.error("Upload documents error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload documents",
      });
    }
  }
);

// Upload horoscope
router.post(
  "/horoscope",
  [authenticateToken, upload.single("horoscope")],
  async (req, res) => {
    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      const fileUrl = `/uploads/horoscope/${req.file.filename}`;

      res.json({
        success: true,
        message: "Horoscope uploaded successfully",
        data: {
          url: fileUrl,
          filename: req.file.filename,
          originalName: req.file.originalname,
          size: req.file.size,
        },
      });
    } catch (error) {
      console.error("Upload horoscope error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to upload horoscope",
      });
    }
  }
);

// Delete uploaded file
router.delete("/file", authenticateToken, async (req, res) => {
  try {
    const { filename, fileType } = req.body; // 'profiles', 'documents', 'horoscope'

    if (!fileType) {
      return res.status(400).json({
        success: false,
        message: "File type is required",
      });
    }

    // Verify the file belongs to the current user
    if (!filename.startsWith(req.user._id.toString())) {
      return res.status(403).json({
        success: false,
        message: "You can only delete your own files",
      });
    }

    const filePath = path.join(uploadsDir, fileType, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: "File not found",
      });
    }

    // Delete the file
    fs.unlinkSync(filePath);

    res.json({
      success: true,
      message: "File deleted successfully",
    });
  } catch (error) {
    console.error("Delete file error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete file",
    });
  }
});

// Get file info
router.post("/file/info", authenticateToken, async (req, res) => {
  try {
    const { filename, fileType } = req.body;

    if (!fileType) {
      return res.status(400).json({
        success: false,
        message: "File type is required",
      });
    }

    const filePath = path.join(uploadsDir, fileType, filename);

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: "File not found",
      });
    }

    const stats = fs.statSync(filePath);

    res.json({
      success: true,
      data: {
        filename,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        url: `/uploads/${fileType}/${filename}`,
      },
    });
  } catch (error) {
    console.error("Get file info error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get file information",
    });
  }
});

// Get user's uploaded files
router.get("/my-files", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id.toString();
    const files = {
      profiles: [],
      documents: [],
      horoscope: [],
    };

    // Scan each directory for user's files
    const directories = ["profiles", "documents", "horoscope"];

    directories.forEach((dir) => {
      const dirPath = path.join(uploadsDir, dir);
      if (fs.existsSync(dirPath)) {
        const dirFiles = fs.readdirSync(dirPath);
        const userFiles = dirFiles.filter((filename) =>
          filename.startsWith(userId)
        );

        files[dir] = userFiles.map((filename) => {
          const filePath = path.join(dirPath, filename);
          const stats = fs.statSync(filePath);

          return {
            filename,
            url: `/uploads/${dir}/${filename}`,
            size: stats.size,
            createdAt: stats.birthtime,
            modifiedAt: stats.mtime,
          };
        });
      }
    });

    res.json({
      success: true,
      data: {
        files,
        totalFiles:
          files.profiles.length +
          files.documents.length +
          files.horoscope.length,
      },
    });
  } catch (error) {
    console.error("Get user files error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get user files",
    });
  }
});

module.exports = router;
