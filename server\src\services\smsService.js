const twilio = require('twilio');

// Initialize Twilio client
const createTwilioClient = () => {
  if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
    throw new Error('Twilio credentials not configured');
  }

  return twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
};

// SMS templates
const smsTemplates = {
  phoneVerification: (data) => 
    `Your Matrimony Platform verification OTP is: ${data.otp}. Valid for 10 minutes. Do not share this OTP with anyone.`,
  
  passwordReset: (data) => 
    `Your password reset OTP for Matrimony Platform is: ${data.otp}. Valid for 10 minutes. If you didn't request this, please ignore.`,
  
  newInterest: (data) => 
    `New interest received from ${data.senderName} on Matrimony Platform! Login to view and respond. ${data.profileLink}`,
  
  interestAccepted: (data) => 
    `Great news! ${data.receiverName} accepted your interest on Matrimony Platform. You can now start messaging. ${data.chatLink}`,
  
  subscriptionActivated: (data) => 
    `Your ${data.planName} subscription is now active! Enjoy premium features on Matrimony Platform. Login to explore.`,
  
  profileVerified: (data) => 
    `Congratulations! Your profile has been verified on Matrimony Platform. Verified profiles get better visibility.`,
  
  reminderIncompleteProfile: (data) => 
    `Complete your profile on Matrimony Platform to get better matches. Your profile is ${data.completionPercentage}% complete.`,
  
  subscriptionExpiring: (data) => 
    `Your ${data.planName} subscription expires in ${data.daysRemaining} days. Renew now to continue enjoying premium features.`,
  
  newMessage: (data) => 
    `New message from ${data.senderName} on Matrimony Platform: "${data.messagePreview}..." Login to reply.`
};

// Send SMS function
const sendSMS = async ({ to, message, template, data }) => {
  try {
    const client = createTwilioClient();

    let smsContent = message;

    // Use template if provided
    if (template && smsTemplates[template]) {
      smsContent = smsTemplates[template](data);
    }

    // Ensure phone number is in international format
    let phoneNumber = to;
    if (!phoneNumber.startsWith('+')) {
      // Assume Indian number if no country code
      phoneNumber = phoneNumber.startsWith('91') ? `+${phoneNumber}` : `+91${phoneNumber}`;
    }

    const result = await client.messages.create({
      body: smsContent,
      from: process.env.TWILIO_PHONE_NUMBER,
      to: phoneNumber
    });

    console.log('SMS sent successfully:', result.sid);
    return {
      success: true,
      sid: result.sid,
      status: result.status
    };
  } catch (error) {
    console.error('SMS sending failed:', error);
    throw new Error(`Failed to send SMS: ${error.message}`);
  }
};

// Send bulk SMS
const sendBulkSMS = async (messages) => {
  try {
    const results = [];

    for (const sms of messages) {
      try {
        const result = await sendSMS(sms);
        results.push({ ...sms, success: true, sid: result.sid });
      } catch (error) {
        results.push({ ...sms, success: false, error: error.message });
      }
    }

    return results;
  } catch (error) {
    console.error('Bulk SMS sending failed:', error);
    throw new Error(`Failed to send bulk SMS: ${error.message}`);
  }
};

// Send OTP
const sendOTP = async ({ to, otp, purpose = 'verification' }) => {
  try {
    let template = 'phoneVerification';
    
    if (purpose === 'password_reset') {
      template = 'passwordReset';
    }

    const result = await sendSMS({
      to,
      template,
      data: { otp }
    });

    return result;
  } catch (error) {
    console.error('OTP sending failed:', error);
    throw new Error(`Failed to send OTP: ${error.message}`);
  }
};

// Verify phone number format
const verifyPhoneNumber = (phoneNumber) => {
  // Remove all non-digit characters
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  // Check if it's a valid Indian mobile number
  const indianMobileRegex = /^(\+91|91)?[6-9]\d{9}$/;
  
  if (indianMobileRegex.test(cleaned)) {
    return {
      isValid: true,
      formatted: cleaned.startsWith('91') ? `+${cleaned}` : `+91${cleaned}`,
      country: 'IN'
    };
  }
  
  // Check if it's an international number (basic validation)
  if (cleaned.length >= 10 && cleaned.length <= 15) {
    return {
      isValid: true,
      formatted: cleaned.startsWith('+') ? cleaned : `+${cleaned}`,
      country: 'UNKNOWN'
    };
  }
  
  return {
    isValid: false,
    error: 'Invalid phone number format'
  };
};

// Get SMS delivery status
const getSMSStatus = async (messageSid) => {
  try {
    const client = createTwilioClient();
    const message = await client.messages(messageSid).fetch();
    
    return {
      sid: message.sid,
      status: message.status,
      errorCode: message.errorCode,
      errorMessage: message.errorMessage,
      dateCreated: message.dateCreated,
      dateSent: message.dateSent,
      dateUpdated: message.dateUpdated
    };
  } catch (error) {
    console.error('Failed to get SMS status:', error);
    throw new Error(`Failed to get SMS status: ${error.message}`);
  }
};

// Verify Twilio configuration
const verifyTwilioConfig = async () => {
  try {
    const client = createTwilioClient();
    const account = await client.api.accounts(process.env.TWILIO_ACCOUNT_SID).fetch();
    
    console.log('Twilio configuration verified successfully');
    console.log('Account SID:', account.sid);
    console.log('Account Status:', account.status);
    
    return {
      success: true,
      accountSid: account.sid,
      status: account.status
    };
  } catch (error) {
    console.error('Twilio configuration verification failed:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Get account balance (if needed)
const getAccountBalance = async () => {
  try {
    const client = createTwilioClient();
    const balance = await client.balance.fetch();
    
    return {
      balance: balance.balance,
      currency: balance.currency
    };
  } catch (error) {
    console.error('Failed to get account balance:', error);
    throw new Error(`Failed to get account balance: ${error.message}`);
  }
};

// SMS rate limiting helper
class SMSRateLimiter {
  constructor() {
    this.attempts = new Map();
    this.maxAttempts = 3;
    this.windowMs = 60 * 60 * 1000; // 1 hour
  }

  canSendSMS(phoneNumber) {
    const now = Date.now();
    const key = phoneNumber;
    
    if (!this.attempts.has(key)) {
      this.attempts.set(key, []);
    }
    
    const attempts = this.attempts.get(key);
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(timestamp => now - timestamp < this.windowMs);
    this.attempts.set(key, validAttempts);
    
    return validAttempts.length < this.maxAttempts;
  }

  recordAttempt(phoneNumber) {
    const now = Date.now();
    const key = phoneNumber;
    
    if (!this.attempts.has(key)) {
      this.attempts.set(key, []);
    }
    
    this.attempts.get(key).push(now);
  }

  getRemainingAttempts(phoneNumber) {
    const key = phoneNumber;
    
    if (!this.attempts.has(key)) {
      return this.maxAttempts;
    }
    
    const now = Date.now();
    const attempts = this.attempts.get(key);
    const validAttempts = attempts.filter(timestamp => now - timestamp < this.windowMs);
    
    return Math.max(0, this.maxAttempts - validAttempts.length);
  }
}

const rateLimiter = new SMSRateLimiter();

module.exports = {
  sendSMS,
  sendBulkSMS,
  sendOTP,
  verifyPhoneNumber,
  getSMSStatus,
  verifyTwilioConfig,
  getAccountBalance,
  rateLimiter,
  smsTemplates
};
