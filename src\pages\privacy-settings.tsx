import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Button,
  Switch,
  Select,
  SelectItem,
  Divider,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dalBody,
  Modal<PERSON>ooter,
  useDisclosure,
  Avatar,
  Input
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { useToast } from '../contexts/toast-context';

interface PrivacySettings {
  profileVisibility: 'public' | 'members_only' | 'premium_only';
  photoVisibility: 'public' | 'members_only' | 'premium_only' | 'private';
  contactInfoVisibility: 'members_only' | 'premium_only' | 'private';
  lastSeenVisibility: 'public' | 'members_only' | 'private';
  showOnlineStatus: boolean;
  allowMessages: 'all' | 'premium_only' | 'mutual_interests_only';
  allowInterests: 'all' | 'premium_only' | 'verified_only';
  allowPhotoRequests: boolean;
  allowContactRequests: 'all' | 'premium_only' | 'mutual_interests_only';
  showInSearch: boolean;
  showProfileViews: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
}

interface BlockedUser {
  userId: string;
  userName: string;
  reason: string;
  blockedAt: string;
}

export const PrivacySettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const { isOpen: isBlockedOpen, onOpen: onBlockedOpen, onClose: onBlockedClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();

  const [settings, setSettings] = React.useState<PrivacySettings>({
    profileVisibility: 'members_only',
    photoVisibility: 'members_only',
    contactInfoVisibility: 'premium_only',
    lastSeenVisibility: 'members_only',
    showOnlineStatus: true,
    allowMessages: 'all',
    allowInterests: 'all',
    allowPhotoRequests: true,
    allowContactRequests: 'premium_only',
    showInSearch: true,
    showProfileViews: true,
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true
  });

  const [blockedUsers, setBlockedUsers] = React.useState<BlockedUser[]>([
    {
      userId: 'user_123',
      userName: 'John Doe',
      reason: 'Inappropriate behavior',
      blockedAt: '2024-01-15T10:30:00Z'
    }
  ]);

  const [loading, setLoading] = React.useState(false);
  const [deleteReason, setDeleteReason] = React.useState('');
  const [deletePassword, setDeletePassword] = React.useState('');

  // Load privacy settings
  React.useEffect(() => {
    loadPrivacySettings();
  }, []);

  const loadPrivacySettings = async () => {
    try {
      // Mock API call - replace with actual API
      // const response = await privacyAPI.getSettings();
      // setSettings(response.data.settings);
    } catch (error) {
      console.error('Failed to load privacy settings:', error);
    }
  };

  const updateSettings = async (newSettings: Partial<PrivacySettings>) => {
    try {
      setLoading(true);
      const updatedSettings = { ...settings, ...newSettings };
      setSettings(updatedSettings);
      
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      showToast('Privacy settings updated successfully', 'success');
    } catch (error) {
      showToast('Failed to update privacy settings', 'error');
    } finally {
      setLoading(false);
    }
  };

  const unblockUser = async (userId: string) => {
    try {
      setBlockedUsers(prev => prev.filter(user => user.userId !== userId));
      showToast('User unblocked successfully', 'success');
    } catch (error) {
      showToast('Failed to unblock user', 'error');
    }
  };

  const requestAccountDeletion = async () => {
    if (!deleteReason || !deletePassword) {
      showToast('Please fill in all required fields', 'error');
      return;
    }

    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast('Account deletion request submitted. You will receive a confirmation email.', 'success');
      onDeleteClose();
      setDeleteReason('');
      setDeletePassword('');
    } catch (error) {
      showToast('Failed to submit deletion request', 'error');
    } finally {
      setLoading(false);
    }
  };

  const visibilityOptions = [
    { key: 'public', label: 'Everyone' },
    { key: 'members_only', label: 'Members Only' },
    { key: 'premium_only', label: 'Premium Members Only' },
    { key: 'private', label: 'Private' }
  ];

  const messageOptions = [
    { key: 'all', label: 'Everyone' },
    { key: 'premium_only', label: 'Premium Members Only' },
    { key: 'mutual_interests_only', label: 'Mutual Interests Only' }
  ];

  const interestOptions = [
    { key: 'all', label: 'Everyone' },
    { key: 'premium_only', label: 'Premium Members Only' },
    { key: 'verified_only', label: 'Verified Members Only' }
  ];

  const deleteReasons = [
    'Found my life partner',
    'Not satisfied with matches',
    'Privacy concerns',
    'Too many unwanted contacts',
    'Technical issues',
    'Cost concerns',
    'Other'
  ];

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Button
            isIconOnly
            variant="light"
            onPress={() => navigate(-1)}
          >
            <Icon icon="lucide:arrow-left" size={20} />
          </Button>
          <h1 className="text-3xl font-bold">Privacy & Security</h1>
        </div>
        <p className="text-default-600">
          Control who can see your information and how others can interact with you.
        </p>
      </div>

      <div className="space-y-6">
        {/* Profile Visibility */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Profile Visibility</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Profile Visibility</p>
                <p className="text-sm text-default-600">Who can see your profile</p>
              </div>
              <Select
                selectedKeys={[settings.profileVisibility]}
                onSelectionChange={(keys) => updateSettings({ profileVisibility: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {visibilityOptions.filter(opt => opt.key !== 'private').map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Photo Visibility</p>
                <p className="text-sm text-default-600">Who can see your photos</p>
              </div>
              <Select
                selectedKeys={[settings.photoVisibility]}
                onSelectionChange={(keys) => updateSettings({ photoVisibility: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {visibilityOptions.map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Contact Information</p>
                <p className="text-sm text-default-600">Who can see your contact details</p>
              </div>
              <Select
                selectedKeys={[settings.contactInfoVisibility]}
                onSelectionChange={(keys) => updateSettings({ contactInfoVisibility: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {visibilityOptions.filter(opt => opt.key !== 'public').map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Show in Search Results</p>
                <p className="text-sm text-default-600">Allow others to find you in search</p>
              </div>
              <Switch
                isSelected={settings.showInSearch}
                onValueChange={(value) => updateSettings({ showInSearch: value })}
              />
            </div>
          </CardBody>
        </Card>

        {/* Activity & Status */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Activity & Status</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Online Status</p>
                <p className="text-sm text-default-600">Show when you're online</p>
              </div>
              <Switch
                isSelected={settings.showOnlineStatus}
                onValueChange={(value) => updateSettings({ showOnlineStatus: value })}
              />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Last Seen</p>
                <p className="text-sm text-default-600">Who can see when you were last active</p>
              </div>
              <Select
                selectedKeys={[settings.lastSeenVisibility]}
                onSelectionChange={(keys) => updateSettings({ lastSeenVisibility: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {visibilityOptions.filter(opt => opt.key !== 'premium_only').map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Profile Views</p>
                <p className="text-sm text-default-600">Show who viewed your profile</p>
              </div>
              <Switch
                isSelected={settings.showProfileViews}
                onValueChange={(value) => updateSettings({ showProfileViews: value })}
              />
            </div>
          </CardBody>
        </Card>

        {/* Communication */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Communication</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Allow Messages</p>
                <p className="text-sm text-default-600">Who can send you messages</p>
              </div>
              <Select
                selectedKeys={[settings.allowMessages]}
                onSelectionChange={(keys) => updateSettings({ allowMessages: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {messageOptions.map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Allow Interests</p>
                <p className="text-sm text-default-600">Who can send you interests</p>
              </div>
              <Select
                selectedKeys={[settings.allowInterests]}
                onSelectionChange={(keys) => updateSettings({ allowInterests: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {interestOptions.map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Photo Requests</p>
                <p className="text-sm text-default-600">Allow others to request your photos</p>
              </div>
              <Switch
                isSelected={settings.allowPhotoRequests}
                onValueChange={(value) => updateSettings({ allowPhotoRequests: value })}
              />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Contact Requests</p>
                <p className="text-sm text-default-600">Who can request your contact information</p>
              </div>
              <Select
                selectedKeys={[settings.allowContactRequests]}
                onSelectionChange={(keys) => updateSettings({ allowContactRequests: Array.from(keys)[0] as any })}
                className="max-w-xs"
              >
                {messageOptions.map(option => (
                  <SelectItem key={option.key}>{option.label}</SelectItem>
                ))}
              </Select>
            </div>
          </CardBody>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">Notifications</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-default-600">Receive notifications via email</p>
              </div>
              <Switch
                isSelected={settings.emailNotifications}
                onValueChange={(value) => updateSettings({ emailNotifications: value })}
              />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">SMS Notifications</p>
                <p className="text-sm text-default-600">Receive notifications via SMS</p>
              </div>
              <Switch
                isSelected={settings.smsNotifications}
                onValueChange={(value) => updateSettings({ smsNotifications: value })}
              />
            </div>

            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Push Notifications</p>
                <p className="text-sm text-default-600">Receive push notifications</p>
              </div>
              <Switch
                isSelected={settings.pushNotifications}
                onValueChange={(value) => updateSettings({ pushNotifications: value })}
              />
            </div>
          </CardBody>
        </Card>

        {/* Blocked Users */}
        <Card>
          <CardHeader className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Blocked Users</h3>
            <Button variant="flat" onPress={onBlockedOpen}>
              View All ({blockedUsers.length})
            </Button>
          </CardHeader>
          <CardBody>
            <p className="text-sm text-default-600">
              Manage users you have blocked. Blocked users cannot contact you or view your profile.
            </p>
          </CardBody>
        </Card>

        {/* Account Deletion */}
        <Card className="border-danger-200">
          <CardHeader>
            <h3 className="text-lg font-semibold text-danger">Danger Zone</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <Alert color="danger" variant="flat">
              <Icon icon="lucide:alert-triangle" />
              Account deletion is permanent and cannot be undone
            </Alert>
            
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">Delete Account</p>
                <p className="text-sm text-default-600">Permanently delete your account and all data</p>
              </div>
              <Button color="danger" variant="flat" onPress={onDeleteOpen}>
                Delete Account
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Blocked Users Modal */}
      <Modal isOpen={isBlockedOpen} onClose={onBlockedClose} size="2xl">
        <ModalContent>
          <ModalHeader>Blocked Users</ModalHeader>
          <ModalBody>
            {blockedUsers.length === 0 ? (
              <div className="text-center py-8">
                <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
                <p className="text-default-600">No blocked users</p>
              </div>
            ) : (
              <div className="space-y-3">
                {blockedUsers.map((blockedUser) => (
                  <div key={blockedUser.userId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar
                        size="sm"
                        fallback={<Icon icon="lucide:user" />}
                      />
                      <div>
                        <p className="font-medium">{blockedUser.userName}</p>
                        <p className="text-sm text-default-600">
                          Blocked: {new Date(blockedUser.blockedAt).toLocaleDateString()}
                        </p>
                        <p className="text-xs text-default-500">Reason: {blockedUser.reason}</p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="flat"
                      onPress={() => unblockUser(blockedUser.userId)}
                    >
                      Unblock
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onBlockedClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Account Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose}>
        <ModalContent>
          <ModalHeader>Delete Account</ModalHeader>
          <ModalBody>
            <Alert color="danger" variant="flat" className="mb-4">
              <Icon icon="lucide:alert-triangle" />
              This action cannot be undone. All your data will be permanently deleted.
            </Alert>

            <div className="space-y-4">
              <Select
                label="Reason for deletion"
                placeholder="Select a reason"
                selectedKeys={deleteReason ? [deleteReason] : []}
                onSelectionChange={(keys) => setDeleteReason(Array.from(keys)[0] as string)}
              >
                {deleteReasons.map((reason) => (
                  <SelectItem key={reason}>{reason}</SelectItem>
                ))}
              </Select>

              <Input
                type="password"
                label="Confirm Password"
                placeholder="Enter your password"
                value={deletePassword}
                onChange={(e) => setDeletePassword(e.target.value)}
              />

              <div className="text-sm text-default-600">
                <p className="font-medium mb-2">What will happen:</p>
                <ul className="space-y-1 ml-4">
                  <li>• Your profile will be permanently deleted</li>
                  <li>• All your photos will be removed</li>
                  <li>• Your conversations will be deleted</li>
                  <li>• Your interests and matches will be lost</li>
                  <li>• You will lose access to premium features</li>
                </ul>
              </div>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onDeleteClose}>
              Cancel
            </Button>
            <Button 
              color="danger" 
              onPress={requestAccountDeletion}
              isLoading={loading}
              isDisabled={!deleteReason || !deletePassword}
            >
              Delete Account
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
