const express = require('express');
require('dotenv').config();

const app = express();
app.use(express.json());

const routes = [
  { name: 'auth', path: './src/routes/auth', mount: '/api/auth' },
  { name: 'users', path: './src/routes/users', mount: '/api/users' },
  { name: 'profiles', path: './src/routes/profiles', mount: '/api/profiles' },
  { name: 'search', path: './src/routes/search', mount: '/api/search' },
  { name: 'interests', path: './src/routes/interests', mount: '/api/interests' },
  { name: 'messages', path: './src/routes/messages', mount: '/api/messages' },
  { name: 'subscriptions', path: './src/routes/subscriptions', mount: '/api/subscriptions' },
  { name: 'horoscope', path: './src/routes/horoscope', mount: '/api/horoscope' },
  { name: 'upload', path: './src/routes/upload', mount: '/api/upload' },
  { name: 'admin', path: './src/routes/admin', mount: '/api/admin' }
];

async function loadRoutes() {
  for (const route of routes) {
    try {
      console.log(`Loading ${route.name} routes...`);
      const routeModule = require(route.path);
      app.use(route.mount, routeModule);
      console.log(`✓ ${route.name} routes loaded successfully`);
    } catch (error) {
      console.error(`✗ Error loading ${route.name} routes:`, error.message);
      console.error('Stack trace:', error.stack);
      break; // Stop at the first error
    }
  }
}

loadRoutes().then(() => {
  console.log('All routes loaded successfully!');
  const PORT = 5002;
  app.listen(PORT, () => {
    console.log(`Debug server running on port ${PORT}`);
  });
}).catch(error => {
  console.error('Failed to load routes:', error);
});
