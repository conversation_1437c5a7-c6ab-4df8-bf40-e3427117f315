import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  Checkbox,
  Progress,
  Divider,
  Chip,
  Slider
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { userAPI } from '../services/api';

interface ProfileSetupData {
  // Personal Details
  maritalStatus: string;
  height: number;
  weight: number;
  complexion: string;
  bodyType: string;
  physicalStatus: string;
  bloodGroup: string;
  country: string;
  state: string;
  city: string;
  area: string;
  pinCode: string;
  willingToRelocate: boolean;
  motherTongue: string;
  knownLanguages: string[];
  aboutMe: string;
  hobbiesInterests: string;

  // Family Details
  familyType: string;
  familyStatus: string;
  familyValues: string;
  fatherName: string;
  fatherOccupation: string;
  motherName: string;
  motherOccupation: string;
  totalBrothers: number;
  marriedBrothers: number;
  totalSisters: number;
  marriedSisters: number;
  familyIncomeRange: string;
  aboutFamily: string;

  // Education Details
  highestEducation: string;
  educationField: string;
  institutionName: string;
  graduationYear: number;
  additionalQualifications: string[];

  // Professional Details
  occupation: string;
  designation: string;
  companyName: string;
  companyType: string;
  workExperienceYears: number;
  annualIncomeRange: string;
  workCity: string;
  willingToRelocateForWork: boolean;
  aboutProfession: string;

  // Lifestyle Details
  diet: string;
  smoking: string;
  drinking: string;
  fitnessLevel: string;
  musicPreferences: string[];
  sportsInterests: string[];
  partyPreference: string;

  // Religious Details
  religion: string;
  caste: string;
  subCaste: string;
  gothra: string;
  starNakshatra: string;
  rashiMoonSign: string;
  manglikStatus: string;
  birthTime: string;
  birthPlace: string;
  horoscopeMatchRequired: boolean;

  // Partner Preferences
  ageMin: number;
  ageMax: number;
  heightMinCm: number;
  heightMaxCm: number;
  preferredMaritalStatus: string[];
  preferredReligions: string[];
  preferredCastes: string[];
  preferredEducationLevels: string[];
  preferredOccupations: string[];
  incomeExpectationMin: number;
  incomeExpectationMax: number;
  preferredDiet: string[];
  preferredSmoking: string[];
  preferredDrinking: string[];
  partnerExpectations: string;
}

const setupSteps = [
  { id: 'personal', title: 'Personal Details', icon: 'lucide:user' },
  { id: 'family', title: 'Family Information', icon: 'lucide:users' },
  { id: 'education', title: 'Education & Career', icon: 'lucide:graduation-cap' },
  { id: 'lifestyle', title: 'Lifestyle', icon: 'lucide:heart' },
  { id: 'religious', title: 'Religious Details', icon: 'lucide:star' },
  { id: 'preferences', title: 'Partner Preferences', icon: 'lucide:search' }
];

export const ProfileSetupPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, updateUser } = useAuth();
  
  const [currentStep, setCurrentStep] = React.useState(location.state?.step || 'personal');
  const [loading, setLoading] = React.useState(false);
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  
  const [formData, setFormData] = React.useState<ProfileSetupData>({
    // Initialize with data from registration or existing profile
    ...location.state?.formData,
    maritalStatus: '',
    height: 165,
    weight: 60,
    complexion: '',
    bodyType: '',
    physicalStatus: 'normal',
    bloodGroup: '',
    country: 'India',
    state: '',
    city: '',
    area: '',
    pinCode: '',
    willingToRelocate: false,
    motherTongue: '',
    knownLanguages: [],
    aboutMe: '',
    hobbiesInterests: '',
    familyType: '',
    familyStatus: '',
    familyValues: '',
    fatherName: '',
    fatherOccupation: '',
    motherName: '',
    motherOccupation: '',
    totalBrothers: 0,
    marriedBrothers: 0,
    totalSisters: 0,
    marriedSisters: 0,
    familyIncomeRange: '',
    aboutFamily: '',
    highestEducation: '',
    educationField: '',
    institutionName: '',
    graduationYear: new Date().getFullYear(),
    additionalQualifications: [],
    occupation: '',
    designation: '',
    companyName: '',
    companyType: '',
    workExperienceYears: 0,
    annualIncomeRange: '',
    workCity: '',
    willingToRelocateForWork: false,
    aboutProfession: '',
    diet: '',
    smoking: 'never',
    drinking: 'never',
    fitnessLevel: '',
    musicPreferences: [],
    sportsInterests: [],
    partyPreference: '',
    religion: '',
    caste: '',
    subCaste: '',
    gothra: '',
    starNakshatra: '',
    rashiMoonSign: '',
    manglikStatus: '',
    birthTime: '',
    birthPlace: '',
    horoscopeMatchRequired: false,
    ageMin: 21,
    ageMax: 35,
    heightMinCm: 150,
    heightMaxCm: 180,
    preferredMaritalStatus: [],
    preferredReligions: [],
    preferredCastes: [],
    preferredEducationLevels: [],
    preferredOccupations: [],
    incomeExpectationMin: 0,
    incomeExpectationMax: 0,
    preferredDiet: [],
    preferredSmoking: [],
    preferredDrinking: [],
    partnerExpectations: ''
  });

  const updateFormData = (field: keyof ProfileSetupData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getCurrentStepIndex = () => {
    return setupSteps.findIndex(step => step.id === currentStep);
  };

  const getProgressPercentage = () => {
    return ((getCurrentStepIndex() + 1) / setupSteps.length) * 100;
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case 'personal':
        if (!formData.maritalStatus) newErrors.maritalStatus = 'Marital status is required';
        if (!formData.state) newErrors.state = 'State is required';
        if (!formData.city) newErrors.city = 'City is required';
        if (!formData.motherTongue) newErrors.motherTongue = 'Mother tongue is required';
        break;

      case 'family':
        if (!formData.familyType) newErrors.familyType = 'Family type is required';
        if (!formData.familyValues) newErrors.familyValues = 'Family values is required';
        break;

      case 'education':
        if (!formData.highestEducation) newErrors.highestEducation = 'Education level is required';
        break;

      case 'lifestyle':
        if (!formData.diet) newErrors.diet = 'Diet preference is required';
        break;

      case 'religious':
        if (!formData.religion) newErrors.religion = 'Religion is required';
        break;

      case 'preferences':
        if (formData.ageMin >= formData.ageMax) {
          newErrors.ageRange = 'Invalid age range';
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const saveCurrentStep = async () => {
    if (!validateCurrentStep()) return false;

    setLoading(true);
    try {
      let updateData: any = {};

      switch (currentStep) {
        case 'personal':
          updateData.personalDetails = {
            maritalStatus: formData.maritalStatus,
            height: formData.height,
            weight: formData.weight,
            complexion: formData.complexion,
            bodyType: formData.bodyType,
            physicalStatus: formData.physicalStatus,
            bloodGroup: formData.bloodGroup,
            country: formData.country,
            state: formData.state,
            city: formData.city,
            area: formData.area,
            pinCode: formData.pinCode,
            willingToRelocate: formData.willingToRelocate,
            motherTongue: formData.motherTongue,
            knownLanguages: formData.knownLanguages,
            aboutMe: formData.aboutMe,
            hobbiesInterests: formData.hobbiesInterests
          };
          break;

        case 'family':
          updateData.familyDetails = {
            familyType: formData.familyType,
            familyStatus: formData.familyStatus,
            familyValues: formData.familyValues,
            fatherName: formData.fatherName,
            fatherOccupation: formData.fatherOccupation,
            motherName: formData.motherName,
            motherOccupation: formData.motherOccupation,
            totalBrothers: formData.totalBrothers,
            marriedBrothers: formData.marriedBrothers,
            totalSisters: formData.totalSisters,
            marriedSisters: formData.marriedSisters,
            familyIncomeRange: formData.familyIncomeRange,
            aboutFamily: formData.aboutFamily
          };
          break;

        case 'education':
          updateData.educationDetails = {
            highestEducation: formData.highestEducation,
            educationField: formData.educationField,
            institutionName: formData.institutionName,
            graduationYear: formData.graduationYear,
            additionalQualifications: formData.additionalQualifications
          };
          updateData.professionalDetails = {
            occupation: formData.occupation,
            designation: formData.designation,
            companyName: formData.companyName,
            companyType: formData.companyType,
            workExperienceYears: formData.workExperienceYears,
            annualIncomeRange: formData.annualIncomeRange,
            workCity: formData.workCity,
            willingToRelocateForWork: formData.willingToRelocateForWork,
            aboutProfession: formData.aboutProfession
          };
          break;

        case 'lifestyle':
          updateData.lifestyleDetails = {
            diet: formData.diet,
            smoking: formData.smoking,
            drinking: formData.drinking,
            fitnessLevel: formData.fitnessLevel,
            musicPreferences: formData.musicPreferences,
            sportsInterests: formData.sportsInterests,
            partyPreference: formData.partyPreference
          };
          break;

        case 'religious':
          updateData.religiousDetails = {
            religion: formData.religion,
            caste: formData.caste,
            subCaste: formData.subCaste,
            gothra: formData.gothra,
            starNakshatra: formData.starNakshatra,
            rashiMoonSign: formData.rashiMoonSign,
            manglikStatus: formData.manglikStatus,
            birthTime: formData.birthTime,
            birthPlace: formData.birthPlace,
            horoscopeMatchRequired: formData.horoscopeMatchRequired
          };
          break;

        case 'preferences':
          updateData.partnerPreferences = {
            ageMin: formData.ageMin,
            ageMax: formData.ageMax,
            heightMinCm: formData.heightMinCm,
            heightMaxCm: formData.heightMaxCm,
            preferredMaritalStatus: formData.preferredMaritalStatus,
            preferredReligions: formData.preferredReligions,
            preferredCastes: formData.preferredCastes,
            preferredEducationLevels: formData.preferredEducationLevels,
            preferredOccupations: formData.preferredOccupations,
            incomeExpectationMin: formData.incomeExpectationMin,
            incomeExpectationMax: formData.incomeExpectationMax,
            preferredDiet: formData.preferredDiet,
            preferredSmoking: formData.preferredSmoking,
            preferredDrinking: formData.preferredDrinking,
            partnerExpectations: formData.partnerExpectations
          };
          break;
      }

      const response = await userAPI.updateProfile(updateData);
      
      if (response.success) {
        updateUser(response.data.user);
        return true;
      } else {
        setErrors({ submit: response.message || 'Failed to save profile' });
        return false;
      }
    } catch (error: any) {
      console.error('Profile update error:', error);
      setErrors({ submit: error.response?.data?.message || 'Failed to save profile' });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleNext = async () => {
    const saved = await saveCurrentStep();
    if (saved) {
      const currentIndex = getCurrentStepIndex();
      if (currentIndex < setupSteps.length - 1) {
        setCurrentStep(setupSteps[currentIndex + 1].id);
      }
    }
  };

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(setupSteps[currentIndex - 1].id);
    }
  };

  const handleComplete = async () => {
    const saved = await saveCurrentStep();
    if (saved) {
      navigate('/dashboard', { 
        state: { message: 'Profile setup completed successfully!' }
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'personal':
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Personal Details</h2>
              <p className="text-default-500">Tell us about yourself</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Marital Status"
                placeholder="Select marital status"
                selectedKeys={formData.maritalStatus ? [formData.maritalStatus] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData('maritalStatus', value);
                }}
                isInvalid={!!errors.maritalStatus}
                errorMessage={errors.maritalStatus}
              >
                <SelectItem key="never_married">Never Married</SelectItem>
                <SelectItem key="divorced">Divorced</SelectItem>
                <SelectItem key="widowed">Widowed</SelectItem>
                <SelectItem key="separated">Separated</SelectItem>
                <SelectItem key="awaiting_divorce">Awaiting Divorce</SelectItem>
              </Select>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Height: {formData.height} cm
                </label>
                <Slider
                  step={1}
                  minValue={140}
                  maxValue={200}
                  value={formData.height}
                  onChange={(value) => updateFormData('height', value as number)}
                  className="max-w-md"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Weight: {formData.weight} kg
                </label>
                <Slider
                  step={1}
                  minValue={40}
                  maxValue={120}
                  value={formData.weight}
                  onChange={(value) => updateFormData('weight', value as number)}
                  className="max-w-md"
                />
              </div>

              <Select
                label="Complexion"
                placeholder="Select complexion"
                selectedKeys={formData.complexion ? [formData.complexion] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData('complexion', value);
                }}
              >
                <SelectItem key="very_fair">Very Fair</SelectItem>
                <SelectItem key="fair">Fair</SelectItem>
                <SelectItem key="wheatish">Wheatish</SelectItem>
                <SelectItem key="dark">Dark</SelectItem>
                <SelectItem key="very_dark">Very Dark</SelectItem>
              </Select>

              <Select
                label="Body Type"
                placeholder="Select body type"
                selectedKeys={formData.bodyType ? [formData.bodyType] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData('bodyType', value);
                }}
              >
                <SelectItem key="slim">Slim</SelectItem>
                <SelectItem key="average">Average</SelectItem>
                <SelectItem key="athletic">Athletic</SelectItem>
                <SelectItem key="heavy">Heavy</SelectItem>
                <SelectItem key="plus_size">Plus Size</SelectItem>
              </Select>

              <Select
                label="Blood Group"
                placeholder="Select blood group"
                selectedKeys={formData.bloodGroup ? [formData.bloodGroup] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData('bloodGroup', value);
                }}
              >
                <SelectItem key="A+">A+</SelectItem>
                <SelectItem key="A-">A-</SelectItem>
                <SelectItem key="B+">B+</SelectItem>
                <SelectItem key="B-">B-</SelectItem>
                <SelectItem key="AB+">AB+</SelectItem>
                <SelectItem key="AB-">AB-</SelectItem>
                <SelectItem key="O+">O+</SelectItem>
                <SelectItem key="O-">O-</SelectItem>
              </Select>
            </div>

            <Divider />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="State"
                placeholder="Enter your state"
                value={formData.state}
                onChange={(e) => updateFormData('state', e.target.value)}
                isInvalid={!!errors.state}
                errorMessage={errors.state}
              />

              <Input
                label="City"
                placeholder="Enter your city"
                value={formData.city}
                onChange={(e) => updateFormData('city', e.target.value)}
                isInvalid={!!errors.city}
                errorMessage={errors.city}
              />

              <Input
                label="Area/Locality"
                placeholder="Enter area or locality"
                value={formData.area}
                onChange={(e) => updateFormData('area', e.target.value)}
              />

              <Input
                label="PIN Code"
                placeholder="Enter PIN code"
                value={formData.pinCode}
                onChange={(e) => updateFormData('pinCode', e.target.value)}
              />
            </div>

            <Select
              label="Mother Tongue"
              placeholder="Select mother tongue"
              selectedKeys={formData.motherTongue ? [formData.motherTongue] : []}
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                updateFormData('motherTongue', value);
              }}
              isInvalid={!!errors.motherTongue}
              errorMessage={errors.motherTongue}
            >
              <SelectItem key="hindi">Hindi</SelectItem>
              <SelectItem key="english">English</SelectItem>
              <SelectItem key="bengali">Bengali</SelectItem>
              <SelectItem key="telugu">Telugu</SelectItem>
              <SelectItem key="marathi">Marathi</SelectItem>
              <SelectItem key="tamil">Tamil</SelectItem>
              <SelectItem key="gujarati">Gujarati</SelectItem>
              <SelectItem key="kannada">Kannada</SelectItem>
              <SelectItem key="malayalam">Malayalam</SelectItem>
              <SelectItem key="punjabi">Punjabi</SelectItem>
              <SelectItem key="urdu">Urdu</SelectItem>
            </Select>

            <Checkbox
              isSelected={formData.willingToRelocate}
              onValueChange={(checked) => updateFormData('willingToRelocate', checked)}
            >
              Willing to relocate after marriage
            </Checkbox>

            <Textarea
              label="About Me"
              placeholder="Tell us about yourself, your interests, and what makes you unique..."
              value={formData.aboutMe}
              onChange={(e) => updateFormData('aboutMe', e.target.value)}
              maxRows={4}
            />

            <Textarea
              label="Hobbies & Interests"
              placeholder="Share your hobbies, interests, and what you enjoy doing..."
              value={formData.hobbiesInterests}
              onChange={(e) => updateFormData('hobbiesInterests', e.target.value)}
              maxRows={3}
            />
          </div>
        );

      // Add other step cases here...
      default:
        return <div>Step content for {currentStep}</div>;
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to set up your profile.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold mb-2">Complete Your Profile</h1>
            <p className="text-default-500">
              Help us find your perfect match by completing your profile
            </p>
          </div>

          {/* Step Navigation */}
          <div className="flex justify-between items-center mb-4">
            {setupSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 cursor-pointer
                  ${currentStep === step.id 
                    ? 'bg-primary text-white border-primary' 
                    : getCurrentStepIndex() > index
                    ? 'bg-success text-white border-success'
                    : 'bg-white text-default-400 border-default-200'
                  }
                `}
                onClick={() => setCurrentStep(step.id)}
                >
                  {getCurrentStepIndex() > index ? (
                    <Icon icon="lucide:check" size={20} />
                  ) : (
                    <Icon icon={step.icon} size={20} />
                  )}
                </div>
                {index < setupSteps.length - 1 && (
                  <div className={`
                    w-full h-0.5 mx-2
                    ${getCurrentStepIndex() > index ? 'bg-success' : 'bg-default-200'}
                  `} />
                )}
              </div>
            ))}
          </div>
          
          <div className="flex justify-between text-sm">
            {setupSteps.map((step) => (
              <span 
                key={step.id}
                className={`
                  cursor-pointer
                  ${currentStep === step.id ? 'text-primary font-medium' : 'text-default-400'}
                `}
                onClick={() => setCurrentStep(step.id)}
              >
                {step.title}
              </span>
            ))}
          </div>
          
          <Progress 
            value={getProgressPercentage()} 
            color="primary" 
            className="mt-4"
          />
        </div>

        {/* Form Card */}
        <Card className="shadow-lg">
          <CardBody className="p-8">
            {renderStepContent()}

            {errors.submit && (
              <div className="mt-4 p-3 bg-danger-50 border border-danger-200 rounded-lg">
                <p className="text-danger text-sm">{errors.submit}</p>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="flat"
                onPress={handlePrevious}
                isDisabled={getCurrentStepIndex() === 0}
                startContent={<Icon icon="lucide:chevron-left" />}
              >
                Previous
              </Button>

              {getCurrentStepIndex() < setupSteps.length - 1 ? (
                <Button
                  color="primary"
                  onPress={handleNext}
                  isLoading={loading}
                  endContent={<Icon icon="lucide:chevron-right" />}
                >
                  Save & Continue
                </Button>
              ) : (
                <Button
                  color="success"
                  onPress={handleComplete}
                  isLoading={loading}
                  endContent={<Icon icon="lucide:check" />}
                >
                  Complete Profile
                </Button>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Skip Option */}
        <div className="text-center mt-6">
          <Button
            variant="light"
            onPress={() => navigate('/dashboard')}
            className="text-default-500"
          >
            Skip for now (you can complete this later)
          </Button>
        </div>
      </div>
    </div>
  );
};
