import React from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  <PERSON>,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  Checkbox,
  Progress,
  Divider,
  Chip,
  Slider,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAuth } from "../contexts/auth-context";
import { userAPI } from "../services/api";

interface ProfileSetupData {
  // Personal Details
  maritalStatus: string;
  height: number;
  weight: number;
  complexion: string;
  bodyType: string;
  physicalStatus: string;
  bloodGroup: string;
  country: string;
  state: string;
  city: string;
  area: string;
  pinCode: string;
  willingToRelocate: boolean;
  motherTongue: string;
  knownLanguages: string[];
  aboutMe: string;
  hobbiesInterests: string;

  // Family Details
  familyType: string;
  familyStatus: string;
  familyValues: string;
  fatherName: string;
  fatherOccupation: string;
  motherName: string;
  motherOccupation: string;
  totalBrothers: number;
  marriedBrothers: number;
  totalSisters: number;
  marriedSisters: number;
  familyIncomeRange: string;
  aboutFamily: string;

  // Education Details
  highestEducation: string;
  educationField: string;
  institutionName: string;
  graduationYear: number;
  additionalQualifications: string[];

  // Professional Details
  occupation: string;
  designation: string;
  companyName: string;
  companyType: string;
  workExperienceYears: number;
  annualIncomeRange: string;
  workCity: string;
  willingToRelocateForWork: boolean;
  aboutProfession: string;

  // Lifestyle Details
  diet: string;
  smoking: string;
  drinking: string;
  fitnessLevel: string;
  musicPreferences: string[];
  sportsInterests: string[];
  partyPreference: string;

  // Religious Details
  religion: string;
  caste: string;
  subCaste: string;
  gothra: string;
  starNakshatra: string;
  rashiMoonSign: string;
  manglikStatus: string;
  birthTime: string;
  birthPlace: string;
  horoscopeMatchRequired: boolean;

  // Partner Preferences
  ageMin: number;
  ageMax: number;
  heightMinCm: number;
  heightMaxCm: number;
  preferredMaritalStatus: string[];
  preferredReligions: string[];
  preferredCastes: string[];
  preferredEducationLevels: string[];
  preferredOccupations: string[];
  incomeExpectationMin: number;
  incomeExpectationMax: number;
  preferredDiet: string[];
  preferredSmoking: string[];
  preferredDrinking: string[];
  partnerExpectations: string;
}

const setupSteps = [
  { id: "personal", title: "Personal Details", icon: "lucide:user" },
  { id: "family", title: "Family Information", icon: "lucide:users" },
  {
    id: "education",
    title: "Education & Career",
    icon: "lucide:graduation-cap",
  },
  { id: "lifestyle", title: "Lifestyle", icon: "lucide:heart" },
  { id: "religious", title: "Religious Details", icon: "lucide:star" },
  { id: "preferences", title: "Partner Preferences", icon: "lucide:search" },
];

export const ProfileSetupPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, updateUser } = useAuth();

  const [currentStep, setCurrentStep] = React.useState(
    location.state?.step || "personal"
  );
  const [loading, setLoading] = React.useState(false);
  const [errors, setErrors] = React.useState<Record<string, string>>({});

  const [formData, setFormData] = React.useState<ProfileSetupData>({
    // Initialize with data from registration or existing profile
    ...location.state?.formData,
    maritalStatus: "",
    height: 165,
    weight: 60,
    complexion: "",
    bodyType: "",
    physicalStatus: "normal",
    bloodGroup: "",
    country: "India",
    state: "",
    city: "",
    area: "",
    pinCode: "",
    willingToRelocate: false,
    motherTongue: "",
    knownLanguages: [],
    aboutMe: "",
    hobbiesInterests: "",
    familyType: "",
    familyStatus: "",
    familyValues: "",
    fatherName: "",
    fatherOccupation: "",
    motherName: "",
    motherOccupation: "",
    totalBrothers: 0,
    marriedBrothers: 0,
    totalSisters: 0,
    marriedSisters: 0,
    familyIncomeRange: "",
    aboutFamily: "",
    highestEducation: "",
    educationField: "",
    institutionName: "",
    graduationYear: new Date().getFullYear(),
    additionalQualifications: [],
    occupation: "",
    designation: "",
    companyName: "",
    companyType: "",
    workExperienceYears: 0,
    annualIncomeRange: "",
    workCity: "",
    willingToRelocateForWork: false,
    aboutProfession: "",
    diet: "",
    smoking: "never",
    drinking: "never",
    fitnessLevel: "",
    musicPreferences: [],
    sportsInterests: [],
    partyPreference: "",
    religion: "",
    caste: "",
    subCaste: "",
    gothra: "",
    starNakshatra: "",
    rashiMoonSign: "",
    manglikStatus: "",
    birthTime: "",
    birthPlace: "",
    horoscopeMatchRequired: false,
    ageMin: 21,
    ageMax: 35,
    heightMinCm: 150,
    heightMaxCm: 180,
    preferredMaritalStatus: [],
    preferredReligions: [],
    preferredCastes: [],
    preferredEducationLevels: [],
    preferredOccupations: [],
    incomeExpectationMin: 0,
    incomeExpectationMax: 0,
    preferredDiet: [],
    preferredSmoking: [],
    preferredDrinking: [],
    partnerExpectations: "",
  });

  const updateFormData = (field: keyof ProfileSetupData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const getCurrentStepIndex = () => {
    return setupSteps.findIndex((step) => step.id === currentStep);
  };

  const getProgressPercentage = () => {
    return ((getCurrentStepIndex() + 1) / setupSteps.length) * 100;
  };

  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, string> = {};

    switch (currentStep) {
      case "personal":
        if (!formData.maritalStatus)
          newErrors.maritalStatus = "Marital status is required";
        if (!formData.state) newErrors.state = "State is required";
        if (!formData.city) newErrors.city = "City is required";
        if (!formData.motherTongue)
          newErrors.motherTongue = "Mother tongue is required";
        break;

      case "family":
        if (!formData.familyType)
          newErrors.familyType = "Family type is required";
        if (!formData.familyValues)
          newErrors.familyValues = "Family values is required";
        break;

      case "education":
        if (!formData.highestEducation)
          newErrors.highestEducation = "Education level is required";
        break;

      case "lifestyle":
        if (!formData.diet) newErrors.diet = "Diet preference is required";
        break;

      case "religious":
        if (!formData.religion) newErrors.religion = "Religion is required";
        break;

      case "preferences":
        if (formData.ageMin >= formData.ageMax) {
          newErrors.ageRange = "Invalid age range";
        }
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const saveCurrentStep = async () => {
    if (!validateCurrentStep()) return false;

    setLoading(true);
    try {
      let updateData: any = {};

      switch (currentStep) {
        case "personal":
          updateData.personalDetails = {
            maritalStatus: formData.maritalStatus,
            height: formData.height,
            weight: formData.weight,
            complexion: formData.complexion,
            bodyType: formData.bodyType,
            physicalStatus: formData.physicalStatus,
            bloodGroup: formData.bloodGroup,
            country: formData.country,
            state: formData.state,
            city: formData.city,
            area: formData.area,
            pinCode: formData.pinCode,
            willingToRelocate: formData.willingToRelocate,
            motherTongue: formData.motherTongue,
            knownLanguages: formData.knownLanguages,
            aboutMe: formData.aboutMe,
            hobbiesInterests: formData.hobbiesInterests,
          };
          break;

        case "family":
          updateData.familyDetails = {
            familyType: formData.familyType,
            familyStatus: formData.familyStatus,
            familyValues: formData.familyValues,
            fatherName: formData.fatherName,
            fatherOccupation: formData.fatherOccupation,
            motherName: formData.motherName,
            motherOccupation: formData.motherOccupation,
            totalBrothers: formData.totalBrothers,
            marriedBrothers: formData.marriedBrothers,
            totalSisters: formData.totalSisters,
            marriedSisters: formData.marriedSisters,
            familyIncomeRange: formData.familyIncomeRange,
            aboutFamily: formData.aboutFamily,
          };
          break;

        case "education":
          updateData.educationDetails = {
            highestEducation: formData.highestEducation,
            educationField: formData.educationField,
            institutionName: formData.institutionName,
            graduationYear: formData.graduationYear,
            additionalQualifications: formData.additionalQualifications,
          };
          updateData.professionalDetails = {
            occupation: formData.occupation,
            designation: formData.designation,
            companyName: formData.companyName,
            companyType: formData.companyType,
            workExperienceYears: formData.workExperienceYears,
            annualIncomeRange: formData.annualIncomeRange,
            workCity: formData.workCity,
            willingToRelocateForWork: formData.willingToRelocateForWork,
            aboutProfession: formData.aboutProfession,
          };
          break;

        case "lifestyle":
          updateData.lifestyleDetails = {
            diet: formData.diet,
            smoking: formData.smoking,
            drinking: formData.drinking,
            fitnessLevel: formData.fitnessLevel,
            musicPreferences: formData.musicPreferences,
            sportsInterests: formData.sportsInterests,
            partyPreference: formData.partyPreference,
          };
          break;

        case "religious":
          updateData.religiousDetails = {
            religion: formData.religion,
            caste: formData.caste,
            subCaste: formData.subCaste,
            gothra: formData.gothra,
            starNakshatra: formData.starNakshatra,
            rashiMoonSign: formData.rashiMoonSign,
            manglikStatus: formData.manglikStatus,
            birthTime: formData.birthTime,
            birthPlace: formData.birthPlace,
            horoscopeMatchRequired: formData.horoscopeMatchRequired,
          };
          break;

        case "preferences":
          updateData.partnerPreferences = {
            ageMin: formData.ageMin,
            ageMax: formData.ageMax,
            heightMinCm: formData.heightMinCm,
            heightMaxCm: formData.heightMaxCm,
            preferredMaritalStatus: formData.preferredMaritalStatus,
            preferredReligions: formData.preferredReligions,
            preferredCastes: formData.preferredCastes,
            preferredEducationLevels: formData.preferredEducationLevels,
            preferredOccupations: formData.preferredOccupations,
            incomeExpectationMin: formData.incomeExpectationMin,
            incomeExpectationMax: formData.incomeExpectationMax,
            preferredDiet: formData.preferredDiet,
            preferredSmoking: formData.preferredSmoking,
            preferredDrinking: formData.preferredDrinking,
            partnerExpectations: formData.partnerExpectations,
          };
          break;
      }

      const response = await userAPI.updateProfile(updateData);

      if (response.success) {
        updateUser(response.data.user);
        return true;
      } else {
        setErrors({ submit: response.message || "Failed to save profile" });
        return false;
      }
    } catch (error: any) {
      console.error("Profile update error:", error);
      setErrors({
        submit: error.response?.data?.message || "Failed to save profile",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };

  const handleNext = async () => {
    const saved = await saveCurrentStep();
    if (saved) {
      const currentIndex = getCurrentStepIndex();
      if (currentIndex < setupSteps.length - 1) {
        setCurrentStep(setupSteps[currentIndex + 1].id);
      }
    }
  };

  const handlePrevious = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      setCurrentStep(setupSteps[currentIndex - 1].id);
    }
  };

  const handleComplete = async () => {
    const saved = await saveCurrentStep();
    if (saved) {
      navigate("/dashboard", {
        state: { message: "Profile setup completed successfully!" },
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case "personal":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Personal Details</h2>
              <p className="text-default-500">Tell us about yourself</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Marital Status"
                placeholder="Select marital status"
                selectedKeys={
                  formData.maritalStatus ? [formData.maritalStatus] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("maritalStatus", value);
                }}
                isInvalid={!!errors.maritalStatus}
                errorMessage={errors.maritalStatus}
              >
                <SelectItem key="never_married">Never Married</SelectItem>
                <SelectItem key="divorced">Divorced</SelectItem>
                <SelectItem key="widowed">Widowed</SelectItem>
                <SelectItem key="separated">Separated</SelectItem>
                <SelectItem key="awaiting_divorce">Awaiting Divorce</SelectItem>
              </Select>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Height: {formData.height} cm
                </label>
                <Slider
                  step={1}
                  minValue={140}
                  maxValue={200}
                  value={formData.height}
                  onChange={(value) =>
                    updateFormData("height", value as number)
                  }
                  className="max-w-md"
                />
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Weight: {formData.weight} kg
                </label>
                <Slider
                  step={1}
                  minValue={40}
                  maxValue={120}
                  value={formData.weight}
                  onChange={(value) =>
                    updateFormData("weight", value as number)
                  }
                  className="max-w-md"
                />
              </div>

              <Select
                label="Complexion"
                placeholder="Select complexion"
                selectedKeys={formData.complexion ? [formData.complexion] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("complexion", value);
                }}
              >
                <SelectItem key="very_fair">Very Fair</SelectItem>
                <SelectItem key="fair">Fair</SelectItem>
                <SelectItem key="wheatish">Wheatish</SelectItem>
                <SelectItem key="dark">Dark</SelectItem>
                <SelectItem key="very_dark">Very Dark</SelectItem>
              </Select>

              <Select
                label="Body Type"
                placeholder="Select body type"
                selectedKeys={formData.bodyType ? [formData.bodyType] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("bodyType", value);
                }}
              >
                <SelectItem key="slim">Slim</SelectItem>
                <SelectItem key="average">Average</SelectItem>
                <SelectItem key="athletic">Athletic</SelectItem>
                <SelectItem key="heavy">Heavy</SelectItem>
                <SelectItem key="plus_size">Plus Size</SelectItem>
              </Select>

              <Select
                label="Blood Group"
                placeholder="Select blood group"
                selectedKeys={formData.bloodGroup ? [formData.bloodGroup] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("bloodGroup", value);
                }}
              >
                <SelectItem key="A+">A+</SelectItem>
                <SelectItem key="A-">A-</SelectItem>
                <SelectItem key="B+">B+</SelectItem>
                <SelectItem key="B-">B-</SelectItem>
                <SelectItem key="AB+">AB+</SelectItem>
                <SelectItem key="AB-">AB-</SelectItem>
                <SelectItem key="O+">O+</SelectItem>
                <SelectItem key="O-">O-</SelectItem>
              </Select>
            </div>

            <Divider />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="State"
                placeholder="Enter your state"
                value={formData.state}
                onChange={(e) => updateFormData("state", e.target.value)}
                isInvalid={!!errors.state}
                errorMessage={errors.state}
              />

              <Input
                label="City"
                placeholder="Enter your city"
                value={formData.city}
                onChange={(e) => updateFormData("city", e.target.value)}
                isInvalid={!!errors.city}
                errorMessage={errors.city}
              />

              <Input
                label="Area/Locality"
                placeholder="Enter area or locality"
                value={formData.area}
                onChange={(e) => updateFormData("area", e.target.value)}
              />

              <Input
                label="PIN Code"
                placeholder="Enter PIN code"
                value={formData.pinCode}
                onChange={(e) => updateFormData("pinCode", e.target.value)}
              />
            </div>

            <Select
              label="Mother Tongue"
              placeholder="Select mother tongue"
              selectedKeys={
                formData.motherTongue ? [formData.motherTongue] : []
              }
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                updateFormData("motherTongue", value);
              }}
              isInvalid={!!errors.motherTongue}
              errorMessage={errors.motherTongue}
            >
              <SelectItem key="hindi">Hindi</SelectItem>
              <SelectItem key="english">English</SelectItem>
              <SelectItem key="bengali">Bengali</SelectItem>
              <SelectItem key="telugu">Telugu</SelectItem>
              <SelectItem key="marathi">Marathi</SelectItem>
              <SelectItem key="tamil">Tamil</SelectItem>
              <SelectItem key="gujarati">Gujarati</SelectItem>
              <SelectItem key="kannada">Kannada</SelectItem>
              <SelectItem key="malayalam">Malayalam</SelectItem>
              <SelectItem key="punjabi">Punjabi</SelectItem>
              <SelectItem key="urdu">Urdu</SelectItem>
            </Select>

            <Checkbox
              isSelected={formData.willingToRelocate}
              onValueChange={(checked) =>
                updateFormData("willingToRelocate", checked)
              }
            >
              Willing to relocate after marriage
            </Checkbox>

            <Textarea
              label="About Me"
              placeholder="Tell us about yourself, your interests, and what makes you unique..."
              value={formData.aboutMe}
              onChange={(e) => updateFormData("aboutMe", e.target.value)}
              maxRows={4}
            />

            <Textarea
              label="Hobbies & Interests"
              placeholder="Share your hobbies, interests, and what you enjoy doing..."
              value={formData.hobbiesInterests}
              onChange={(e) =>
                updateFormData("hobbiesInterests", e.target.value)
              }
              maxRows={3}
            />
          </div>
        );

      case "family":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Family Information</h2>
              <p className="text-default-500">Tell us about your family</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Family Type"
                placeholder="Select family type"
                selectedKeys={formData.familyType ? [formData.familyType] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("familyType", value);
                }}
                isInvalid={!!errors.familyType}
                errorMessage={errors.familyType}
              >
                <SelectItem key="nuclear">Nuclear Family</SelectItem>
                <SelectItem key="joint">Joint Family</SelectItem>
                <SelectItem key="others">Others</SelectItem>
              </Select>

              <Select
                label="Family Status"
                placeholder="Select family status"
                selectedKeys={
                  formData.familyStatus ? [formData.familyStatus] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("familyStatus", value);
                }}
              >
                <SelectItem key="middle_class">Middle Class</SelectItem>
                <SelectItem key="upper_middle_class">
                  Upper Middle Class
                </SelectItem>
                <SelectItem key="rich">Rich</SelectItem>
                <SelectItem key="affluent">Affluent</SelectItem>
              </Select>

              <Select
                label="Family Values"
                placeholder="Select family values"
                selectedKeys={
                  formData.familyValues ? [formData.familyValues] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("familyValues", value);
                }}
                isInvalid={!!errors.familyValues}
                errorMessage={errors.familyValues}
              >
                <SelectItem key="orthodox">Orthodox</SelectItem>
                <SelectItem key="traditional">Traditional</SelectItem>
                <SelectItem key="moderate">Moderate</SelectItem>
                <SelectItem key="liberal">Liberal</SelectItem>
              </Select>
            </div>

            <Divider />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Father's Name"
                placeholder="Enter father's name"
                value={formData.fatherName}
                onChange={(e) => updateFormData("fatherName", e.target.value)}
              />

              <Input
                label="Father's Occupation"
                placeholder="Enter father's occupation"
                value={formData.fatherOccupation}
                onChange={(e) =>
                  updateFormData("fatherOccupation", e.target.value)
                }
              />

              <Input
                label="Mother's Name"
                placeholder="Enter mother's name"
                value={formData.motherName}
                onChange={(e) => updateFormData("motherName", e.target.value)}
              />

              <Input
                label="Mother's Occupation"
                placeholder="Enter mother's occupation"
                value={formData.motherOccupation}
                onChange={(e) =>
                  updateFormData("motherOccupation", e.target.value)
                }
              />
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Input
                label="Total Brothers"
                type="number"
                min="0"
                value={formData.totalBrothers.toString()}
                onChange={(e) =>
                  updateFormData("totalBrothers", parseInt(e.target.value) || 0)
                }
              />

              <Input
                label="Married Brothers"
                type="number"
                min="0"
                value={formData.marriedBrothers.toString()}
                onChange={(e) =>
                  updateFormData(
                    "marriedBrothers",
                    parseInt(e.target.value) || 0
                  )
                }
              />

              <Input
                label="Total Sisters"
                type="number"
                min="0"
                value={formData.totalSisters.toString()}
                onChange={(e) =>
                  updateFormData("totalSisters", parseInt(e.target.value) || 0)
                }
              />

              <Input
                label="Married Sisters"
                type="number"
                min="0"
                value={formData.marriedSisters.toString()}
                onChange={(e) =>
                  updateFormData(
                    "marriedSisters",
                    parseInt(e.target.value) || 0
                  )
                }
              />
            </div>

            <Select
              label="Family Income Range"
              placeholder="Select family income range"
              selectedKeys={
                formData.familyIncomeRange ? [formData.familyIncomeRange] : []
              }
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                updateFormData("familyIncomeRange", value);
              }}
            >
              <SelectItem key="below_1_lakh">Below 1 Lakh</SelectItem>
              <SelectItem key="1_3_lakhs">1-3 Lakhs</SelectItem>
              <SelectItem key="3_5_lakhs">3-5 Lakhs</SelectItem>
              <SelectItem key="5_10_lakhs">5-10 Lakhs</SelectItem>
              <SelectItem key="10_20_lakhs">10-20 Lakhs</SelectItem>
              <SelectItem key="20_50_lakhs">20-50 Lakhs</SelectItem>
              <SelectItem key="above_50_lakhs">Above 50 Lakhs</SelectItem>
            </Select>

            <Textarea
              label="About Family"
              placeholder="Tell us about your family background, traditions, and values..."
              value={formData.aboutFamily}
              onChange={(e) => updateFormData("aboutFamily", e.target.value)}
              maxRows={4}
            />
          </div>
        );

      case "education":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Education & Career</h2>
              <p className="text-default-500">
                Share your educational and professional background
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Highest Education"
                placeholder="Select education level"
                selectedKeys={
                  formData.highestEducation ? [formData.highestEducation] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("highestEducation", value);
                }}
                isInvalid={!!errors.highestEducation}
                errorMessage={errors.highestEducation}
              >
                <SelectItem key="high_school">High School</SelectItem>
                <SelectItem key="diploma">Diploma</SelectItem>
                <SelectItem key="bachelors">Bachelor's Degree</SelectItem>
                <SelectItem key="masters">Master's Degree</SelectItem>
                <SelectItem key="doctorate">Doctorate</SelectItem>
                <SelectItem key="professional">Professional Degree</SelectItem>
              </Select>

              <Input
                label="Field of Study"
                placeholder="e.g., Computer Science, Medicine"
                value={formData.educationField}
                onChange={(e) =>
                  updateFormData("educationField", e.target.value)
                }
              />

              <Input
                label="Institution Name"
                placeholder="Enter institution name"
                value={formData.institutionName}
                onChange={(e) =>
                  updateFormData("institutionName", e.target.value)
                }
              />

              <Input
                label="Graduation Year"
                type="number"
                min="1980"
                max={new Date().getFullYear()}
                value={formData.graduationYear.toString()}
                onChange={(e) =>
                  updateFormData(
                    "graduationYear",
                    parseInt(e.target.value) || new Date().getFullYear()
                  )
                }
              />
            </div>

            <Divider />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Occupation"
                placeholder="Enter your occupation"
                value={formData.occupation}
                onChange={(e) => updateFormData("occupation", e.target.value)}
              />

              <Input
                label="Designation"
                placeholder="Enter your designation"
                value={formData.designation}
                onChange={(e) => updateFormData("designation", e.target.value)}
              />

              <Input
                label="Company Name"
                placeholder="Enter company name"
                value={formData.companyName}
                onChange={(e) => updateFormData("companyName", e.target.value)}
              />

              <Select
                label="Company Type"
                placeholder="Select company type"
                selectedKeys={
                  formData.companyType ? [formData.companyType] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("companyType", value);
                }}
              >
                <SelectItem key="government">Government</SelectItem>
                <SelectItem key="private">Private</SelectItem>
                <SelectItem key="business">Business</SelectItem>
                <SelectItem key="self_employed">Self Employed</SelectItem>
                <SelectItem key="not_working">Not Working</SelectItem>
              </Select>

              <Input
                label="Work Experience (Years)"
                type="number"
                min="0"
                value={formData.workExperienceYears.toString()}
                onChange={(e) =>
                  updateFormData(
                    "workExperienceYears",
                    parseInt(e.target.value) || 0
                  )
                }
              />

              <Select
                label="Annual Income"
                placeholder="Select income range"
                selectedKeys={
                  formData.annualIncomeRange ? [formData.annualIncomeRange] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("annualIncomeRange", value);
                }}
              >
                <SelectItem key="below_2_lakhs">Below 2 Lakhs</SelectItem>
                <SelectItem key="2_3_lakhs">2-3 Lakhs</SelectItem>
                <SelectItem key="3_5_lakhs">3-5 Lakhs</SelectItem>
                <SelectItem key="5_7_lakhs">5-7 Lakhs</SelectItem>
                <SelectItem key="7_10_lakhs">7-10 Lakhs</SelectItem>
                <SelectItem key="10_15_lakhs">10-15 Lakhs</SelectItem>
                <SelectItem key="15_20_lakhs">15-20 Lakhs</SelectItem>
                <SelectItem key="20_30_lakhs">20-30 Lakhs</SelectItem>
                <SelectItem key="30_50_lakhs">30-50 Lakhs</SelectItem>
                <SelectItem key="above_50_lakhs">Above 50 Lakhs</SelectItem>
              </Select>

              <Input
                label="Work City"
                placeholder="Enter work city"
                value={formData.workCity}
                onChange={(e) => updateFormData("workCity", e.target.value)}
              />
            </div>

            <Checkbox
              isSelected={formData.willingToRelocateForWork}
              onValueChange={(checked) =>
                updateFormData("willingToRelocateForWork", checked)
              }
            >
              Willing to relocate for work
            </Checkbox>

            <Textarea
              label="About Your Profession"
              placeholder="Describe your work, achievements, and career goals..."
              value={formData.aboutProfession}
              onChange={(e) =>
                updateFormData("aboutProfession", e.target.value)
              }
              maxRows={3}
            />
          </div>
        );

      case "lifestyle":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Lifestyle Preferences</h2>
              <p className="text-default-500">
                Share your lifestyle and habits
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Select
                label="Diet"
                placeholder="Select diet preference"
                selectedKeys={formData.diet ? [formData.diet] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("diet", value);
                }}
                isInvalid={!!errors.diet}
                errorMessage={errors.diet}
              >
                <SelectItem key="vegetarian">Vegetarian</SelectItem>
                <SelectItem key="non_vegetarian">Non-Vegetarian</SelectItem>
                <SelectItem key="vegan">Vegan</SelectItem>
                <SelectItem key="jain_food">Jain Food</SelectItem>
                <SelectItem key="occasionally_non_veg">
                  Occasionally Non-Veg
                </SelectItem>
              </Select>

              <Select
                label="Smoking"
                placeholder="Select smoking habit"
                selectedKeys={formData.smoking ? [formData.smoking] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("smoking", value);
                }}
              >
                <SelectItem key="never">Never</SelectItem>
                <SelectItem key="occasionally">Occasionally</SelectItem>
                <SelectItem key="regularly">Regularly</SelectItem>
                <SelectItem key="trying_to_quit">Trying to Quit</SelectItem>
              </Select>

              <Select
                label="Drinking"
                placeholder="Select drinking habit"
                selectedKeys={formData.drinking ? [formData.drinking] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("drinking", value);
                }}
              >
                <SelectItem key="never">Never</SelectItem>
                <SelectItem key="occasionally">Occasionally</SelectItem>
                <SelectItem key="socially">Socially</SelectItem>
                <SelectItem key="regularly">Regularly</SelectItem>
              </Select>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Fitness Level"
                placeholder="Select fitness level"
                selectedKeys={
                  formData.fitnessLevel ? [formData.fitnessLevel] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("fitnessLevel", value);
                }}
              >
                <SelectItem key="low">Low</SelectItem>
                <SelectItem key="moderate">Moderate</SelectItem>
                <SelectItem key="high">High</SelectItem>
                <SelectItem key="fitness_freak">Fitness Freak</SelectItem>
              </Select>

              <Select
                label="Party Preference"
                placeholder="Select party preference"
                selectedKeys={
                  formData.partyPreference ? [formData.partyPreference] : []
                }
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("partyPreference", value);
                }}
              >
                <SelectItem key="never">Never</SelectItem>
                <SelectItem key="occasionally">Occasionally</SelectItem>
                <SelectItem key="often">Often</SelectItem>
                <SelectItem key="love_parties">Love Parties</SelectItem>
              </Select>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Music Preferences
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "Classical",
                    "Bollywood",
                    "Pop",
                    "Rock",
                    "Jazz",
                    "Folk",
                    "Devotional",
                  ].map((music) => (
                    <Chip
                      key={music}
                      variant={
                        formData.musicPreferences.includes(music)
                          ? "solid"
                          : "bordered"
                      }
                      color={
                        formData.musicPreferences.includes(music)
                          ? "primary"
                          : "default"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const current = formData.musicPreferences;
                        if (current.includes(music)) {
                          updateFormData(
                            "musicPreferences",
                            current.filter((m) => m !== music)
                          );
                        } else {
                          updateFormData("musicPreferences", [
                            ...current,
                            music,
                          ]);
                        }
                      }}
                    >
                      {music}
                    </Chip>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Sports Interests
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    "Cricket",
                    "Football",
                    "Tennis",
                    "Badminton",
                    "Swimming",
                    "Yoga",
                    "Gym",
                    "Running",
                  ].map((sport) => (
                    <Chip
                      key={sport}
                      variant={
                        formData.sportsInterests.includes(sport)
                          ? "solid"
                          : "bordered"
                      }
                      color={
                        formData.sportsInterests.includes(sport)
                          ? "primary"
                          : "default"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const current = formData.sportsInterests;
                        if (current.includes(sport)) {
                          updateFormData(
                            "sportsInterests",
                            current.filter((s) => s !== sport)
                          );
                        } else {
                          updateFormData("sportsInterests", [
                            ...current,
                            sport,
                          ]);
                        }
                      }}
                    >
                      {sport}
                    </Chip>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );

      case "religious":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Religious Details</h2>
              <p className="text-default-500">
                Share your religious and astrological information
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select
                label="Religion"
                placeholder="Select religion"
                selectedKeys={formData.religion ? [formData.religion] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  updateFormData("religion", value);
                }}
                isInvalid={!!errors.religion}
                errorMessage={errors.religion}
              >
                <SelectItem key="hindu">Hindu</SelectItem>
                <SelectItem key="muslim">Muslim</SelectItem>
                <SelectItem key="christian">Christian</SelectItem>
                <SelectItem key="sikh">Sikh</SelectItem>
                <SelectItem key="buddhist">Buddhist</SelectItem>
                <SelectItem key="jain">Jain</SelectItem>
                <SelectItem key="parsi">Parsi</SelectItem>
                <SelectItem key="other">Other</SelectItem>
              </Select>

              <Input
                label="Caste"
                placeholder="Enter caste"
                value={formData.caste}
                onChange={(e) => updateFormData("caste", e.target.value)}
              />

              <Input
                label="Sub Caste"
                placeholder="Enter sub caste"
                value={formData.subCaste}
                onChange={(e) => updateFormData("subCaste", e.target.value)}
              />

              <Input
                label="Gothra"
                placeholder="Enter gothra"
                value={formData.gothra}
                onChange={(e) => updateFormData("gothra", e.target.value)}
              />

              <Input
                label="Star/Nakshatra"
                placeholder="Enter star/nakshatra"
                value={formData.starNakshatra}
                onChange={(e) =>
                  updateFormData("starNakshatra", e.target.value)
                }
              />

              <Input
                label="Rashi/Moon Sign"
                placeholder="Enter rashi/moon sign"
                value={formData.rashiMoonSign}
                onChange={(e) =>
                  updateFormData("rashiMoonSign", e.target.value)
                }
              />
            </div>

            <Select
              label="Manglik Status"
              placeholder="Select manglik status"
              selectedKeys={
                formData.manglikStatus ? [formData.manglikStatus] : []
              }
              onSelectionChange={(keys) => {
                const value = Array.from(keys)[0] as string;
                updateFormData("manglikStatus", value);
              }}
            >
              <SelectItem key="yes">Yes</SelectItem>
              <SelectItem key="no">No</SelectItem>
              <SelectItem key="anshik">Anshik</SelectItem>
              <SelectItem key="dont_know">Don't Know</SelectItem>
            </Select>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Birth Time"
                type="time"
                value={formData.birthTime}
                onChange={(e) => updateFormData("birthTime", e.target.value)}
              />

              <Input
                label="Birth Place"
                placeholder="Enter birth place"
                value={formData.birthPlace}
                onChange={(e) => updateFormData("birthPlace", e.target.value)}
              />
            </div>

            <Checkbox
              isSelected={formData.horoscopeMatchRequired}
              onValueChange={(checked) =>
                updateFormData("horoscopeMatchRequired", checked)
              }
            >
              Horoscope matching required for marriage
            </Checkbox>
          </div>
        );

      case "preferences":
        return (
          <div className="space-y-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold mb-2">Partner Preferences</h2>
              <p className="text-default-500">
                Tell us about your ideal partner
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Age Range: {formData.ageMin} - {formData.ageMax} years
                </label>
                <Slider
                  step={1}
                  minValue={18}
                  maxValue={60}
                  value={[formData.ageMin, formData.ageMax]}
                  onChange={(value) => {
                    const [min, max] = value as number[];
                    updateFormData("ageMin", min);
                    updateFormData("ageMax", max);
                  }}
                  className="max-w-md"
                />
                {errors.ageRange && (
                  <p className="text-danger text-sm mt-1">{errors.ageRange}</p>
                )}
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Height Range: {formData.heightMinCm} - {formData.heightMaxCm}{" "}
                  cm
                </label>
                <Slider
                  step={1}
                  minValue={140}
                  maxValue={200}
                  value={[formData.heightMinCm, formData.heightMaxCm]}
                  onChange={(value) => {
                    const [min, max] = value as number[];
                    updateFormData("heightMinCm", min);
                    updateFormData("heightMaxCm", max);
                  }}
                  className="max-w-md"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Preferred Marital Status
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { key: "never_married", label: "Never Married" },
                    { key: "divorced", label: "Divorced" },
                    { key: "widowed", label: "Widowed" },
                    { key: "separated", label: "Separated" },
                  ].map((status) => (
                    <Chip
                      key={status.key}
                      variant={
                        formData.preferredMaritalStatus.includes(status.key)
                          ? "solid"
                          : "bordered"
                      }
                      color={
                        formData.preferredMaritalStatus.includes(status.key)
                          ? "primary"
                          : "default"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const current = formData.preferredMaritalStatus;
                        if (current.includes(status.key)) {
                          updateFormData(
                            "preferredMaritalStatus",
                            current.filter((s) => s !== status.key)
                          );
                        } else {
                          updateFormData("preferredMaritalStatus", [
                            ...current,
                            status.key,
                          ]);
                        }
                      }}
                    >
                      {status.label}
                    </Chip>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Preferred Education Levels
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { key: "high_school", label: "High School" },
                    { key: "diploma", label: "Diploma" },
                    { key: "bachelors", label: "Bachelor's" },
                    { key: "masters", label: "Master's" },
                    { key: "doctorate", label: "Doctorate" },
                    { key: "professional", label: "Professional" },
                  ].map((education) => (
                    <Chip
                      key={education.key}
                      variant={
                        formData.preferredEducationLevels.includes(
                          education.key
                        )
                          ? "solid"
                          : "bordered"
                      }
                      color={
                        formData.preferredEducationLevels.includes(
                          education.key
                        )
                          ? "primary"
                          : "default"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const current = formData.preferredEducationLevels;
                        if (current.includes(education.key)) {
                          updateFormData(
                            "preferredEducationLevels",
                            current.filter((e) => e !== education.key)
                          );
                        } else {
                          updateFormData("preferredEducationLevels", [
                            ...current,
                            education.key,
                          ]);
                        }
                      }}
                    >
                      {education.label}
                    </Chip>
                  ))}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium mb-2 block">
                  Preferred Diet
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { key: "vegetarian", label: "Vegetarian" },
                    { key: "non_vegetarian", label: "Non-Vegetarian" },
                    { key: "vegan", label: "Vegan" },
                    { key: "jain_food", label: "Jain Food" },
                  ].map((diet) => (
                    <Chip
                      key={diet.key}
                      variant={
                        formData.preferredDiet.includes(diet.key)
                          ? "solid"
                          : "bordered"
                      }
                      color={
                        formData.preferredDiet.includes(diet.key)
                          ? "primary"
                          : "default"
                      }
                      className="cursor-pointer"
                      onClick={() => {
                        const current = formData.preferredDiet;
                        if (current.includes(diet.key)) {
                          updateFormData(
                            "preferredDiet",
                            current.filter((d) => d !== diet.key)
                          );
                        } else {
                          updateFormData("preferredDiet", [
                            ...current,
                            diet.key,
                          ]);
                        }
                      }}
                    >
                      {diet.label}
                    </Chip>
                  ))}
                </div>
              </div>
            </div>

            <Textarea
              label="Partner Expectations"
              placeholder="Describe your expectations from your life partner..."
              value={formData.partnerExpectations}
              onChange={(e) =>
                updateFormData("partnerExpectations", e.target.value)
              }
              maxRows={4}
            />
          </div>
        );

      default:
        return <div>Step content for {currentStep}</div>;
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon
            icon="lucide:user-x"
            size={48}
            className="mx-auto mb-4 text-default-400"
          />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">
            You need to log in to set up your profile.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Progress Header */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold mb-2">Complete Your Profile</h1>
            <p className="text-default-500">
              Help us find your perfect match by completing your profile
            </p>
          </div>

          {/* Step Navigation */}
          <div className="flex justify-between items-center mb-4">
            {setupSteps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div
                  className={`
                  flex items-center justify-center w-10 h-10 rounded-full border-2 cursor-pointer
                  ${
                    currentStep === step.id
                      ? "bg-primary text-white border-primary"
                      : getCurrentStepIndex() > index
                      ? "bg-success text-white border-success"
                      : "bg-white text-default-400 border-default-200"
                  }
                `}
                  onClick={() => setCurrentStep(step.id)}
                >
                  {getCurrentStepIndex() > index ? (
                    <Icon icon="lucide:check" size={20} />
                  ) : (
                    <Icon icon={step.icon} size={20} />
                  )}
                </div>
                {index < setupSteps.length - 1 && (
                  <div
                    className={`
                    w-full h-0.5 mx-2
                    ${
                      getCurrentStepIndex() > index
                        ? "bg-success"
                        : "bg-default-200"
                    }
                  `}
                  />
                )}
              </div>
            ))}
          </div>

          <div className="flex justify-between text-sm">
            {setupSteps.map((step) => (
              <span
                key={step.id}
                className={`
                  cursor-pointer
                  ${
                    currentStep === step.id
                      ? "text-primary font-medium"
                      : "text-default-400"
                  }
                `}
                onClick={() => setCurrentStep(step.id)}
              >
                {step.title}
              </span>
            ))}
          </div>

          <Progress
            value={getProgressPercentage()}
            color="primary"
            className="mt-4"
          />
        </div>

        {/* Form Card */}
        <Card className="shadow-lg">
          <CardBody className="p-8">
            {renderStepContent()}

            {errors.submit && (
              <div className="mt-4 p-3 bg-danger-50 border border-danger-200 rounded-lg">
                <p className="text-danger text-sm">{errors.submit}</p>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8">
              <Button
                variant="flat"
                onPress={handlePrevious}
                isDisabled={getCurrentStepIndex() === 0}
                startContent={<Icon icon="lucide:chevron-left" />}
              >
                Previous
              </Button>

              {getCurrentStepIndex() < setupSteps.length - 1 ? (
                <Button
                  color="primary"
                  onPress={handleNext}
                  isLoading={loading}
                  endContent={<Icon icon="lucide:chevron-right" />}
                >
                  Save & Continue
                </Button>
              ) : (
                <Button
                  color="success"
                  onPress={handleComplete}
                  isLoading={loading}
                  endContent={<Icon icon="lucide:check" />}
                >
                  Complete Profile
                </Button>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Skip Option */}
        <div className="text-center mt-6">
          <Button
            variant="light"
            onPress={() => navigate("/dashboard")}
            className="text-default-500"
          >
            Skip for now (you can complete this later)
          </Button>
        </div>
      </div>
    </div>
  );
};
