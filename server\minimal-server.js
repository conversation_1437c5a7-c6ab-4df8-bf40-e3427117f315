const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();

// Basic middleware
app.use(cors());
app.use(express.json());

// Test route
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Minimal server is working!',
    timestamp: new Date().toISOString()
  });
});

// Simple auth test route
app.post('/api/test/register', (req, res) => {
  res.json({
    success: true,
    message: 'Test registration endpoint',
    data: req.body
  });
});

const PORT = process.env.PORT || 5000;

app.listen(PORT, () => {
  console.log(`Minimal server is running on port ${PORT}`);
  console.log(`Test it at: http://localhost:${PORT}/api/health`);
});
