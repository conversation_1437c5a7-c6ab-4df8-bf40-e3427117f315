import React from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Tabs,
  Tab,
  Avatar,
  Divider,
  <PERSON>,
  Badge,
  Image,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAuth } from "../contexts/auth-context";
import { User, ProfileMatch } from "../types/user";

interface ProfileParams {
  id: string;
}

// Mock profile data
const mockProfile: User = {
  id: "101",
  name: "<PERSON><PERSON><PERSON>",
  email: "<EMAIL>",
  phone: "+91 9876543210",
  profileCompleted: true,
  profileType: "self",
  membershipType: "platinum",
  profilePicture: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
  verificationStatus: {
    email: true,
    phone: true,
    photo: true,
    document: true,
  },
  createdAt: "2023-01-15T10:30:00Z",
  personalInfo: {
    firstName: "<PERSON><PERSON><PERSON>",
    lastName: "<PERSON>",
    gender: "female",
    dateOfBirth: "1995-06-12",
    age: 28,
    height: 165,
    weight: 58,
    maritalStatus: "never_married",
    physicalStatus: "normal",
    complexion: "fair",
    bodyType: "slim",
    aboutMe:
      "I am a software engineer by profession and love to travel and explore new places. I enjoy reading books, listening to music, and cooking in my free time. I believe in living life to the fullest and value family bonds.",
    location: {
      country: "India",
      state: "Maharashtra",
      city: "Mumbai",
      willingToRelocate: true,
    },
    religion: "hindu",
    caste: "brahmin",
    subCaste: "kanyakubja",
    gothra: "kashyap",
    motherTongue: "hindi",
    knownLanguages: ["hindi", "english", "marathi"],
    photos: [
      "https://img.heroui.chat/image/avatar?w=400&h=600&u=2",
      "https://img.heroui.chat/image/avatar?w=400&h=600&u=12",
      "https://img.heroui.chat/image/avatar?w=400&h=600&u=22",
    ],
  },
  familyInfo: {
    familyType: "nuclear",
    familyStatus: "upper_middle_class",
    familyValues: "moderate",
    fatherOccupation: "Government Officer",
    motherOccupation: "Teacher",
    fatherStatus: "alive",
    motherStatus: "alive",
    brothers: 1,
    sisters: 1,
    marriedBrothers: 0,
    marriedSisters: 1,
    familyIncome: "above_50",
    familyBasedOut: "Mumbai, Maharashtra",
    aboutFamily:
      "We are a close-knit family with modern values while respecting our traditions. My father works as a government officer and my mother is a school teacher. I have one brother who is studying engineering and one sister who is married and settled in Pune.",
  },
  educationCareer: {
    highestEducation: "bachelor",
    educationField: "Computer Science",
    university: "Mumbai University",
    yearOfPassing: "2017",
    otherQualifications: "AWS Certified Solutions Architect",
    occupation: "software",
    employedIn: "private",
    companyName: "Tech Solutions Ltd.",
    designation: "Senior Software Engineer",
    workLocation: "Mumbai",
    annualIncome: "15_20",
    workExperience: 5,
    careerDescription:
      "I have been working in the IT industry for 5 years, specializing in web development and cloud solutions. I enjoy solving complex problems and learning new technologies.",
  },
  lifestyle: {
    diet: "vegetarian",
    drinking: "no",
    smoking: "no",
    hobbies: ["Reading", "Traveling", "Cooking", "Music"],
    interests: ["Technology", "Photography", "Yoga"],
    dressStyle: "mixed",
  },
  partnerPreferences: {
    ageRange: {
      min: 27,
      max: 33,
    },
    heightRange: {
      min: 170,
      max: 190,
    },
    maritalStatus: ["never_married"],
    religion: ["hindu"],
    caste: ["brahmin", "kshatriya", "vaishya"],
    education: ["bachelor", "master"],
    occupation: ["software", "engineer", "doctor", "business"],
    incomeRange: {
      min: 1000000,
      max: 5000000,
    },
    dietPreference: ["vegetarian", "eggetarian"],
    drinkingPreference: ["no", "occasionally"],
    smokingPreference: ["no"],
    locationPreference: {
      countries: ["India"],
      states: ["Maharashtra", "Karnataka", "Delhi"],
      cities: ["Mumbai", "Pune", "Bangalore", "Delhi"],
    },
    manglikStatus: "doesnt_matter",
    lookingFor:
      "I am looking for a well-educated, family-oriented partner who shares similar values and interests. Someone who is kind, respectful, and has a good sense of humor. Career-oriented but also values work-life balance.",
  },
  horoscope: {
    manglikStatus: "no",
    timeOfBirth: "08:30",
    placeOfBirth: "Mumbai, Maharashtra",
    nakshatra: "rohini",
    rashi: "taurus",
    horoscopeMatch: true,
    houses: {
      "1": ["Sun"],
      "2": ["Venus"],
      "4": ["Mars"],
      "7": ["Moon"],
      "9": ["Jupiter"],
      "10": ["Saturn"],
      "11": ["Rahu"],
      "6": ["Ketu"],
    },
  },
};

// Similar profiles
const similarProfiles: ProfileMatch[] = [
  {
    id: "2",
    userId: "102",
    name: "Neha Patel",
    age: 26,
    location: "Bangalore, Karnataka",
    profession: "Data Scientist",
    education: "M.Sc, Statistics",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3",
    compatibilityScore: 78,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-14T14:20:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: true,
  },
  {
    id: "3",
    userId: "103",
    name: "Pooja Verma",
    age: 27,
    location: "Delhi, NCR",
    profession: "Marketing Manager",
    education: "MBA, Marketing",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4",
    compatibilityScore: 72,
    isPremium: true,
    isVerified: true,
    lastActive: "2023-06-15T09:15:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: true,
  },
  {
    id: "4",
    userId: "104",
    name: "Kavita Singh",
    age: 29,
    location: "Pune, Maharashtra",
    profession: "HR Manager",
    education: "MBA, Human Resources",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=5",
    compatibilityScore: 68,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-15T11:45:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
];

export const ProfilePage: React.FC = () => {
  const { id } = useParams<ProfileParams>();
  const { user } = useAuth();
  const [activeTab, setActiveTab] = React.useState("about");
  const [profile, setProfile] = React.useState<User | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const [interestSent, setInterestSent] = React.useState(false);
  const [isShortlisted, setIsShortlisted] = React.useState(false);
  const [selectedPhotoIndex, setSelectedPhotoIndex] = React.useState(0);

  React.useEffect(() => {
    // Simulate API call to fetch profile data
    const fetchProfile = async () => {
      setIsLoading(true);
      try {
        // In a real app, this would be an API call
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // For demo purposes, use mock data
        setProfile(mockProfile);
      } catch (error) {
        console.error("Error fetching profile:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [id]);

  const handleSendInterest = () => {
    setInterestSent(true);
  };

  const handleShortlist = () => {
    setIsShortlisted(!isShortlisted);
  };

  const handleContactNow = () => {
    // Implement contact functionality
    console.log("Contact now clicked");
  };

  const handleTabChange = (key: React.Key) => {
    setActiveTab(key as string);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-default-500">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen flex justify-center items-center">
        <div className="text-center">
          <Icon
            icon="lucide:user-x"
            className="text-6xl text-default-400 mx-auto"
          />
          <h2 className="text-2xl font-bold mt-4">Profile Not Found</h2>
          <p className="mt-2 text-default-500">
            The profile you are looking for does not exist or has been removed.
          </p>
          <Link to="/search">
            <Button color="primary" className="mt-6">
              Back to Search
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  const getAgeFromDOB = (dob: string) => {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const getHeightInFeetInches = (heightInCm: number) => {
    const feet = Math.floor(heightInCm / 30.48);
    const inches = Math.round((heightInCm / 2.54) % 12);
    return `${feet}'${inches}" (${heightInCm} cm)`;
  };

  const getIncomeRange = (incomeKey: string) => {
    const incomeMap: Record<string, string> = {
      below_5: "Below 5 Lakhs",
      "5_10": "5-10 Lakhs",
      "10_15": "10-15 Lakhs",
      "15_20": "15-20 Lakhs",
      "20_30": "20-30 Lakhs",
      "30_50": "30-50 Lakhs",
      above_50: "Above 50 Lakhs",
    };

    return incomeMap[incomeKey] || incomeKey;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Profile Summary */}
          <div className="lg:col-span-1">
            <Card className="mb-6">
              <CardBody className="flex flex-col items-center text-center">
                <div className="relative mb-4">
                  {profile.verificationStatus.photo ? (
                    <Badge
                      content={
                        <Icon icon="lucide:check" className="text-white" />
                      }
                      color="success"
                      placement="bottom-right"
                      className="border-2 border-white"
                    >
                      <Avatar
                        src={profile.profilePicture}
                        className="w-32 h-32"
                        isBordered
                        color="primary"
                      />
                    </Badge>
                  ) : (
                    <Avatar
                      src={profile.profilePicture}
                      className="w-32 h-32"
                      isBordered
                      color="primary"
                    />
                  )}
                </div>

                <h2 className="text-2xl font-semibold">{profile.name}</h2>
                <p className="text-default-500 text-sm">ID: BM{profile.id}</p>

                <div className="flex items-center gap-2 mt-2">
                  <Chip color="primary" variant="flat" size="sm">
                    {profile.membershipType.charAt(0).toUpperCase() +
                      profile.membershipType.slice(1)}{" "}
                    Member
                  </Chip>

                  {profile.verificationStatus.email && (
                    <Chip
                      color="success"
                      variant="flat"
                      size="sm"
                      startContent={
                        <Icon icon="lucide:check" className="text-success" />
                      }
                    >
                      Verified
                    </Chip>
                  )}
                </div>

                <Divider className="my-4 w-full" />

                <div className="w-full space-y-2">
                  <div className="flex justify-between">
                    <span className="text-default-500">Age</span>
                    <span className="font-medium">
                      {profile.personalInfo?.age} years
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-default-500">Height</span>
                    <span className="font-medium">
                      {getHeightInFeetInches(profile.personalInfo?.height || 0)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-default-500">Religion</span>
                    <span className="font-medium">
                      {profile.personalInfo?.religion.charAt(0).toUpperCase() +
                        profile.personalInfo?.religion.slice(1)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-default-500">Mother Tongue</span>
                    <span className="font-medium">
                      {profile.personalInfo?.motherTongue
                        .charAt(0)
                        .toUpperCase() +
                        profile.personalInfo?.motherTongue.slice(1)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-default-500">Location</span>
                    <span className="font-medium">
                      {profile.personalInfo?.location.city},{" "}
                      {profile.personalInfo?.location.state}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-default-500">Profession</span>
                    <span className="font-medium">
                      {profile.educationCareer?.designation}
                    </span>
                  </div>
                </div>

                <Divider className="my-4 w-full" />

                <div className="grid grid-cols-1 w-full gap-3">
                  <Button
                    color="primary"
                    startContent={<Icon icon="lucide:heart" />}
                    onPress={handleSendInterest}
                    isDisabled={interestSent}
                  >
                    {interestSent ? "Interest Sent" : "Send Interest"}
                  </Button>

                  <Button
                    color="success"
                    variant="flat"
                    startContent={<Icon icon="lucide:phone" />}
                    onPress={handleContactNow}
                  >
                    Contact Now
                  </Button>

                  <Button
                    variant="flat"
                    color={isShortlisted ? "warning" : "default"}
                    startContent={<Icon icon="lucide:star" />}
                    onPress={handleShortlist}
                  >
                    {isShortlisted ? "Shortlisted" : "Shortlist"}
                  </Button>
                </div>
              </CardBody>
            </Card>

            <Card>
              <CardHeader className="flex gap-3">
                <Icon
                  icon="lucide:shield-check"
                  className="text-primary text-xl"
                />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Verification Status</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Email</span>
                    {profile.verificationStatus.email ? (
                      <Chip color="success" size="sm" variant="flat">
                        Verified
                      </Chip>
                    ) : (
                      <Chip color="warning" size="sm" variant="flat">
                        Pending
                      </Chip>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm">Phone</span>
                    {profile.verificationStatus.phone ? (
                      <Chip color="success" size="sm" variant="flat">
                        Verified
                      </Chip>
                    ) : (
                      <Chip color="warning" size="sm" variant="flat">
                        Pending
                      </Chip>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm">Photo</span>
                    {profile.verificationStatus.photo ? (
                      <Chip color="success" size="sm" variant="flat">
                        Verified
                      </Chip>
                    ) : (
                      <Chip color="warning" size="sm" variant="flat">
                        Pending
                      </Chip>
                    )}
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm">Documents</span>
                    {profile.verificationStatus.document ? (
                      <Chip color="success" size="sm" variant="flat">
                        Verified
                      </Chip>
                    ) : (
                      <Chip color="warning" size="sm" variant="flat">
                        Pending
                      </Chip>
                    )}
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Middle & Right Columns - Profile Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Photos Section */}
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Photos</h3>
              </CardHeader>
              <CardBody>
                <div className="space-y-4">
                  <div className="w-full">
                    <Image
                      src={
                        profile.personalInfo?.photos[selectedPhotoIndex] ||
                        profile.profilePicture
                      }
                      alt={`${profile.name} - Photo ${selectedPhotoIndex + 1}`}
                      className="w-full h-auto max-h-[500px] object-contain rounded-lg"
                    />
                  </div>

                  <div className="flex gap-2 overflow-x-auto py-2">
                    {profile.personalInfo?.photos.map((photo, index) => (
                      <div
                        key={index}
                        className={`cursor-pointer rounded-md overflow-hidden border-2 ${
                          selectedPhotoIndex === index
                            ? "border-primary"
                            : "border-transparent"
                        }`}
                        onClick={() => setSelectedPhotoIndex(index)}
                      >
                        <img
                          src={photo}
                          alt={`${profile.name} - Thumbnail ${index + 1}`}
                          className="w-20 h-20 object-cover"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Profile Tabs */}
            <Card>
              <CardBody>
                <Tabs
                  aria-label="Profile Details"
                  selectedKey={activeTab}
                  onSelectionChange={handleTabChange}
                  className="w-full"
                >
                  <Tab
                    key="about"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:user" />
                        <span>About</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">
                        About {profile.name}
                      </h3>
                      <p className="text-default-700 mb-6">
                        {profile.personalInfo?.aboutMe}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Basic Details
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">Name</span>
                              <span>
                                {profile.personalInfo?.firstName}{" "}
                                {profile.personalInfo?.lastName}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Age</span>
                              <span>{profile.personalInfo?.age} years</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Height</span>
                              <span>
                                {getHeightInFeetInches(
                                  profile.personalInfo?.height || 0
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Weight</span>
                              <span>{profile.personalInfo?.weight} kg</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Marital Status
                              </span>
                              <span>
                                {profile.personalInfo?.maritalStatus ===
                                "never_married"
                                  ? "Never Married"
                                  : profile.personalInfo?.maritalStatus ===
                                    "divorced"
                                  ? "Divorced"
                                  : profile.personalInfo?.maritalStatus ===
                                    "widowed"
                                  ? "Widowed"
                                  : "Awaiting Divorce"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Physical Status
                              </span>
                              <span>
                                {profile.personalInfo?.physicalStatus ===
                                "normal"
                                  ? "Normal"
                                  : "Physically Challenged"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Complexion
                              </span>
                              <span>
                                {profile.personalInfo?.complexion
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.personalInfo?.complexion.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Body Type
                              </span>
                              <span>
                                {profile.personalInfo?.bodyType
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.personalInfo?.bodyType.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Religious Background
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">Religion</span>
                              <span>
                                {profile.personalInfo?.religion
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.personalInfo?.religion.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Caste</span>
                              <span>
                                {profile.personalInfo?.caste
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.personalInfo?.caste.slice(1)}
                              </span>
                            </div>
                            {profile.personalInfo?.subCaste && (
                              <div className="flex justify-between">
                                <span className="text-default-500">
                                  Sub-caste
                                </span>
                                <span>
                                  {profile.personalInfo?.subCaste
                                    .charAt(0)
                                    .toUpperCase() +
                                    profile.personalInfo?.subCaste.slice(1)}
                                </span>
                              </div>
                            )}
                            {profile.personalInfo?.gothra && (
                              <div className="flex justify-between">
                                <span className="text-default-500">Gothra</span>
                                <span>{profile.personalInfo?.gothra}</span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Mother Tongue
                              </span>
                              <span>
                                {profile.personalInfo?.motherTongue
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.personalInfo?.motherTongue.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Known Languages
                              </span>
                              <span>
                                {profile.personalInfo?.knownLanguages
                                  .map(
                                    (lang) =>
                                      lang.charAt(0).toUpperCase() +
                                      lang.slice(1)
                                  )
                                  .join(", ")}
                              </span>
                            </div>
                          </div>

                          <h4 className="text-md font-semibold mt-6 mb-3">
                            Location
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">Country</span>
                              <span>
                                {profile.personalInfo?.location.country}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">State</span>
                              <span>
                                {profile.personalInfo?.location.state}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">City</span>
                              <span>{profile.personalInfo?.location.city}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Willing to Relocate
                              </span>
                              <span>
                                {profile.personalInfo?.location
                                  .willingToRelocate
                                  ? "Yes"
                                  : "No"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="family"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:users" />
                        <span>Family</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">
                        Family Details
                      </h3>
                      <p className="text-default-700 mb-6">
                        {profile.familyInfo?.aboutFamily}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Family Background
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Family Type
                              </span>
                              <span>
                                {profile.familyInfo?.familyType
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.familyInfo?.familyType.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Family Status
                              </span>
                              <span>
                                {profile.familyInfo?.familyStatus ===
                                "middle_class"
                                  ? "Middle Class"
                                  : profile.familyInfo?.familyStatus ===
                                    "upper_middle_class"
                                  ? "Upper Middle Class"
                                  : profile.familyInfo?.familyStatus === "rich"
                                  ? "Rich"
                                  : "Affluent"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Family Values
                              </span>
                              <span>
                                {profile.familyInfo?.familyValues
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.familyInfo?.familyValues.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Family Income
                              </span>
                              <span>
                                {getIncomeRange(
                                  profile.familyInfo?.familyIncome || ""
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Family Based Out
                              </span>
                              <span>{profile.familyInfo?.familyBasedOut}</span>
                            </div>
                          </div>

                          <h4 className="text-md font-semibold mt-6 mb-3">
                            Parents
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Father's Occupation
                              </span>
                              <span>
                                {profile.familyInfo?.fatherOccupation}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Father's Status
                              </span>
                              <span>
                                {profile.familyInfo?.fatherStatus
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.familyInfo?.fatherStatus.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Mother's Occupation
                              </span>
                              <span>
                                {profile.familyInfo?.motherOccupation}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Mother's Status
                              </span>
                              <span>
                                {profile.familyInfo?.motherStatus
                                  .charAt(0)
                                  .toUpperCase() +
                                  profile.familyInfo?.motherStatus.slice(1)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Siblings
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">Brothers</span>
                              <span>{profile.familyInfo?.brothers}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Married Brothers
                              </span>
                              <span>{profile.familyInfo?.marriedBrothers}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Sisters</span>
                              <span>{profile.familyInfo?.sisters}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Married Sisters
                              </span>
                              <span>{profile.familyInfo?.marriedSisters}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="education"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:graduation-cap" />
                        <span>Education & Career</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">
                        Education & Career
                      </h3>
                      <p className="text-default-700 mb-6">
                        {profile.educationCareer?.careerDescription}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Education
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Highest Education
                              </span>
                              <span>
                                {profile.educationCareer?.highestEducation ===
                                "bachelor"
                                  ? "Bachelor's Degree"
                                  : profile.educationCareer
                                      ?.highestEducation === "master"
                                  ? "Master's Degree"
                                  : profile.educationCareer
                                      ?.highestEducation === "doctorate"
                                  ? "Doctorate"
                                  : profile.educationCareer
                                      ?.highestEducation === "diploma"
                                  ? "Diploma"
                                  : profile.educationCareer
                                      ?.highestEducation === "high_school"
                                  ? "High School"
                                  : "Other"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Education Field
                              </span>
                              <span>
                                {profile.educationCareer?.educationField}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                University/College
                              </span>
                              <span>{profile.educationCareer?.university}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Year of Passing
                              </span>
                              <span>
                                {profile.educationCareer?.yearOfPassing}
                              </span>
                            </div>
                            {profile.educationCareer?.otherQualifications && (
                              <div className="flex justify-between">
                                <span className="text-default-500">
                                  Other Qualifications
                                </span>
                                <span>
                                  {profile.educationCareer?.otherQualifications}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">Career</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Occupation
                              </span>
                              <span>
                                {profile.educationCareer?.occupation ===
                                "software"
                                  ? "Software Professional"
                                  : profile.educationCareer?.occupation ===
                                    "doctor"
                                  ? "Doctor"
                                  : profile.educationCareer?.occupation ===
                                    "engineer"
                                  ? "Engineer"
                                  : profile.educationCareer?.occupation ===
                                    "teacher"
                                  ? "Teacher"
                                  : profile.educationCareer?.occupation ===
                                    "business"
                                  ? "Business Owner"
                                  : profile.educationCareer?.occupation ===
                                    "government"
                                  ? "Government Employee"
                                  : "Other"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Employed In
                              </span>
                              <span>
                                {profile.educationCareer?.employedIn ===
                                "government"
                                  ? "Government"
                                  : profile.educationCareer?.employedIn ===
                                    "private"
                                  ? "Private"
                                  : profile.educationCareer?.employedIn ===
                                    "business"
                                  ? "Business"
                                  : profile.educationCareer?.employedIn ===
                                    "self_employed"
                                  ? "Self Employed"
                                  : "Not Working"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Company Name
                              </span>
                              <span>
                                {profile.educationCareer?.companyName}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Designation
                              </span>
                              <span>
                                {profile.educationCareer?.designation}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Work Location
                              </span>
                              <span>
                                {profile.educationCareer?.workLocation}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Annual Income
                              </span>
                              <span>
                                {getIncomeRange(
                                  profile.educationCareer?.annualIncome || ""
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Work Experience
                              </span>
                              <span>
                                {profile.educationCareer?.workExperience} years
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="lifestyle"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:heart" />
                        <span>Lifestyle</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">Lifestyle</h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">Habits</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">Diet</span>
                              <span>
                                {profile.lifestyle?.diet === "vegetarian"
                                  ? "Vegetarian"
                                  : profile.lifestyle?.diet === "non_vegetarian"
                                  ? "Non-Vegetarian"
                                  : profile.lifestyle?.diet === "eggetarian"
                                  ? "Eggetarian"
                                  : profile.lifestyle?.diet === "vegan"
                                  ? "Vegan"
                                  : "Jain"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Drinking</span>
                              <span>
                                {profile.lifestyle?.drinking === "no"
                                  ? "No"
                                  : profile.lifestyle?.drinking ===
                                    "occasionally"
                                  ? "Occasionally"
                                  : "Yes"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Smoking</span>
                              <span>
                                {profile.lifestyle?.smoking === "no"
                                  ? "No"
                                  : profile.lifestyle?.smoking ===
                                    "occasionally"
                                  ? "Occasionally"
                                  : "Yes"}
                              </span>
                            </div>
                            {profile.lifestyle?.dressStyle && (
                              <div className="flex justify-between">
                                <span className="text-default-500">
                                  Dress Style
                                </span>
                                <span>
                                  {profile.lifestyle?.dressStyle ===
                                  "traditional"
                                    ? "Traditional"
                                    : profile.lifestyle?.dressStyle === "modern"
                                    ? "Modern"
                                    : "Mixed"}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Interests & Hobbies
                          </h4>
                          <div className="space-y-4">
                            <div>
                              <span className="text-default-500 block mb-2">
                                Hobbies
                              </span>
                              <div className="flex flex-wrap gap-2">
                                {profile.lifestyle?.hobbies.map(
                                  (hobby, index) => (
                                    <Chip
                                      key={index}
                                      size="sm"
                                      variant="flat"
                                      color="primary"
                                    >
                                      {hobby}
                                    </Chip>
                                  )
                                )}
                              </div>
                            </div>

                            <div>
                              <span className="text-default-500 block mb-2">
                                Interests
                              </span>
                              <div className="flex flex-wrap gap-2">
                                {profile.lifestyle?.interests.map(
                                  (interest, index) => (
                                    <Chip
                                      key={index}
                                      size="sm"
                                      variant="flat"
                                      color="secondary"
                                    >
                                      {interest}
                                    </Chip>
                                  )
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="preferences"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:filter" />
                        <span>Preferences</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">
                        Partner Preferences
                      </h3>
                      <p className="text-default-700 mb-6">
                        {profile.partnerPreferences?.lookingFor}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Basic Preferences
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Age Range
                              </span>
                              <span>
                                {profile.partnerPreferences?.ageRange.min} to{" "}
                                {profile.partnerPreferences?.ageRange.max} years
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Height Range
                              </span>
                              <span>
                                {getHeightInFeetInches(
                                  profile.partnerPreferences?.heightRange.min ||
                                    0
                                )}{" "}
                                to{" "}
                                {getHeightInFeetInches(
                                  profile.partnerPreferences?.heightRange.max ||
                                    0
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Marital Status
                              </span>
                              <span>
                                {profile.partnerPreferences?.maritalStatus
                                  .map((status) =>
                                    status === "never_married"
                                      ? "Never Married"
                                      : status === "divorced"
                                      ? "Divorced"
                                      : status === "widowed"
                                      ? "Widowed"
                                      : "Awaiting Divorce"
                                  )
                                  .join(", ")}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Religion</span>
                              <span>
                                {profile.partnerPreferences?.religion
                                  .map(
                                    (rel) =>
                                      rel.charAt(0).toUpperCase() + rel.slice(1)
                                  )
                                  .join(", ")}
                              </span>
                            </div>
                            {profile.partnerPreferences?.caste.length && (
                              <div className="flex justify-between">
                                <span className="text-default-500">Caste</span>
                                <span>
                                  {profile.partnerPreferences?.caste
                                    .map(
                                      (c) =>
                                        c.charAt(0).toUpperCase() + c.slice(1)
                                    )
                                    .join(", ")}
                                </span>
                              </div>
                            )}
                          </div>

                          <h4 className="text-md font-semibold mt-6 mb-3">
                            Education & Career
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Education
                              </span>
                              <span>
                                {profile.partnerPreferences?.education
                                  .map((edu) =>
                                    edu === "bachelor"
                                      ? "Bachelor's Degree"
                                      : edu === "master"
                                      ? "Master's Degree"
                                      : edu === "doctorate"
                                      ? "Doctorate"
                                      : edu === "diploma"
                                      ? "Diploma"
                                      : edu === "high_school"
                                      ? "High School"
                                      : "Other"
                                  )
                                  .join(", ")}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Occupation
                              </span>
                              <span>
                                {profile.partnerPreferences?.occupation
                                  .map((occ) =>
                                    occ === "software"
                                      ? "Software Professional"
                                      : occ === "doctor"
                                      ? "Doctor"
                                      : occ === "engineer"
                                      ? "Engineer"
                                      : occ === "teacher"
                                      ? "Teacher"
                                      : occ === "business"
                                      ? "Business Owner"
                                      : occ === "government"
                                      ? "Government Employee"
                                      : "Other"
                                  )
                                  .join(", ")}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Annual Income
                              </span>
                              <span>
                                {profile.partnerPreferences?.incomeRange.min.toLocaleString()}{" "}
                                to{" "}
                                {profile.partnerPreferences?.incomeRange.max.toLocaleString()}{" "}
                                INR
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Lifestyle Preferences
                          </h4>
                          <div className="space-y-2">
                            {profile.partnerPreferences?.dietPreference && (
                              <div className="flex justify-between">
                                <span className="text-default-500">Diet</span>
                                <span>
                                  {profile.partnerPreferences?.dietPreference
                                    .map((diet) =>
                                      diet === "vegetarian"
                                        ? "Vegetarian"
                                        : diet === "non_vegetarian"
                                        ? "Non-Vegetarian"
                                        : diet === "eggetarian"
                                        ? "Eggetarian"
                                        : diet === "vegan"
                                        ? "Vegan"
                                        : "Jain"
                                    )
                                    .join(", ")}
                                </span>
                              </div>
                            )}
                            {profile.partnerPreferences?.drinkingPreference && (
                              <div className="flex justify-between">
                                <span className="text-default-500">
                                  Drinking
                                </span>
                                <span>
                                  {profile.partnerPreferences?.drinkingPreference
                                    .map((drink) =>
                                      drink === "no"
                                        ? "No"
                                        : drink === "occasionally"
                                        ? "Occasionally"
                                        : "Yes"
                                    )
                                    .join(", ")}
                                </span>
                              </div>
                            )}
                            {profile.partnerPreferences?.smokingPreference && (
                              <div className="flex justify-between">
                                <span className="text-default-500">
                                  Smoking
                                </span>
                                <span>
                                  {profile.partnerPreferences?.smokingPreference
                                    .map((smoke) =>
                                      smoke === "no"
                                        ? "No"
                                        : smoke === "occasionally"
                                        ? "Occasionally"
                                        : "Yes"
                                    )
                                    .join(", ")}
                                </span>
                              </div>
                            )}
                          </div>

                          <h4 className="text-md font-semibold mt-6 mb-3">
                            Location Preference
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Countries
                              </span>
                              <span>
                                {profile.partnerPreferences?.locationPreference.countries.join(
                                  ", "
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">States</span>
                              <span>
                                {profile.partnerPreferences?.locationPreference.states.join(
                                  ", "
                                )}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Cities</span>
                              <span>
                                {profile.partnerPreferences?.locationPreference.cities.join(
                                  ", "
                                )}
                              </span>
                            </div>
                          </div>

                          <h4 className="text-md font-semibold mt-6 mb-3">
                            Horoscope
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Manglik Status
                              </span>
                              <span>
                                {profile.partnerPreferences?.manglikStatus ===
                                "yes"
                                  ? "Yes"
                                  : profile.partnerPreferences
                                      ?.manglikStatus === "no"
                                  ? "No"
                                  : profile.partnerPreferences
                                      ?.manglikStatus === "anshik"
                                  ? "Anshik"
                                  : "Doesn't Matter"}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>

                  <Tab
                    key="horoscope"
                    title={
                      <div className="flex items-center gap-2">
                        <Icon icon="lucide:star" />
                        <span>Horoscope</span>
                      </div>
                    }
                  >
                    <div className="py-4">
                      <h3 className="text-lg font-semibold mb-3">
                        Horoscope Details
                      </h3>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Basic Details
                          </h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Date of Birth
                              </span>
                              <span>
                                {new Date(
                                  profile.personalInfo?.dateOfBirth || ""
                                ).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Time of Birth
                              </span>
                              <span>{profile.horoscope?.timeOfBirth}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Place of Birth
                              </span>
                              <span>{profile.horoscope?.placeOfBirth}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Manglik Status
                              </span>
                              <span>
                                {profile.horoscope?.manglikStatus === "yes"
                                  ? "Yes"
                                  : profile.horoscope?.manglikStatus === "no"
                                  ? "No"
                                  : profile.horoscope?.manglikStatus ===
                                    "anshik"
                                  ? "Anshik"
                                  : "Don't Know"}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Nakshatra
                              </span>
                              <span>
                                {profile.horoscope?.nakshatra
                                  ?.charAt(0)
                                  .toUpperCase() +
                                  profile.horoscope?.nakshatra
                                    ?.slice(1)
                                    .replace("_", " ")}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">Rashi</span>
                              <span>
                                {profile.horoscope?.rashi
                                  ?.charAt(0)
                                  .toUpperCase() +
                                  profile.horoscope?.rashi?.slice(1)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-default-500">
                                Horoscope Match Required
                              </span>
                              <span>
                                {profile.horoscope?.horoscopeMatch
                                  ? "Yes"
                                  : "No"}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="text-md font-semibold mb-3">
                            Kundli Chart
                          </h4>
                          <div className="grid grid-cols-3 gap-4 border-2 border-gray-200 p-4 rounded-lg">
                            {Object.entries(
                              profile.horoscope?.houses || {}
                            ).map(([house, planets]) => (
                              <div
                                key={house}
                                className="border p-3 rounded-md text-center"
                              >
                                <span className="font-semibold block mb-2">
                                  House {house}
                                </span>
                                <span className="text-sm text-default-600">
                                  {planets.join(", ")}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Tab>
                </Tabs>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};
