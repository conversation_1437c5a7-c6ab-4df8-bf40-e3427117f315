{"name": "matrimony-backend", "version": "2.0.0", "description": "Backend API for Indian Matrimony Platform with Multi-Database Support", "main": "src/unified-server.js", "scripts": {"start": "node start-server.js", "dev": "nodemon start-server.js", "dev:sqlite": "DB_TYPE=sqlite nodemon start-server.js", "dev:postgres": "DB_TYPE=postgres nodemon start-server.js", "dev:mongodb": "DB_TYPE=mongodb nodemon start-server.js", "init-db": "node src/scripts/init-database.js", "start:sqlite": "DB_TYPE=sqlite node src/unified-server.js", "start:postgres": "DB_TYPE=postgres node src/unified-server.js", "start:mongodb": "DB_TYPE=mongodb node src/unified-server.js", "test": "jest", "seed": "SEED_DATA=true node src/unified-server.js", "db:reset": "DB_FORCE_SYNC=true node src/unified-server.js", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["matrimony", "dating", "nodejs", "express", "postgresql", "sqlite", "mongodb", "sequelize", "mongoose", "api"], "author": "Matrimony Platform Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "nodemailer": "^6.9.7", "twilio": "^4.19.0", "razorpay": "^2.9.2", "sequelize": "^6.35.1", "sqlite3": "^5.1.6", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "mongoose": "^8.0.3", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "joi": "^17.11.0", "sharp": "^0.32.6", "csv-parser": "^3.0.0", "xlsx": "^0.18.5", "pdf-lib": "^1.17.1", "qrcode": "^1.5.3", "node-cron": "^3.0.3", "redis": "^4.6.10", "bull": "^4.12.2", "winston": "^3.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "sequelize-cli": "^6.6.2", "@types/node": "^20.10.4"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/matrimony-backend.git"}, "bugs": {"url": "https://github.com/your-org/matrimony-backend/issues"}, "homepage": "https://github.com/your-org/matrimony-backend#readme"}