const mongoose = require("mongoose");
const { SubscriptionPlan } = require("../models/Subscription");
require("dotenv").config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(
      process.env.MONGODB_URI || "mongodb://localhost:27017/matrimony-db"
    );
    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("MongoDB connection error:", error);
    process.exit(1);
  }
};

// Subscription plans data
const subscriptionPlans = [
  {
    name: "free",
    displayName: "Free",
    description: "Basic features to get started",
    price: 0,
    currency: "INR",
    duration: 1,
    features: [
      {
        name: "Basic Profile Creation",
        description: "Create and maintain your profile",
        enabled: true,
      },
      {
        name: "Limited Profile Views",
        description: "View up to 10 profiles per day",
        enabled: true,
      },
      {
        name: "Basic Search",
        description: "Search with basic filters",
        enabled: true,
      },
      {
        name: "Send Interests",
        description: "Send up to 5 interests per day",
        enabled: true,
      },
      {
        name: "Receive Interests",
        description: "Receive unlimited interests",
        enabled: true,
      },
    ],
    limits: {
      profileViews: 10,
      contactViews: 0,
      messagesPerDay: 5,
      interestsPerDay: 5,
      searchFilters: 3,
      photoUploads: 3,
    },
    premiumFeatures: {
      unlimitedViews: false,
      advancedSearch: false,
      priorityListing: false,
      directMessaging: false,
      contactDetails: false,
      horoscopeMatching: false,
      relationshipManager: false,
      profileHighlight: false,
      readReceipts: false,
      videoCall: false,
    },
    isActive: true,
    isPopular: false,
    sortOrder: 1,
  },
  {
    name: "silver",
    displayName: "Silver",
    description: "Enhanced features for serious seekers",
    price: 1999,
    currency: "INR",
    duration: 3,
    features: [
      {
        name: "All Free Features",
        description: "Everything in Free plan",
        enabled: true,
      },
      {
        name: "Unlimited Profile Views",
        description: "View unlimited profiles",
        enabled: true,
      },
      {
        name: "Advanced Search",
        description: "Search with advanced filters",
        enabled: true,
      },
      {
        name: "Direct Messaging",
        description: "Message without mutual interest",
        enabled: true,
      },
      {
        name: "Contact Details",
        description: "View contact information",
        enabled: true,
      },
      {
        name: "Profile Highlight",
        description: "Highlight your profile",
        enabled: true,
      },
    ],
    limits: {
      profileViews: -1,
      contactViews: 50,
      messagesPerDay: 25,
      interestsPerDay: 15,
      searchFilters: -1,
      photoUploads: 8,
    },
    premiumFeatures: {
      unlimitedViews: true,
      advancedSearch: true,
      priorityListing: true,
      directMessaging: true,
      contactDetails: true,
      horoscopeMatching: false,
      relationshipManager: false,
      profileHighlight: true,
      readReceipts: true,
      videoCall: false,
    },
    isActive: true,
    isPopular: true,
    discountPercentage: 10,
    sortOrder: 2,
  },
  {
    name: "gold",
    displayName: "Gold",
    description: "Premium features for better matches",
    price: 3999,
    currency: "INR",
    duration: 6,
    features: [
      {
        name: "All Silver Features",
        description: "Everything in Silver plan",
        enabled: true,
      },
      {
        name: "Horoscope Matching",
        description: "Advanced astrological compatibility",
        enabled: true,
      },
      {
        name: "Priority Listing",
        description: "Appear first in search results",
        enabled: true,
      },
      {
        name: "Read Receipts",
        description: "Know when messages are read",
        enabled: true,
      },
      {
        name: "Video Call",
        description: "Video calling feature",
        enabled: true,
      },
      {
        name: "Relationship Manager",
        description: "Dedicated support",
        enabled: true,
      },
    ],
    limits: {
      profileViews: -1,
      contactViews: -1,
      messagesPerDay: -1,
      interestsPerDay: -1,
      searchFilters: -1,
      photoUploads: 15,
    },
    premiumFeatures: {
      unlimitedViews: true,
      advancedSearch: true,
      priorityListing: true,
      directMessaging: true,
      contactDetails: true,
      horoscopeMatching: true,
      relationshipManager: true,
      profileHighlight: true,
      readReceipts: true,
      videoCall: true,
    },
    isActive: true,
    isPopular: false,
    discountPercentage: 15,
    sortOrder: 3,
  },
  {
    name: "platinum",
    displayName: "Platinum",
    description: "Ultimate matrimonial experience",
    price: 7999,
    currency: "INR",
    duration: 12,
    features: [
      {
        name: "All Gold Features",
        description: "Everything in Gold plan",
        enabled: true,
      },
      {
        name: "Unlimited Everything",
        description: "No limits on any feature",
        enabled: true,
      },
      {
        name: "Personal Matchmaker",
        description: "Dedicated matchmaking service",
        enabled: true,
      },
      {
        name: "Profile Verification",
        description: "Priority profile verification",
        enabled: true,
      },
      {
        name: "Wedding Planning",
        description: "Wedding planning assistance",
        enabled: true,
      },
      {
        name: "Exclusive Events",
        description: "Access to exclusive meetup events",
        enabled: true,
      },
    ],
    limits: {
      profileViews: -1,
      contactViews: -1,
      messagesPerDay: -1,
      interestsPerDay: -1,
      searchFilters: -1,
      photoUploads: -1,
    },
    premiumFeatures: {
      unlimitedViews: true,
      advancedSearch: true,
      priorityListing: true,
      directMessaging: true,
      contactDetails: true,
      horoscopeMatching: true,
      relationshipManager: true,
      profileHighlight: true,
      readReceipts: true,
      videoCall: true,
    },
    isActive: true,
    isPopular: false,
    discountPercentage: 20,
    sortOrder: 4,
  },
];

// Seed subscription plans
const seedSubscriptionPlans = async () => {
  try {
    console.log("Seeding subscription plans...");

    // Clear existing plans
    await SubscriptionPlan.deleteMany({});

    // Insert new plans
    const plans = await SubscriptionPlan.insertMany(subscriptionPlans);

    console.log(`Successfully seeded ${plans.length} subscription plans`);

    plans.forEach((plan) => {
      console.log(
        `- ${plan.displayName}: ₹${plan.price} for ${plan.duration} months`
      );
    });
  } catch (error) {
    console.error("Error seeding subscription plans:", error);
  }
};

// Sample user data for testing
const sampleUsers = [
  {
    name: "Priya Sharma",
    email: "<EMAIL>",
    password: "password123",
    phone: "+************",
    profileType: "self",
    personalInfo: {
      dateOfBirth: new Date("1995-05-15"),
      age: 28,
      height: "5'4\"",
      weight: "55",
      complexion: "Fair",
      maritalStatus: "never_married",
      motherTongue: "Hindi",
      country: "India",
      state: "Delhi",
      city: "New Delhi",
    },
    religiousInfo: {
      religion: "hindu",
      caste: "Brahmin",
      manglik: "no",
    },
    educationCareer: {
      highestEducation: "Masters",
      occupation: "Software Engineer",
      annualIncome: "8-12 Lakhs",
    },
    lifestyle: {
      diet: "vegetarian",
      smoking: "never",
      drinking: "never",
    },
    verificationStatus: {
      email: true,
      phone: true,
      photo: false,
      document: false,
    },
  },
  {
    name: "Rahul Gupta",
    email: "<EMAIL>",
    password: "password123",
    phone: "+9***********",
    profileType: "self",
    personalInfo: {
      dateOfBirth: new Date("1992-08-20"),
      age: 31,
      height: "5'10\"",
      weight: "75",
      complexion: "Wheatish",
      maritalStatus: "never_married",
      motherTongue: "Hindi",
      country: "India",
      state: "Maharashtra",
      city: "Mumbai",
    },
    religiousInfo: {
      religion: "hindu",
      caste: "Kshatriya",
      manglik: "no",
    },
    educationCareer: {
      highestEducation: "MBA",
      occupation: "Business Analyst",
      annualIncome: "12-15 Lakhs",
    },
    lifestyle: {
      diet: "vegetarian",
      smoking: "never",
      drinking: "occasionally",
    },
    verificationStatus: {
      email: true,
      phone: true,
      photo: false,
      document: false,
    },
  },
];

// Seed sample users (for development only)
const seedSampleUsers = async () => {
  try {
    console.log("Seeding sample users...");

    const User = require("../models/User");

    for (const userData of sampleUsers) {
      const existingUser = await User.findOne({ email: userData.email });
      if (!existingUser) {
        const user = new User(userData);
        user.calculateProfileCompletion();
        await user.save();
        console.log(`Created user: ${user.name} (${user.email})`);
      } else {
        console.log(`User already exists: ${userData.email}`);
      }
    }
  } catch (error) {
    console.error("Error seeding sample users:", error);
  }
};

// Main seed function
const seedDatabase = async () => {
  try {
    await connectDB();

    console.log("Starting database seeding...");

    await seedSubscriptionPlans();

    // Only seed sample users in development
    if (process.env.NODE_ENV === "development") {
      await seedSampleUsers();
    }

    console.log("Database seeding completed successfully!");
  } catch (error) {
    console.error("Database seeding failed:", error);
  } finally {
    await mongoose.connection.close();
    console.log("Database connection closed");
  }
};

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase();
}

module.exports = {
  seedDatabase,
  seedSubscriptionPlans,
  seedSampleUsers,
};
