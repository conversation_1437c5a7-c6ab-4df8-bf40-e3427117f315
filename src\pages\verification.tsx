import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Card<PERSON>ody,
  CardHeader,
  Button,
  Input,
  Progress,
  Divider,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  useDisclosure
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { useToast } from '../contexts/toast-context';

interface VerificationStatus {
  emailVerified: boolean;
  phoneVerified: boolean;
  profileVerified: boolean;
  documentVerified: boolean;
  photoVerified: boolean;
}

export const VerificationPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();
  const { showToast } = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [verificationStatus, setVerificationStatus] = React.useState<VerificationStatus>({
    emailVerified: false,
    phoneVerified: false,
    profileVerified: false,
    documentVerified: false,
    photoVerified: false
  });

  const [emailOTP, setEmailOTP] = React.useState('');
  const [phoneOTP, setPhoneOTP] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const [otpSent, setOtpSent] = React.useState({ email: false, phone: false });
  const [countdown, setCountdown] = React.useState({ email: 0, phone: 0 });
  const [activeVerification, setActiveVerification] = React.useState<'email' | 'phone' | null>(null);

  // Load verification status
  React.useEffect(() => {
    loadVerificationStatus();
  }, []);

  // Countdown timer
  React.useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => ({
        email: Math.max(0, prev.email - 1),
        phone: Math.max(0, prev.phone - 1)
      }));
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const loadVerificationStatus = async () => {
    try {
      // Mock API call - replace with actual API
      const mockStatus = {
        emailVerified: user?.emailVerified || false,
        phoneVerified: user?.phoneVerified || false,
        profileVerified: user?.isVerified || false,
        documentVerified: false,
        photoVerified: false
      };
      setVerificationStatus(mockStatus);
    } catch (error) {
      console.error('Failed to load verification status:', error);
    }
  };

  const sendEmailOTP = async () => {
    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setOtpSent(prev => ({ ...prev, email: true }));
      setCountdown(prev => ({ ...prev, email: 300 })); // 5 minutes
      showToast('OTP sent to your email address', 'success');
    } catch (error) {
      showToast('Failed to send email OTP', 'error');
    } finally {
      setLoading(false);
    }
  };

  const sendPhoneOTP = async () => {
    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setOtpSent(prev => ({ ...prev, phone: true }));
      setCountdown(prev => ({ ...prev, phone: 300 })); // 5 minutes
      showToast('OTP sent to your phone number', 'success');
    } catch (error) {
      showToast('Failed to send phone OTP', 'error');
    } finally {
      setLoading(false);
    }
  };

  const verifyEmailOTP = async () => {
    if (emailOTP.length !== 6) {
      showToast('Please enter a valid 6-digit OTP', 'error');
      return;
    }

    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setVerificationStatus(prev => ({ ...prev, emailVerified: true }));
      setEmailOTP('');
      setOtpSent(prev => ({ ...prev, email: false }));
      showToast('Email verified successfully!', 'success');
      
      // Update user context
      if (updateUser) {
        updateUser({ ...user, emailVerified: true });
      }
    } catch (error) {
      showToast('Invalid OTP. Please try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const verifyPhoneOTP = async () => {
    if (phoneOTP.length !== 6) {
      showToast('Please enter a valid 6-digit OTP', 'error');
      return;
    }

    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setVerificationStatus(prev => ({ ...prev, phoneVerified: true }));
      setPhoneOTP('');
      setOtpSent(prev => ({ ...prev, phone: false }));
      showToast('Phone verified successfully!', 'success');
      
      // Update user context
      if (updateUser) {
        updateUser({ ...user, phoneVerified: true });
      }
    } catch (error) {
      showToast('Invalid OTP. Please try again.', 'error');
    } finally {
      setLoading(false);
    }
  };

  const requestProfileVerification = async () => {
    try {
      setLoading(true);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      showToast('Profile verification request submitted. We will review within 24-48 hours.', 'success');
      onClose();
    } catch (error) {
      showToast('Failed to submit verification request', 'error');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getVerificationProgress = () => {
    const total = 5;
    const completed = Object.values(verificationStatus).filter(Boolean).length;
    return (completed / total) * 100;
  };

  const getVerificationLevel = () => {
    const progress = getVerificationProgress();
    if (progress >= 80) return { level: 'Highly Verified', color: 'success' as const };
    if (progress >= 60) return { level: 'Well Verified', color: 'primary' as const };
    if (progress >= 40) return { level: 'Partially Verified', color: 'warning' as const };
    return { level: 'Basic Verification', color: 'danger' as const };
  };

  const verificationLevel = getVerificationLevel();

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center gap-3 mb-4">
          <Button
            isIconOnly
            variant="light"
            onPress={() => navigate(-1)}
          >
            <Icon icon="lucide:arrow-left" size={20} />
          </Button>
          <h1 className="text-3xl font-bold">Account Verification</h1>
        </div>
        <p className="text-default-600">
          Verify your account to build trust and get better matches. Verified profiles get 5x more responses.
        </p>
      </div>

      {/* Verification Progress */}
      <Card className="mb-6">
        <CardBody>
          <div className="flex items-center justify-between mb-4">
            <div>
              <h3 className="text-lg font-semibold">Verification Progress</h3>
              <p className="text-sm text-default-600">Complete all verifications to become a trusted member</p>
            </div>
            <Chip color={verificationLevel.color} variant="flat">
              {verificationLevel.level}
            </Chip>
          </div>
          
          <Progress 
            value={getVerificationProgress()} 
            color={verificationLevel.color}
            className="mb-2"
          />
          <p className="text-sm text-default-600">
            {Object.values(verificationStatus).filter(Boolean).length} of 5 verifications completed
          </p>
        </CardBody>
      </Card>

      {/* Verification Items */}
      <div className="space-y-4">
        {/* Email Verification */}
        <Card>
          <CardBody>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  verificationStatus.emailVerified ? 'bg-success-100' : 'bg-default-100'
                }`}>
                  <Icon 
                    icon={verificationStatus.emailVerified ? "lucide:check" : "lucide:mail"} 
                    className={verificationStatus.emailVerified ? 'text-success-600' : 'text-default-600'}
                    size={20}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Email Verification</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Verify your email address to receive important notifications
                  </p>
                  
                  {!verificationStatus.emailVerified && (
                    <div className="space-y-3">
                      {otpSent.email ? (
                        <div className="flex gap-2">
                          <Input
                            placeholder="Enter 6-digit OTP"
                            value={emailOTP}
                            onChange={(e) => setEmailOTP(e.target.value)}
                            maxLength={6}
                            className="max-w-xs"
                          />
                          <Button 
                            color="primary"
                            onPress={verifyEmailOTP}
                            isLoading={loading}
                            isDisabled={emailOTP.length !== 6}
                          >
                            Verify
                          </Button>
                        </div>
                      ) : (
                        <Button 
                          color="primary" 
                          variant="flat"
                          onPress={sendEmailOTP}
                          isLoading={loading}
                          isDisabled={countdown.email > 0}
                        >
                          {countdown.email > 0 ? `Resend in ${formatTime(countdown.email)}` : 'Send OTP'}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              {verificationStatus.emailVerified && (
                <Chip color="success" variant="flat" startContent={<Icon icon="lucide:check" />}>
                  Verified
                </Chip>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Phone Verification */}
        <Card>
          <CardBody>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  verificationStatus.phoneVerified ? 'bg-success-100' : 'bg-default-100'
                }`}>
                  <Icon 
                    icon={verificationStatus.phoneVerified ? "lucide:check" : "lucide:phone"} 
                    className={verificationStatus.phoneVerified ? 'text-success-600' : 'text-default-600'}
                    size={20}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Phone Verification</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Verify your phone number for secure account access
                  </p>
                  
                  {!verificationStatus.phoneVerified && (
                    <div className="space-y-3">
                      {otpSent.phone ? (
                        <div className="flex gap-2">
                          <Input
                            placeholder="Enter 6-digit OTP"
                            value={phoneOTP}
                            onChange={(e) => setPhoneOTP(e.target.value)}
                            maxLength={6}
                            className="max-w-xs"
                          />
                          <Button 
                            color="primary"
                            onPress={verifyPhoneOTP}
                            isLoading={loading}
                            isDisabled={phoneOTP.length !== 6}
                          >
                            Verify
                          </Button>
                        </div>
                      ) : (
                        <Button 
                          color="primary" 
                          variant="flat"
                          onPress={sendPhoneOTP}
                          isLoading={loading}
                          isDisabled={countdown.phone > 0}
                        >
                          {countdown.phone > 0 ? `Resend in ${formatTime(countdown.phone)}` : 'Send OTP'}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              {verificationStatus.phoneVerified && (
                <Chip color="success" variant="flat" startContent={<Icon icon="lucide:check" />}>
                  Verified
                </Chip>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Profile Verification */}
        <Card>
          <CardBody>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  verificationStatus.profileVerified ? 'bg-success-100' : 'bg-default-100'
                }`}>
                  <Icon 
                    icon={verificationStatus.profileVerified ? "lucide:check" : "lucide:user-check"} 
                    className={verificationStatus.profileVerified ? 'text-success-600' : 'text-default-600'}
                    size={20}
                  />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Profile Verification</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Get your profile manually verified by our team for maximum trust
                  </p>
                  
                  {!verificationStatus.profileVerified && (
                    <Button 
                      color="primary" 
                      variant="flat"
                      onPress={onOpen}
                    >
                      Request Verification
                    </Button>
                  )}
                </div>
              </div>
              
              {verificationStatus.profileVerified && (
                <Chip color="success" variant="flat" startContent={<Icon icon="lucide:check" />}>
                  Verified
                </Chip>
              )}
            </div>
          </CardBody>
        </Card>

        {/* Document Verification */}
        <Card>
          <CardBody>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-default-100">
                  <Icon icon="lucide:file-text" className="text-default-600" size={20} />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Document Verification</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Upload government ID for identity verification
                  </p>
                  
                  <Chip color="warning" variant="flat">
                    Coming Soon
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Photo Verification */}
        <Card>
          <CardBody>
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 rounded-full flex items-center justify-center bg-default-100">
                  <Icon icon="lucide:camera" className="text-default-600" size={20} />
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-1">Photo Verification</h3>
                  <p className="text-sm text-default-600 mb-3">
                    Verify your photos to ensure authenticity
                  </p>
                  
                  <Chip color="warning" variant="flat">
                    Coming Soon
                  </Chip>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* Profile Verification Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalContent>
          <ModalHeader>Request Profile Verification</ModalHeader>
          <ModalBody>
            <Alert color="primary" variant="flat">
              <Icon icon="lucide:info" />
              Profile verification requires manual review by our team
            </Alert>
            
            <div className="space-y-4 mt-4">
              <p className="text-sm">
                To request profile verification, please ensure:
              </p>
              <ul className="text-sm space-y-2 ml-4">
                <li>• Your profile is at least 80% complete</li>
                <li>• All information is accurate and up-to-date</li>
                <li>• You have uploaded clear profile photos</li>
                <li>• Your email and phone are verified</li>
              </ul>
              
              <p className="text-sm text-default-600">
                Our team will review your profile within 24-48 hours and update your verification status.
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Cancel
            </Button>
            <Button 
              color="primary" 
              onPress={requestProfileVerification}
              isLoading={loading}
            >
              Submit Request
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
