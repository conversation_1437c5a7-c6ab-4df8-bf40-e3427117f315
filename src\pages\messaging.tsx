import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Avatar,
  Badge,
  Chip,
  Divider,
  Spinner,
  Alert,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { messagingAPI, userAPI } from '../services/api';
import { Message, Conversation, User } from '../types/matrimony';

export const MessagingPage: React.FC = () => {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [conversations, setConversations] = React.useState<Conversation[]>([]);
  const [messages, setMessages] = React.useState<Message[]>([]);
  const [selectedConversation, setSelectedConversation] = React.useState<Conversation | null>(null);
  const [otherUser, setOtherUser] = React.useState<User | null>(null);
  const [newMessage, setNewMessage] = React.useState('');
  const [loading, setLoading] = React.useState(true);
  const [sendingMessage, setSendingMessage] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Load conversations
  React.useEffect(() => {
    const loadConversations = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const response = await messagingAPI.getConversations();
        
        if (response.success) {
          setConversations(response.data.conversations);
          
          // If userId is provided, find and select that conversation
          if (userId) {
            const conversation = response.data.conversations.find((c: Conversation) =>
              c.participant1Id === userId || c.participant2Id === userId
            );
            
            if (conversation) {
              setSelectedConversation(conversation);
              await loadMessages(conversation.id);
              await loadOtherUser(userId);
            } else {
              // Create new conversation if it doesn't exist
              await createNewConversation(userId);
            }
          }
        } else {
          setError(response.message || 'Failed to load conversations');
        }
      } catch (err: any) {
        console.error('Load conversations error:', err);
        setError(err.response?.data?.message || 'Failed to load conversations');
      } finally {
        setLoading(false);
      }
    };
    
    loadConversations();
  }, [user, userId]);

  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load messages for a conversation
  const loadMessages = async (conversationId: string) => {
    try {
      const response = await messagingAPI.getMessages(conversationId);
      
      if (response.success) {
        setMessages(response.data.messages);
        
        // Mark messages as read
        await messagingAPI.markMessagesAsRead(conversationId);
      } else {
        setError(response.message || 'Failed to load messages');
      }
    } catch (err: any) {
      console.error('Load messages error:', err);
      setError(err.response?.data?.message || 'Failed to load messages');
    }
  };

  // Load other user details
  const loadOtherUser = async (otherUserId: string) => {
    try {
      const response = await userAPI.getProfile(otherUserId);
      
      if (response.success) {
        setOtherUser(response.data.user);
      }
    } catch (err: any) {
      console.error('Load user error:', err);
    }
  };

  // Create new conversation
  const createNewConversation = async (otherUserId: string) => {
    try {
      const response = await messagingAPI.createConversation(otherUserId);
      
      if (response.success) {
        const newConversation = response.data.conversation;
        setConversations(prev => [newConversation, ...prev]);
        setSelectedConversation(newConversation);
        setMessages([]);
        await loadOtherUser(otherUserId);
      } else {
        setError(response.message || 'Failed to create conversation');
      }
    } catch (err: any) {
      console.error('Create conversation error:', err);
      setError(err.response?.data?.message || 'Failed to create conversation');
    }
  };

  // Send message
  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !user) return;

    try {
      setSendingMessage(true);
      
      const response = await messagingAPI.sendMessage(selectedConversation.id, {
        content: newMessage.trim(),
        messageType: 'text'
      });
      
      if (response.success) {
        const sentMessage = response.data.message;
        setMessages(prev => [...prev, sentMessage]);
        setNewMessage('');
        
        // Update conversation's last message
        setConversations(prev => prev.map(conv =>
          conv.id === selectedConversation.id
            ? { ...conv, lastMessageId: sentMessage.id, lastMessageAt: sentMessage.sentAt }
            : conv
        ));
      } else {
        setError(response.message || 'Failed to send message');
      }
    } catch (err: any) {
      console.error('Send message error:', err);
      setError(err.response?.data?.message || 'Failed to send message');
    } finally {
      setSendingMessage(false);
    }
  };

  // Handle conversation selection
  const selectConversation = async (conversation: Conversation) => {
    setSelectedConversation(conversation);
    await loadMessages(conversation.id);
    
    // Load other user details
    const otherUserId = conversation.participant1Id === user?.id 
      ? conversation.participant2Id 
      : conversation.participant1Id;
    await loadOtherUser(otherUserId);
  };

  // Format message time
  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return diffInMinutes < 1 ? 'Just now' : `${diffInMinutes}m ago`;
    }
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    }
    
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short'
    });
  };

  // Get conversation display name
  const getConversationDisplayName = (conversation: Conversation) => {
    // This would be populated from the API response
    return conversation.participant1Id === user?.id 
      ? 'Other User' // Replace with actual name from API
      : 'Other User';
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to access messages.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* Conversations List */}
        <Card className="lg:col-span-1">
          <CardHeader className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Messages</h2>
            <Button
              size="sm"
              variant="flat"
              startContent={<Icon icon="lucide:plus" />}
              onPress={() => navigate('/search')}
            >
              New Chat
            </Button>
          </CardHeader>
          <CardBody className="p-0">
            {loading && (
              <div className="flex justify-center items-center py-8">
                <Spinner color="primary" />
              </div>
            )}
            
            {!loading && conversations.length === 0 && (
              <div className="text-center py-8 px-4">
                <Icon icon="lucide:message-circle" size={48} className="mx-auto mb-4 text-default-400" />
                <h3 className="font-semibold mb-2">No Conversations</h3>
                <p className="text-sm text-default-500 mb-4">
                  Start a conversation by sending an interest or browsing profiles.
                </p>
                <Button
                  color="primary"
                  size="sm"
                  onPress={() => navigate('/search')}
                >
                  Browse Profiles
                </Button>
              </div>
            )}
            
            <div className="divide-y divide-default-200">
              {conversations.map((conversation) => (
                <div
                  key={conversation.id}
                  className={`p-4 cursor-pointer hover:bg-default-50 transition-colors ${
                    selectedConversation?.id === conversation.id ? 'bg-primary-50' : ''
                  }`}
                  onClick={() => selectConversation(conversation)}
                >
                  <div className="flex gap-3">
                    <Avatar
                      size="md"
                      fallback={<Icon icon="lucide:user" size={20} />}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-start">
                        <h3 className="font-medium truncate">
                          {getConversationDisplayName(conversation)}
                        </h3>
                        <span className="text-xs text-default-500">
                          {conversation.lastMessageAt && formatMessageTime(conversation.lastMessageAt)}
                        </span>
                      </div>
                      <p className="text-sm text-default-600 truncate">
                        Last message preview...
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardBody>
        </Card>

        {/* Chat Area */}
        <Card className="lg:col-span-2">
          {selectedConversation && otherUser ? (
            <>
              {/* Chat Header */}
              <CardHeader className="flex justify-between items-center border-b border-default-200">
                <div className="flex items-center gap-3">
                  <Avatar
                    src={otherUser.personalDetails?.profilePicture}
                    size="md"
                    fallback={<Icon icon="lucide:user" size={20} />}
                  />
                  <div>
                    <h3 className="font-semibold">
                      {otherUser.personalDetails?.firstName} {otherUser.personalDetails?.lastName}
                    </h3>
                    <p className="text-sm text-default-500">
                      {otherUser.personalDetails?.age} years • {otherUser.personalDetails?.city}
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="flat"
                    startContent={<Icon icon="lucide:user" />}
                    onPress={() => navigate(`/profile/${otherUser.id}`)}
                  >
                    View Profile
                  </Button>
                  
                  <Dropdown>
                    <DropdownTrigger>
                      <Button size="sm" variant="flat" isIconOnly>
                        <Icon icon="lucide:more-vertical" />
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu>
                      <DropdownItem key="block" className="text-danger">
                        Block User
                      </DropdownItem>
                      <DropdownItem key="report" className="text-danger">
                        Report User
                      </DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                </div>
              </CardHeader>

              {/* Messages */}
              <CardBody className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length === 0 && (
                  <div className="text-center py-8">
                    <Icon icon="lucide:message-circle" size={48} className="mx-auto mb-4 text-default-400" />
                    <h3 className="font-semibold mb-2">Start the Conversation</h3>
                    <p className="text-sm text-default-500">
                      Send your first message to {otherUser.personalDetails?.firstName}
                    </p>
                  </div>
                )}
                
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.senderId === user.id ? 'justify-end' : 'justify-start'}`}
                  >
                    <div
                      className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                        message.senderId === user.id
                          ? 'bg-primary text-white'
                          : 'bg-default-100 text-default-900'
                      }`}
                    >
                      <p className="text-sm">{message.content}</p>
                      <div className={`flex items-center gap-1 mt-1 ${
                        message.senderId === user.id ? 'justify-end' : 'justify-start'
                      }`}>
                        <span className={`text-xs ${
                          message.senderId === user.id ? 'text-white/70' : 'text-default-500'
                        }`}>
                          {formatMessageTime(message.sentAt)}
                        </span>
                        {message.senderId === user.id && (
                          <Icon
                            icon={message.isRead ? 'lucide:check-check' : 'lucide:check'}
                            size={12}
                            className={message.isRead ? 'text-white' : 'text-white/70'}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </CardBody>

              {/* Message Input */}
              <div className="p-4 border-t border-default-200">
                <div className="flex gap-2">
                  <Input
                    placeholder="Type your message..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendMessage();
                      }
                    }}
                    className="flex-1"
                  />
                  <Button
                    color="primary"
                    isIconOnly
                    onPress={sendMessage}
                    isLoading={sendingMessage}
                    isDisabled={!newMessage.trim()}
                  >
                    <Icon icon="lucide:send" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <CardBody className="flex items-center justify-center">
              <div className="text-center">
                <Icon icon="lucide:message-circle" size={64} className="mx-auto mb-4 text-default-400" />
                <h3 className="text-xl font-semibold mb-2">Select a Conversation</h3>
                <p className="text-default-500">
                  Choose a conversation from the list to start messaging
                </p>
              </div>
            </CardBody>
          )}
        </Card>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          color="danger"
          variant="flat"
          className="mt-4"
          startContent={<Icon icon="lucide:alert-circle" />}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}
    </div>
  );
};
