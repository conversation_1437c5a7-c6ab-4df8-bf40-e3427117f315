#!/usr/bin/env node

/**
 * Comprehensive Matrimony Platform Server Startup Script
 * This script handles proper initialization and error handling
 */

const path = require('path');
const fs = require('fs');

// Set environment variables if not set
process.env.NODE_ENV = process.env.NODE_ENV || 'development';
process.env.PORT = process.env.PORT || '3001';

// Create necessary directories
const createDirectories = () => {
  const dirs = [
    path.join(__dirname, 'uploads'),
    path.join(__dirname, 'uploads/photos'),
    path.join(__dirname, 'uploads/documents'),
    path.join(__dirname, 'uploads/horoscopes'),
    path.join(__dirname, 'logs')
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`📁 Created directory: ${dir}`);
    }
  });
};

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('💥 Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Graceful shutdown
const gracefulShutdown = (signal) => {
  console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);
  
  // Close server
  if (global.server) {
    global.server.close(() => {
      console.log('✅ HTTP server closed');
      
      // Close database connections
      if (global.sequelize) {
        global.sequelize.close().then(() => {
          console.log('✅ Database connections closed');
          process.exit(0);
        });
      } else {
        process.exit(0);
      }
    });
  } else {
    process.exit(0);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Main startup function
const startServer = async () => {
  try {
    console.log('🚀 Starting Comprehensive Matrimony Platform Server...');
    console.log(`📍 Environment: ${process.env.NODE_ENV}`);
    console.log(`🔌 Port: ${process.env.PORT}`);
    
    // Create necessary directories
    createDirectories();
    
    // Load environment variables from .env file if it exists
    const envPath = path.join(__dirname, '.env');
    if (fs.existsSync(envPath)) {
      require('dotenv').config({ path: envPath });
      console.log('✅ Environment variables loaded from .env');
    } else {
      console.log('⚠️  No .env file found, using default environment variables');
    }
    
    // Start the unified server
    const app = require('./src/unified-server');
    
    const PORT = process.env.PORT || 3001;
    const server = app.listen(PORT, () => {
      console.log('\n🎉 Server started successfully!');
      console.log(`🌐 Server running on http://localhost:${PORT}`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api/docs`);
      console.log(`💻 Admin Panel: http://localhost:${PORT}/admin`);
      console.log('\n📋 Available API Endpoints:');
      console.log('   🔐 Authentication: /api/auth/*');
      console.log('   👤 User Management: /api/users/*');
      console.log('   🔍 Search & Discovery: /api/search/*');
      console.log('   💝 Interest Management: /api/interests/*');
      console.log('   💬 Messaging: /api/messages/*');
      console.log('   💳 Subscriptions: /api/subscription/*');
      console.log('   📊 Analytics: /api/analytics/*');
      console.log('\n🔧 Development Features:');
      console.log('   📝 Sample data automatically created');
      console.log('   🔄 Hot reload enabled');
      console.log('   🐛 Debug mode active');
      console.log('\n✨ Ready to serve requests!');
    });
    
    // Store server reference for graceful shutdown
    global.server = server;
    
    // Handle server errors
    server.on('error', (error) => {
      if (error.syscall !== 'listen') {
        throw error;
      }
      
      const bind = typeof PORT === 'string' ? 'Pipe ' + PORT : 'Port ' + PORT;
      
      switch (error.code) {
        case 'EACCES':
          console.error(`❌ ${bind} requires elevated privileges`);
          process.exit(1);
          break;
        case 'EADDRINUSE':
          console.error(`❌ ${bind} is already in use`);
          console.log('💡 Try using a different port or stop the existing server');
          process.exit(1);
          break;
        default:
          throw error;
      }
    });
    
  } catch (error) {
    console.error('💥 Failed to start server:', error);
    console.error('Stack:', error.stack);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = startServer;
