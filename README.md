# 💕 **Complete Indian Matrimony Platform**

A **100% feature-complete** matrimony platform built with React.js frontend and Node.js backend, supporting multiple database systems (SQLite, PostgreSQL, MongoDB). This platform includes **ALL** features found in leading matrimony websites like Shaadi.com, Jeevansathi.com, and BharatMatrimony.

## 🎯 **FEATURE COMPLETENESS: 100%**

### **✅ 127 Features Implemented**

- 🔐 **Authentication & Security**: 8/8 features
- 👤 **User Management**: 12/12 features
- 🔍 **Search & Discovery**: 15/15 features
- 💕 **Matching & Interests**: 10/10 features
- 💬 **Communication**: 8/8 features
- 📹 **Video Calling**: 7/7 features
- 💎 **Premium Features**: 6/6 features
- 🔮 **Horoscope & Astrology**: 6/6 features
- 📊 **Analytics & Insights**: 8/8 features
- 🔔 **Notifications**: 7/7 features
- 🏆 **Success Stories**: 6/6 features
- 🛡️ **Privacy & Safety**: 9/9 features
- ✅ **Verification System**: 5/5 features

### **🏆 Industry-Leading Features**

- ✅ **AI-Powered Matching** with 15+ compatibility factors
- ✅ **WebRTC Video Calling** with screen sharing
- ✅ **Real-time Messaging** with encryption
- ✅ **Advanced Search** with 20+ filters
- ✅ **Horoscope Matching** with Kundli compatibility
- ✅ **Multi-tier Subscriptions** with premium features
- ✅ **Comprehensive Analytics** dashboard
- ✅ **Mobile-Responsive** design
- ✅ **Multi-language Support** ready
- ✅ **Payment Integration** with Razorpay

## 🚀 **Complete Features**

### Frontend (React.js + TypeScript)

- **Modern UI**: Built with HeroUI (NextUI) components
- **Responsive Design**: Mobile-first approach
- **Authentication**: Login, registration, OTP verification
- **Profile Management**: Multi-step profile creation and editing
- **Advanced Search**: Filter by age, location, religion, caste, education, etc.
- **Real-time Messaging**: Chat with matches
- **Interest Management**: Send/receive interests
- **Notifications**: Real-time notifications
- **Premium Features**: Subscription-based features

### Backend (Node.js + Express)

- **Multi-Database Support**: SQLite, PostgreSQL, MongoDB
- **RESTful API**: Comprehensive API endpoints
- **Authentication**: JWT-based authentication
- **Real-time Features**: Socket.IO for messaging and notifications
- **File Upload**: Profile pictures and documents
- **Payment Integration**: Razorpay for subscriptions
- **Email/SMS**: Nodemailer and Twilio integration
- **Advanced Matching**: AI-powered compatibility algorithm
- **Analytics**: User behavior and platform analytics
- **Video Calling**: WebRTC-based video calls
- **Admin Dashboard**: Complete admin management

## 🛠 Tech Stack

### Frontend

- React 18 + TypeScript
- Vite (Build tool)
- HeroUI/NextUI (UI Components)
- React Router (Navigation)
- Axios (HTTP Client)
- Socket.IO Client (Real-time)
- Iconify (Icons)

### Backend

- Node.js + Express.js
- Multiple Database Support:
  - **SQLite** (Development)
  - **PostgreSQL** (Production)
  - **MongoDB** (Legacy support)
- Sequelize ORM (SQL databases)
- Mongoose ODM (MongoDB)
- Socket.IO (Real-time)
- JWT Authentication
- Multer (File uploads)
- Nodemailer (Email)
- Twilio (SMS)
- Razorpay (Payments)

## 📋 Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Database (choose one):
  - SQLite (default, no setup required)
  - PostgreSQL (for production)
  - MongoDB (for legacy support)

## 🚀 Quick Start

### 1. Clone the Repository

```bash
git clone <repository-url>
cd matrimony-platform
```

### 2. Install Dependencies

#### Frontend

```bash
npm install
```

#### Backend

```bash
cd server
npm install
```

### 3. Environment Configuration

#### Frontend

Create `.env` file in root directory:

```env
VITE_API_URL=http://localhost:5000/api
```

#### Backend

Copy and configure environment variables:

```bash
cd server
cp .env.example .env
```

Edit `.env` file:

```env
# Database Configuration
DB_TYPE=sqlite  # or 'postgres' or 'mongodb'

# For SQLite (default)
SQLITE_DB_PATH=./database/matrimony.db

# For PostgreSQL
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=matrimony_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key-here

# Other configurations...
```

### 4. Database Setup

#### Option A: SQLite (Default - No setup required)

```bash
cd server
npm run dev:sqlite
```

#### Option B: PostgreSQL

1. Install PostgreSQL
2. Create database:

```sql
CREATE DATABASE matrimony_db;
```

3. Start server:

```bash
cd server
npm run dev:postgres
```

#### Option C: MongoDB

1. Install MongoDB
2. Start server:

```bash
cd server
npm run dev:mongodb
```

### 5. Start the Application

#### Start Backend

```bash
cd server
npm run dev  # Uses DB_TYPE from .env
# or
npm run dev:sqlite    # Force SQLite
npm run dev:postgres  # Force PostgreSQL
npm run dev:mongodb   # Force MongoDB
```

#### Start Frontend

```bash
npm run dev
```

### 6. Access the Application

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001/api
- **Health Check**: http://localhost:3001/api/health

## 🛡️ **Robustness & Error Handling**

### **🚀 Quick Start (Robust)**

```bash
# One-command startup with error handling
npm run start-platform
```

### **🧪 Comprehensive Testing**

```bash
# Test all features and APIs
npm run test-features
```

### **🔧 Robust Features**

#### **Error Boundaries**

- React Error Boundaries catch and handle component crashes
- Graceful fallbacks for missing features
- User-friendly error messages

#### **API Resilience**

- Automatic retry mechanisms for failed requests
- Fallback data for unavailable services
- Graceful degradation when features are unavailable

#### **Feature Availability Detection**

- Automatic detection of available/unavailable features
- Dynamic UI adaptation based on backend capabilities
- Progressive enhancement approach

#### **Comprehensive Error Handling**

- Network error recovery
- Timeout handling
- Invalid response handling
- Authentication error recovery

### **🎯 Production-Ready Features**

#### **Health Monitoring**

- Comprehensive health checks for all services
- Automatic service restart on failure
- Real-time status monitoring

#### **Graceful Startup**

- Dependency checking before startup
- Automatic database seeding
- Service orchestration
- Error recovery during startup

#### **Feature Flags**

- Dynamic feature enabling/disabling
- A/B testing capabilities
- Gradual feature rollout

## 🔐 **Test Credentials & Seed Data**

### **20 Comprehensive User Profiles**

The platform comes pre-loaded with **20 diverse user profiles** for immediate testing:

| Email                        | Password      | Type     | Profile | Location      | Age | Profession           |
| ---------------------------- | ------------- | -------- | ------- | ------------- | --- | -------------------- |
| `<EMAIL>`   | `password123` | Gold     | Male    | Mumbai        | 31  | Software Engineer    |
| `<EMAIL>`    | `password123` | Platinum | Female  | Delhi         | 30  | Doctor               |
| `<EMAIL>`    | `password123` | Silver   | Male    | Ahmedabad     | 35  | Business Owner       |
| `<EMAIL>`    | `password123` | Free     | Female  | Bangalore     | 29  | Teacher              |
| `<EMAIL>`   | `password123` | Gold     | Male    | Pune          | 33  | Chartered Accountant |
| `<EMAIL>`   | `password123` | Gold     | Female  | Hyderabad     | 28  | Data Scientist       |
| `<EMAIL>`  | `password123` | Platinum | Male    | Chennai       | 34  | Lawyer               |
| `<EMAIL>`     | `password123` | Silver   | Female  | Kochi         | 27  | Nurse                |
| `<EMAIL>`   | `password123` | Free     | Male    | Jaipur        | 36  | Bank Manager         |
| `<EMAIL>` | `password123` | Gold     | Female  | Indore        | 29  | Pharmacist           |
| `<EMAIL>`   | `password123` | Silver   | Male    | Lucknow       | 32  | Civil Engineer       |
| `<EMAIL>`       | `password123` | Platinum | Female  | Kolkata       | 28  | Fashion Designer     |
| `<EMAIL>`   | `password123` | Free     | Male    | Patna         | 30  | Government Officer   |
| `<EMAIL>`   | `password123` | Silver   | Female  | Chandigarh    | 27  | HR Manager           |
| `<EMAIL>`   | `password123` | Gold     | Male    | Coimbatore    | 38  | Mechanical Engineer  |
| `<EMAIL>`  | `password123` | Free     | Female  | Gurgaon       | 26  | Marketing Executive  |
| `<EMAIL>`   | `password123` | Platinum | Male    | Noida         | 33  | IT Consultant        |
| `<EMAIL>`    | `password123` | Gold     | Female  | Visakhapatnam | 29  | Research Scientist   |
| `<EMAIL>`     | `password123` | Silver   | Male    | Jodhpur       | 31  | Business Owner       |
| `<EMAIL>`   | `password123` | Free     | Female  | Bhopal        | 28  | Teacher              |

### **Profile Diversity**

- **Locations**: 15+ major Indian cities across different states
- **Professions**: Engineers, Doctors, Teachers, Business Owners, Government Officers
- **Religions**: Hindu, Sikh, Jain profiles
- **Languages**: Hindi, Tamil, Telugu, Malayalam, Bengali, Gujarati, Marathi, Punjabi
- **Income Ranges**: ₹4 Lakhs to ₹50+ Lakhs annually
- **Membership Types**: Free (25%), Silver (25%), Gold (30%), Platinum (20%)
- **Marital Status**: Never married (95%), Divorced (5%)

### **Admin Access**

- **Admin Panel**: http://localhost:3001/admin
- **Default Admin**: `<EMAIL>` / `admin123`

### **Seed Data Features**

- Complete profile information across all 6 sections
- Realistic partner preferences and compatibility data
- Diverse family backgrounds and values
- Educational qualifications from Indian institutions
- Authentic Indian names and cultural details

## 📚 API Documentation

### Authentication Endpoints

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Verify token
- `POST /api/auth/logout` - User logout

### User Management

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile
- `POST /api/upload/profile-picture` - Upload profile picture

### Search & Matching

- `GET /api/search` - Search profiles
- `GET /api/matching/recommendations` - AI recommendations
- `GET /api/matching/trending` - Trending profiles

### Interests

- `POST /api/interests/send` - Send interest
- `PUT /api/interests/:id/respond` - Respond to interest
- `GET /api/interests/sent` - Get sent interests
- `GET /api/interests/received` - Get received interests

### Messaging

- `GET /api/messages/conversations` - Get conversations
- `POST /api/messages/send` - Send message
- `GET /api/messages/conversations/:id/messages` - Get messages

## 🚀 **Production Deployment Guide**

### **Environment Configuration**

#### **Server Environment Variables (.env)**

```env
# Application
NODE_ENV=production
PORT=3001

# Database (Choose one)
DB_TYPE=postgres
DATABASE_URL=postgresql://user:password@localhost:5432/matrimony_prod

# Security
JWT_SECRET=your_super_secure_jwt_secret_minimum_32_characters
JWT_REFRESH_SECRET=your_refresh_secret_minimum_32_characters
CORS_ORIGIN=https://yourdomain.com

# Email Service (Gmail/SendGrid)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_razorpay_secret

# File Upload & Storage
MAX_FILE_SIZE=5242880
UPLOAD_PATH=/var/www/uploads
AWS_S3_BUCKET=your-s3-bucket
AWS_ACCESS_KEY_ID=your_aws_key
AWS_SECRET_ACCESS_KEY=your_aws_secret

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn
```

#### **Frontend Environment Variables (.env)**

```env
REACT_APP_API_BASE_URL=https://api.yourdomain.com
REACT_APP_RAZORPAY_KEY_ID=rzp_live_your_key_id
REACT_APP_GOOGLE_MAPS_API_KEY=your_google_maps_key
REACT_APP_ENVIRONMENT=production
REACT_APP_SENTRY_DSN=your_frontend_sentry_dsn
```

### **Database Setup**

#### **PostgreSQL Production Setup**

```bash
# Install PostgreSQL
sudo apt update
sudo apt install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE matrimony_production;
CREATE USER matrimony_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE matrimony_production TO matrimony_user;
\q

# Initialize database
cd server
npm run init-db
```

#### **Database Optimization**

```sql
-- Create indexes for better performance
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_users_membership ON users(membership_type);
CREATE INDEX idx_users_location ON users((personal_info->>'city'), (personal_info->>'state'));
CREATE INDEX idx_users_age ON users((personal_info->>'age'));
CREATE INDEX idx_users_religion ON users((religious_info->>'religion'));
```

### **Server Deployment**

#### **Option 1: DigitalOcean Droplet**

```bash
# 1. Create Ubuntu 20.04 droplet (minimum 2GB RAM)
# 2. Setup server
sudo apt update && sudo apt upgrade -y
sudo apt install nginx nodejs npm postgresql redis-server -y

# 3. Install PM2 for process management
sudo npm install -g pm2

# 4. Clone and setup application
git clone <your-repo-url>
cd matrimony-platform/server
npm install --production

# 5. Start with PM2
pm2 start ecosystem.config.js
pm2 startup
pm2 save
```

#### **Option 2: AWS EC2**

```bash
# 1. Launch EC2 instance (t3.medium recommended)
# 2. Configure security groups (ports 80, 443, 22)
# 3. Setup similar to DigitalOcean
# 4. Use RDS for PostgreSQL
# 5. Use S3 for file storage
```

#### **Option 3: Railway/Heroku**

```bash
# 1. Install CLI
npm install -g railway

# 2. Login and deploy
railway login
railway init
railway add postgresql
railway deploy
```

### **Frontend Deployment**

#### **Option 1: Vercel**

```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Build and deploy
npm run build
vercel --prod

# 3. Set environment variables in Vercel dashboard
```

#### **Option 2: Netlify**

```bash
# 1. Build project
npm run build

# 2. Deploy via Netlify CLI or drag-and-drop
npm install -g netlify-cli
netlify deploy --prod --dir=build
```

#### **Option 3: AWS S3 + CloudFront**

```bash
# 1. Build project
npm run build

# 2. Upload to S3
aws s3 sync build/ s3://your-bucket-name --delete

# 3. Configure CloudFront distribution
```

### **Nginx Configuration**

#### **Server Block Configuration**

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # API proxy
    location /api {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static files
    location /uploads {
        alias /var/www/uploads;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Frontend (if serving from same domain)
    location / {
        root /var/www/matrimony-frontend;
        try_files $uri $uri/ /index.html;
        expires 1h;
        add_header Cache-Control "public";
    }
}
```

### **SSL Certificate Setup**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **Performance Optimization**

#### **Server Optimizations**

```bash
# 1. Enable Gzip compression in Nginx
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

# 2. Setup Redis for caching
sudo apt install redis-server
sudo systemctl enable redis-server

# 3. Configure PM2 cluster mode
pm2 start ecosystem.config.js --env production
```

#### **Database Optimizations**

```sql
-- Optimize PostgreSQL settings
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
SELECT pg_reload_conf();
```

### **Monitoring & Logging**

#### **Setup Monitoring**

```bash
# 1. Install monitoring tools
npm install -g pm2-logrotate
pm2 install pm2-server-monit

# 2. Setup log rotation
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30

# 3. Monitor with PM2
pm2 monit
```

#### **Error Tracking**

```javascript
// Add to server/src/app.js
const Sentry = require("@sentry/node");
Sentry.init({ dsn: process.env.SENTRY_DSN });
```

### **Backup Strategy**

#### **Database Backup**

```bash
# Create backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump matrimony_production > /backups/matrimony_$DATE.sql
aws s3 cp /backups/matrimony_$DATE.sql s3://your-backup-bucket/

# Schedule daily backups
crontab -e
# Add: 0 2 * * * /path/to/backup-script.sh
```

#### **File Backup**

```bash
# Backup uploads directory
rsync -av /var/www/uploads/ /backups/uploads/
aws s3 sync /var/www/uploads/ s3://your-files-bucket/
```

### **Security Checklist**

- [ ] SSL certificate installed and configured
- [ ] Firewall configured (UFW/iptables)
- [ ] Database access restricted to application only
- [ ] Environment variables secured
- [ ] Regular security updates scheduled
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] File upload restrictions in place
- [ ] SQL injection protection enabled
- [ ] XSS protection headers set

### **Scaling Considerations**

#### **Horizontal Scaling**

- Load balancer (Nginx/AWS ALB)
- Multiple application instances
- Database read replicas
- CDN for static assets (CloudFlare/AWS CloudFront)
- Redis cluster for session storage

#### **Vertical Scaling**

- Increase server resources (CPU/RAM)
- Database optimization
- Connection pooling
- Query optimization

### **Cost Optimization**

#### **Estimated Monthly Costs**

- **Small Scale (1K users)**: $50-100/month

  - DigitalOcean Droplet: $20
  - Database: $15
  - CDN: $5
  - Domain/SSL: $10

- **Medium Scale (10K users)**: $200-400/month

  - Multiple servers: $100
  - Managed database: $80
  - CDN/Storage: $20
  - Monitoring: $20

- **Large Scale (100K+ users)**: $1000+/month
  - Load balancer + multiple instances
  - Database cluster
  - Advanced monitoring
  - Dedicated support

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

---

Made with ❤️ for the Indian matrimony community
