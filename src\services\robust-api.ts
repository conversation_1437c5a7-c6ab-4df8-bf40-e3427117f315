import axios, { AxiosInstance, AxiosResponse } from 'axios';

// Enhanced API response interface
export interface RobustApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: any[];
  meta?: {
    pagination?: {
      currentPage: number;
      totalPages: number;
      totalCount: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

// API configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:3001';

// Create axios instance with robust configuration
const createRobustAPI = (): AxiosInstance => {
  const instance = axios.create({
    baseURL: API_BASE_URL,
    timeout: 30000, // 30 seconds
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Request interceptor
  instance.interceptors.request.use(
    (config) => {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      console.error('Request interceptor error:', error);
      return Promise.reject(error);
    }
  );

  // Response interceptor with robust error handling
  instance.interceptors.response.use(
    (response: AxiosResponse): AxiosResponse => {
      return response;
    },
    (error) => {
      console.error('API Error:', error);

      // Handle different types of errors
      if (error.code === 'ECONNABORTED') {
        return Promise.resolve({
          data: {
            success: false,
            message: 'Request timeout. Please try again.',
            data: null
          }
        } as AxiosResponse);
      }

      if (error.response?.status === 404) {
        return Promise.resolve({
          data: {
            success: false,
            message: 'Feature not yet available',
            data: null
          }
        } as AxiosResponse);
      }

      if (error.response?.status === 401) {
        localStorage.removeItem('token');
        window.location.href = '/login';
        return Promise.reject(error);
      }

      if (!error.response) {
        return Promise.resolve({
          data: {
            success: false,
            message: 'Network error. Please check your connection.',
            data: null
          }
        } as AxiosResponse);
      }

      return Promise.reject(error);
    }
  );

  return instance;
};

// Create the robust API instance
const robustAPI = createRobustAPI();

// Generic API call wrapper with fallback data
const makeAPICall = async <T>(
  apiCall: () => Promise<AxiosResponse>,
  fallbackData: T,
  featureName?: string
): Promise<RobustApiResponse<T>> => {
  try {
    const response = await apiCall();
    return response.data;
  } catch (error: any) {
    console.warn(`API call failed for ${featureName || 'unknown feature'}:`, error.message);
    
    // Return fallback data instead of throwing
    return {
      success: false,
      message: `${featureName || 'Feature'} is currently unavailable`,
      data: fallbackData
    };
  }
};

// Robust Analytics API
export const robustAnalyticsAPI = {
  getDashboard: async (): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/analytics/dashboard'),
      {
        profileViews: 0,
        interestsSent: 0,
        interestsReceived: 0,
        mutualInterests: 0,
        messages: 0,
        profileCompletionPercentage: 75
      },
      'Dashboard Analytics'
    );
  },

  getProfileViews: async (period = '30d'): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/analytics/profile-views', { params: { period } }),
      {
        totalViews: 0,
        uniqueViewers: 0,
        chartData: [],
        period
      },
      'Profile Views Analytics'
    );
  },

  getInterestStats: async (period = '30d'): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/analytics/interests', { params: { period } }),
      {
        sent: { total: 0, pending: 0, accepted: 0, declined: 0 },
        received: { total: 0, pending: 0, accepted: 0, declined: 0 },
        mutual: 0,
        period
      },
      'Interest Analytics'
    );
  }
};

// Robust Horoscope API
export const robustHoroscopeAPI = {
  getHoroscope: async (): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/horoscope'),
      {
        horoscopeInfo: {
          birthTime: '',
          birthPlace: '',
          rashi: '',
          nakshatra: '',
          manglikStatus: 'dont_know'
        }
      },
      'Horoscope'
    );
  },

  uploadHoroscope: async (formData: FormData): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.post('/api/horoscope/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }),
      { uploaded: false },
      'Horoscope Upload'
    );
  },

  checkCompatibility: async (otherUserId: string): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.post('/api/horoscope/compatibility', { otherUserId }),
      {
        compatibility: {
          score: 75,
          level: 'Good',
          factors: [],
          recommendation: 'Compatibility analysis will be available soon.'
        }
      },
      'Horoscope Compatibility'
    );
  }
};

// Robust Notifications API
export const robustNotificationAPI = {
  getNotifications: async (page = 1, limit = 20): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/notifications', { params: { page, limit } }),
      {
        notifications: [],
        pagination: {
          currentPage: page,
          totalPages: 0,
          totalCount: 0,
          hasNext: false,
          hasPrev: false
        }
      },
      'Notifications'
    );
  },

  getUnreadCount: async (): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/notifications/unread-count'),
      { unreadCount: 0 },
      'Unread Notifications Count'
    );
  },

  markAsRead: async (notificationId: string): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.put(`/api/notifications/${notificationId}/read`),
      { marked: true },
      'Mark Notification as Read'
    );
  }
};

// Robust Success Stories API
export const robustSuccessStoriesAPI = {
  getStories: async (page = 1, limit = 10): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/success-stories', { params: { page, limit } }),
      {
        stories: [
          {
            id: 'sample-1',
            groomName: 'Arjun',
            brideName: 'Priya',
            groomLocation: 'Mumbai',
            brideLocation: 'Delhi',
            marriageDate: '2024-02-14',
            story: 'We found each other through this amazing platform and are now happily married!',
            likes: 45,
            views: 234
          }
        ],
        pagination: {
          currentPage: page,
          totalPages: 1,
          totalCount: 1,
          hasNext: false,
          hasPrev: false
        }
      },
      'Success Stories'
    );
  },

  submitStory: async (storyData: any): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.post('/api/success-stories', storyData),
      { submitted: true },
      'Submit Success Story'
    );
  }
};

// Enhanced existing APIs with fallbacks
export const enhancedUserAPI = {
  ...robustAPI,
  
  getProfile: async (): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/users/profile'),
      {
        id: 'temp-user',
        email: '<EMAIL>',
        personalInfo: {},
        profileCompletionPercentage: 50
      },
      'User Profile'
    );
  },

  updateProfile: async (section: string, data: any): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.put(`/api/users/profile/${section}`, data),
      { updated: true },
      `Update ${section} Profile`
    );
  }
};

export const enhancedSearchAPI = {
  search: async (filters: any): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/search', { params: filters }),
      {
        profiles: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalCount: 0,
          hasNext: false,
          hasPrev: false
        }
      },
      'Profile Search'
    );
  },

  getRecommendations: async (limit = 10): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/search/recommendations', { params: { limit } }),
      { profiles: [] },
      'Recommendations'
    );
  }
};

export const enhancedInterestAPI = {
  getInterests: async (type = 'all'): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.get('/api/interests', { params: { type } }),
      {
        sent: [],
        received: [],
        mutual: []
      },
      'Interests'
    );
  },

  sendInterest: async (userId: string, message?: string): Promise<RobustApiResponse> => {
    return makeAPICall(
      () => robustAPI.post('/api/interests/send', { userId, message }),
      { sent: true },
      'Send Interest'
    );
  }
};

// Export all robust APIs
export {
  robustAPI as api,
  robustAnalyticsAPI as analyticsAPI,
  robustHoroscopeAPI as horoscopeAPI,
  robustNotificationAPI as notificationAPI,
  robustSuccessStoriesAPI as successStoriesAPI,
  enhancedUserAPI as userAPI,
  enhancedSearchAPI as searchAPI,
  enhancedInterestAPI as interestAPI
};

export default robustAPI;
