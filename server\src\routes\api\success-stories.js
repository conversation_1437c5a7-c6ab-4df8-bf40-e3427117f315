const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, query, validationResult } = require('express-validator');

// Configure multer for story photos
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../../uploads/success-stories');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `story-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 3
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.'));
    }
  }
});

// In-memory storage for success stories (in production, use proper database table)
let successStories = [];
let storyIdCounter = 1;

// Helper function to generate story ID
const generateStoryId = () => {
  return `story_${storyIdCounter++}_${Date.now()}`;
};

// Create sample success stories
const createSampleStories = () => {
  const sampleStories = [
    {
      id: generateStoryId(),
      groomName: 'Arjun',
      brideName: 'Priya',
      groomAge: 31,
      brideAge: 30,
      groomLocation: 'Mumbai',
      brideLocation: 'Delhi',
      marriageDate: '2024-02-14',
      story: 'We met through this platform and instantly connected over our shared love for travel and technology. After months of conversations and meeting each other\'s families, we knew we were meant to be together. Thank you for helping us find our perfect match!',
      photos: ['/uploads/success-stories/sample-1.jpg'],
      isApproved: true,
      isPublic: true,
      submittedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      approvedAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000).toISOString(),
      likes: 45,
      views: 234
    },
    {
      id: generateStoryId(),
      groomName: 'Karthik',
      brideName: 'Meera',
      groomAge: 34,
      brideAge: 27,
      groomLocation: 'Chennai',
      brideLocation: 'Kochi',
      marriageDate: '2024-01-20',
      story: 'Our families had been looking for suitable matches for us for years. When we connected here, we realized we had so much in common - our values, our dreams, and our love for classical music. Our wedding was a beautiful blend of Tamil and Malayalam traditions.',
      photos: ['/uploads/success-stories/sample-2.jpg'],
      isApproved: true,
      isPublic: true,
      submittedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
      approvedAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000).toISOString(),
      likes: 67,
      views: 456
    },
    {
      id: generateStoryId(),
      groomName: 'Vikram',
      brideName: 'Ananya',
      groomAge: 33,
      brideAge: 28,
      groomLocation: 'Pune',
      brideLocation: 'Hyderabad',
      marriageDate: '2023-12-15',
      story: 'What started as a simple interest turned into the most beautiful journey of our lives. We spent hours talking about our careers, families, and future plans. The platform made it so easy to connect with someone who truly understood and complemented me.',
      photos: ['/uploads/success-stories/sample-3.jpg'],
      isApproved: true,
      isPublic: true,
      submittedAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
      approvedAt: new Date(Date.now() - 55 * 24 * 60 * 60 * 1000).toISOString(),
      likes: 89,
      views: 678
    }
  ];

  successStories.push(...sampleStories);
};

// Initialize sample stories
createSampleStories();

// Get success stories (public endpoint)
router.get('/', [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 20 }).toInt(),
  query('sortBy').optional().isIn(['newest', 'popular', 'likes'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { page = 1, limit = 10, sortBy = 'newest' } = req.query;
    const offset = (page - 1) * limit;

    // Filter approved and public stories
    let stories = successStories.filter(story => story.isApproved && story.isPublic);

    // Sort stories
    switch (sortBy) {
      case 'popular':
        stories.sort((a, b) => b.views - a.views);
        break;
      case 'likes':
        stories.sort((a, b) => b.likes - a.likes);
        break;
      case 'newest':
      default:
        stories.sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
        break;
    }

    // Paginate
    const paginatedStories = stories.slice(offset, offset + limit);
    const totalCount = stories.length;

    // Increment view count for displayed stories
    paginatedStories.forEach(story => {
      const storyIndex = successStories.findIndex(s => s.id === story.id);
      if (storyIndex !== -1) {
        successStories[storyIndex].views++;
      }
    });

    res.json({
      success: true,
      data: {
        stories: paginatedStories,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: offset + limit < totalCount,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get success stories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get success stories'
    });
  }
});

// Submit success story
router.post('/', authenticateToken, upload.array('photos', 3), [
  body('groomName').notEmpty().isLength({ min: 2, max: 50 }).trim(),
  body('brideName').notEmpty().isLength({ min: 2, max: 50 }).trim(),
  body('groomAge').isInt({ min: 18, max: 100 }),
  body('brideAge').isInt({ min: 18, max: 100 }),
  body('groomLocation').notEmpty().isLength({ min: 2, max: 100 }).trim(),
  body('brideLocation').notEmpty().isLength({ min: 2, max: 100 }).trim(),
  body('marriageDate').isISO8601(),
  body('story').notEmpty().isLength({ min: 50, max: 2000 }).trim(),
  body('isPublic').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const {
      groomName,
      brideName,
      groomAge,
      brideAge,
      groomLocation,
      brideLocation,
      marriageDate,
      story,
      isPublic = true
    } = req.body;

    // Check if user already has a story
    const existingStory = successStories.find(s => s.userId === userId);
    if (existingStory) {
      return res.status(409).json({
        success: false,
        message: 'You have already submitted a success story'
      });
    }

    // Process uploaded photos
    const photos = [];
    if (req.files && req.files.length > 0) {
      req.files.forEach(file => {
        photos.push(`/uploads/success-stories/${file.filename}`);
      });
    }

    // Create success story
    const successStory = {
      id: generateStoryId(),
      userId,
      groomName,
      brideName,
      groomAge: parseInt(groomAge),
      brideAge: parseInt(brideAge),
      groomLocation,
      brideLocation,
      marriageDate,
      story,
      photos,
      isApproved: false, // Requires admin approval
      isPublic: isPublic === 'true' || isPublic === true,
      submittedAt: new Date().toISOString(),
      approvedAt: null,
      likes: 0,
      views: 0
    };

    successStories.push(successStory);

    res.status(201).json({
      success: true,
      message: 'Success story submitted successfully. It will be reviewed and published soon.',
      data: { story: successStory }
    });

  } catch (error) {
    console.error('Submit success story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit success story'
    });
  }
});

// Get user's own story
router.get('/my-story', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const userStory = successStories.find(story => story.userId === userId);

    if (!userStory) {
      return res.status(404).json({
        success: false,
        message: 'No success story found'
      });
    }

    res.json({
      success: true,
      data: { story: userStory }
    });

  } catch (error) {
    console.error('Get user story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user story'
    });
  }
});

// Like a success story
router.post('/:storyId/like', authenticateToken, async (req, res) => {
  try {
    const { storyId } = req.params;
    const userId = req.user.id;

    const storyIndex = successStories.findIndex(story => 
      story.id === storyId && story.isApproved && story.isPublic
    );

    if (storyIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    // For simplicity, just increment likes (in production, track user likes)
    successStories[storyIndex].likes++;

    res.json({
      success: true,
      message: 'Story liked successfully',
      data: { 
        storyId,
        likes: successStories[storyIndex].likes
      }
    });

  } catch (error) {
    console.error('Like story error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to like story'
    });
  }
});

// Get success story statistics
router.get('/stats', async (req, res) => {
  try {
    const stats = {
      totalStories: successStories.filter(s => s.isApproved && s.isPublic).length,
      totalViews: successStories.reduce((sum, story) => sum + story.views, 0),
      totalLikes: successStories.reduce((sum, story) => sum + story.likes, 0),
      averageAge: {
        groom: Math.round(successStories.reduce((sum, story) => sum + story.groomAge, 0) / successStories.length),
        bride: Math.round(successStories.reduce((sum, story) => sum + story.brideAge, 0) / successStories.length)
      },
      topLocations: {
        groom: ['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Pune'],
        bride: ['Delhi', 'Mumbai', 'Hyderabad', 'Kochi', 'Bangalore']
      },
      recentMarriages: successStories
        .filter(s => s.isApproved && s.isPublic)
        .sort((a, b) => new Date(b.marriageDate) - new Date(a.marriageDate))
        .slice(0, 5)
        .map(story => ({
          groomName: story.groomName,
          brideName: story.brideName,
          marriageDate: story.marriageDate,
          locations: `${story.groomLocation} & ${story.brideLocation}`
        }))
    };

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get success story stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get success story statistics'
    });
  }
});

// Admin: Get pending stories (requires admin authentication)
router.get('/admin/pending', authenticateToken, async (req, res) => {
  try {
    // In production, check if user is admin
    const pendingStories = successStories.filter(story => !story.isApproved);

    res.json({
      success: true,
      data: { stories: pendingStories }
    });

  } catch (error) {
    console.error('Get pending stories error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get pending stories'
    });
  }
});

// Admin: Approve/reject story
router.put('/admin/:storyId/status', authenticateToken, [
  body('status').isIn(['approved', 'rejected']),
  body('reason').optional().isLength({ max: 500 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { storyId } = req.params;
    const { status, reason } = req.body;

    const storyIndex = successStories.findIndex(story => story.id === storyId);
    if (storyIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Success story not found'
      });
    }

    // Update story status
    successStories[storyIndex].isApproved = status === 'approved';
    successStories[storyIndex].approvedAt = status === 'approved' ? new Date().toISOString() : null;
    successStories[storyIndex].rejectionReason = status === 'rejected' ? reason : null;

    res.json({
      success: true,
      message: `Story ${status} successfully`,
      data: { story: successStories[storyIndex] }
    });

  } catch (error) {
    console.error('Update story status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update story status'
    });
  }
});

module.exports = router;
