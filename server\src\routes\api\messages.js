const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const { rateLimits } = require('../../middleware/rateLimiting');
const User = require('../../models/sequelize/User');
const { body, query, validationResult } = require('express-validator');

// In-memory storage for conversations and messages (in production, use proper database tables)
let conversations = [];
let messages = [];
let conversationIdCounter = 1;
let messageIdCounter = 1;

// Helper functions
const generateConversationId = () => {
  return `conv_${conversationIdCounter++}_${Date.now()}`;
};

const generateMessageId = () => {
  return `msg_${messageIdCounter++}_${Date.now()}`;
};

// Get conversations
router.get('/conversations', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Find conversations where user is a participant
    const userConversations = conversations.filter(conv => 
      conv.participant1Id === userId || conv.participant2Id === userId
    );

    const formattedConversations = [];

    for (const conv of userConversations) {
      const otherUserId = conv.participant1Id === userId ? conv.participant2Id : conv.participant1Id;
      const otherUser = await User.findByPk(otherUserId, {
        attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
      });

      if (otherUser) {
        const personal = otherUser.personalInfo || {};
        const photos = otherUser.profilePhotos || [];
        const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

        // Get last message
        const lastMessage = conv.lastMessageId ? 
          messages.find(m => m.id === conv.lastMessageId) : null;

        formattedConversations.push({
          ...conv,
          otherUser: {
            id: otherUser.id,
            name: personal.firstName && personal.lastName ? 
              `${personal.firstName} ${personal.lastName}` : 'Unknown',
            profilePicture: profilePicture?.filePath,
            lastActive: otherUser.lastActive
          },
          lastMessage: lastMessage ? {
            content: lastMessage.content,
            sentAt: lastMessage.sentAt,
            senderId: lastMessage.senderId
          } : null,
          unreadCount: messages.filter(m => 
            m.conversationId === conv.id && 
            m.receiverId === userId && 
            !m.isRead
          ).length
        });
      }
    }

    // Sort by last message time
    formattedConversations.sort((a, b) => {
      const aTime = a.lastMessageAt || a.createdAt;
      const bTime = b.lastMessageAt || b.createdAt;
      return new Date(bTime) - new Date(aTime);
    });

    res.json({
      success: true,
      data: { conversations: formattedConversations }
    });

  } catch (error) {
    console.error('Get conversations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get conversations'
    });
  }
});

// Create conversation
router.post('/conversations', authenticateToken, [
  body('otherUserId').notEmpty().withMessage('Other user ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { otherUserId } = req.body;

    if (userId === otherUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot create conversation with yourself'
      });
    }

    // Check if other user exists
    const otherUser = await User.findOne({
      where: {
        id: otherUserId,
        isActive: true,
        isBlocked: false
      }
    });

    if (!otherUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Check if conversation already exists
    const existingConversation = conversations.find(conv => 
      (conv.participant1Id === userId && conv.participant2Id === otherUserId) ||
      (conv.participant1Id === otherUserId && conv.participant2Id === userId)
    );

    if (existingConversation) {
      return res.json({
        success: true,
        message: 'Conversation already exists',
        data: { conversation: existingConversation }
      });
    }

    // Create new conversation
    const conversation = {
      id: generateConversationId(),
      participant1Id: userId,
      participant2Id: otherUserId,
      lastMessageId: null,
      lastMessageAt: null,
      participant1Deleted: false,
      participant2Deleted: false,
      participant1Blocked: false,
      participant2Blocked: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    conversations.push(conversation);

    res.status(201).json({
      success: true,
      message: 'Conversation created successfully',
      data: { conversation }
    });

  } catch (error) {
    console.error('Create conversation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create conversation'
    });
  }
});

// Get messages for a conversation
router.get('/conversations/:conversationId/messages', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 100 }).toInt()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { conversationId } = req.params;
    const { page = 1, limit = 50 } = req.query;
    const userId = req.user.id;

    // Check if user is participant in conversation
    const conversation = conversations.find(conv => 
      conv.id === conversationId && 
      (conv.participant1Id === userId || conv.participant2Id === userId)
    );

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    // Get messages for conversation
    const conversationMessages = messages
      .filter(m => m.conversationId === conversationId)
      .sort((a, b) => new Date(a.sentAt) - new Date(b.sentAt));

    // Pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedMessages = conversationMessages.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: { 
        messages: paginatedMessages,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(conversationMessages.length / limit),
          totalMessages: conversationMessages.length,
          hasNext: endIndex < conversationMessages.length,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get messages error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get messages'
    });
  }
});

// Send message
router.post('/conversations/:conversationId/messages', authenticateToken, rateLimits.messages, [
  body('content').notEmpty().isLength({ max: 1000 }).trim().withMessage('Message content is required and must be less than 1000 characters'),
  body('messageType').optional().isIn(['text', 'image', 'voice', 'video', 'document'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { conversationId } = req.params;
    const { content, messageType = 'text' } = req.body;
    const userId = req.user.id;

    // Check if user is participant in conversation
    const conversation = conversations.find(conv => 
      conv.id === conversationId && 
      (conv.participant1Id === userId || conv.participant2Id === userId)
    );

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    // Get receiver ID
    const receiverId = conversation.participant1Id === userId ? 
      conversation.participant2Id : conversation.participant1Id;

    // Create message
    const message = {
      id: generateMessageId(),
      conversationId,
      senderId: userId,
      receiverId,
      messageType,
      content,
      filePath: null,
      isRead: false,
      isDelivered: true,
      readAt: null,
      deliveredAt: new Date().toISOString(),
      isFlagged: false,
      flaggedReason: null,
      sentAt: new Date().toISOString()
    };

    messages.push(message);

    // Update conversation
    const conversationIndex = conversations.findIndex(c => c.id === conversationId);
    if (conversationIndex !== -1) {
      conversations[conversationIndex].lastMessageId = message.id;
      conversations[conversationIndex].lastMessageAt = message.sentAt;
      conversations[conversationIndex].updatedAt = message.sentAt;
    }

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: { message }
    });

  } catch (error) {
    console.error('Send message error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send message'
    });
  }
});

// Mark messages as read
router.put('/conversations/:conversationId/read', authenticateToken, async (req, res) => {
  try {
    const { conversationId } = req.params;
    const userId = req.user.id;

    // Check if user is participant in conversation
    const conversation = conversations.find(conv => 
      conv.id === conversationId && 
      (conv.participant1Id === userId || conv.participant2Id === userId)
    );

    if (!conversation) {
      return res.status(404).json({
        success: false,
        message: 'Conversation not found'
      });
    }

    // Mark all unread messages as read
    const updatedCount = messages.filter(m => 
      m.conversationId === conversationId && 
      m.receiverId === userId && 
      !m.isRead
    ).length;

    messages.forEach(m => {
      if (m.conversationId === conversationId && 
          m.receiverId === userId && 
          !m.isRead) {
        m.isRead = true;
        m.readAt = new Date().toISOString();
      }
    });

    res.json({
      success: true,
      message: 'Messages marked as read',
      data: { updatedCount }
    });

  } catch (error) {
    console.error('Mark messages as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark messages as read'
    });
  }
});

// Block user
router.post('/conversations/block', authenticateToken, [
  body('userId').notEmpty().withMessage('User ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const currentUserId = req.user.id;
    const { userId } = req.body;

    if (currentUserId === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot block yourself'
      });
    }

    // Find conversation
    const conversation = conversations.find(conv => 
      (conv.participant1Id === currentUserId && conv.participant2Id === userId) ||
      (conv.participant1Id === userId && conv.participant2Id === currentUserId)
    );

    if (conversation) {
      // Update block status
      if (conversation.participant1Id === currentUserId) {
        conversation.participant1Blocked = true;
      } else {
        conversation.participant2Blocked = true;
      }
      conversation.updatedAt = new Date().toISOString();
    }

    res.json({
      success: true,
      message: 'User blocked successfully'
    });

  } catch (error) {
    console.error('Block user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to block user'
    });
  }
});

// Report user
router.post('/conversations/report', authenticateToken, [
  body('userId').notEmpty().withMessage('User ID is required'),
  body('reason').notEmpty().isLength({ max: 500 }).trim().withMessage('Reason is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const reporterId = req.user.id;
    const { userId, reason } = req.body;

    if (reporterId === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot report yourself'
      });
    }

    // In a real application, you would save this to a reports table
    console.log('User report:', {
      reporterId,
      reportedUserId: userId,
      reason,
      reportedAt: new Date().toISOString()
    });

    res.json({
      success: true,
      message: 'User reported successfully. Our team will review this report.'
    });

  } catch (error) {
    console.error('Report user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to report user'
    });
  }
});

// Get message statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const stats = {
      totalConversations: conversations.filter(conv => 
        conv.participant1Id === userId || conv.participant2Id === userId
      ).length,
      totalMessages: messages.filter(m => 
        m.senderId === userId || m.receiverId === userId
      ).length,
      unreadMessages: messages.filter(m => 
        m.receiverId === userId && !m.isRead
      ).length,
      sentMessages: messages.filter(m => m.senderId === userId).length,
      receivedMessages: messages.filter(m => m.receiverId === userId).length
    };

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get message stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get message statistics'
    });
  }
});

module.exports = router;
