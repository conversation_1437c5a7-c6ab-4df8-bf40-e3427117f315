import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Card, 
  CardBody, 
  CardHeader, 
  CardFooter, 
  Button, 
  Avatar, 
  Badge, 
  Progress, 
  Divider,
  Chip
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { ProfileMatch } from '../types/user';

// Mock data for matches
const recentMatches: ProfileMatch[] = [
  {
    id: '1',
    userId: '101',
    name: '<PERSON><PERSON><PERSON>',
    age: 28,
    location: 'Mumbai, Maharashtra',
    profession: 'Software Engineer',
    education: 'B.Tech, Computer Science',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
    compatibilityScore: 85,
    isPremium: true,
    isVerified: true,
    lastActive: '2023-06-15T10:30:00Z',
    shortlisted: false,
    interestSent: false,
    interestReceived: true,
    viewed: true
  },
  {
    id: '2',
    userId: '102',
    name: '<PERSON><PERSON><PERSON>',
    age: 26,
    location: 'Bangalore, Karnataka',
    profession: 'Data Scientist',
    education: '<PERSON>.Sc, Statistics',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=3',
    compatibilityScore: 78,
    isPremium: false,
    isVerified: true,
    lastActive: '2023-06-14T14:20:00Z',
    shortlisted: true,
    interestSent: false,
    interestReceived: false,
    viewed: true
  },
  {
    id: '3',
    userId: '103',
    name: 'Pooja Verma',
    age: 27,
    location: 'Delhi, NCR',
    profession: 'Marketing Manager',
    education: 'MBA, Marketing',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=4',
    compatibilityScore: 72,
    isPremium: true,
    isVerified: true,
    lastActive: '2023-06-15T09:15:00Z',
    shortlisted: false,
    interestSent: true,
    interestReceived: false,
    viewed: true
  },
];

// Mock data for recent visitors
const recentVisitors: ProfileMatch[] = [
  {
    id: '4',
    userId: '104',
    name: 'Kavita Singh',
    age: 29,
    location: 'Pune, Maharashtra',
    profession: 'HR Manager',
    education: 'MBA, Human Resources',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=5',
    compatibilityScore: 68,
    isPremium: false,
    isVerified: true,
    lastActive: '2023-06-15T11:45:00Z',
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false
  },
  {
    id: '5',
    userId: '105',
    name: 'Meera Reddy',
    age: 25,
    location: 'Hyderabad, Telangana',
    profession: 'Architect',
    education: 'B.Arch',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=6',
    compatibilityScore: 75,
    isPremium: true,
    isVerified: true,
    lastActive: '2023-06-14T16:30:00Z',
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false
  },
];

export const Dashboard: React.FC = () => {
  const { user } = useAuth();
  
  // Calculate profile completion percentage
  const calculateProfileCompletion = () => {
    if (!user) return 0;
    
    let completedSections = 0;
    let totalSections = 6; // Personal, Family, Education, Lifestyle, Preferences, Horoscope
    
    if (user.personalInfo) completedSections++;
    if (user.familyInfo) completedSections++;
    if (user.educationCareer) completedSections++;
    if (user.lifestyle) completedSections++;
    if (user.partnerPreferences) completedSections++;
    if (user.horoscope) completedSections++;
    
    return Math.round((completedSections / totalSections) * 100);
  };
  
  const profileCompletion = calculateProfileCompletion();
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Profile Overview */}
          <div className="space-y-6">
            <Card>
              <CardBody className="flex flex-col items-center text-center">
                <div className="relative mb-4">
                  <Avatar
                    src={user?.profilePicture || "https://img.heroui.chat/image/avatar?w=200&h=200&u=1"}
                    className="w-24 h-24"
                    isBordered
                    color="primary"
                  />
                  {user?.verificationStatus.photo && (
                    <Badge
                      content={<Icon icon="lucide:check" className="text-white" />}
                      color="success"
                      placement="bottom-right"
                      className="border-2 border-white"
                    />
                  )}
                </div>
                
                <h2 className="text-xl font-semibold">{user?.name}</h2>
                <p className="text-default-500 text-sm">ID: BM{user?.id}</p>
                
                <div className="flex items-center gap-2 mt-2">
                  <Chip
                    color="primary"
                    variant="flat"
                    size="sm"
                  >
                    {user?.membershipType.charAt(0).toUpperCase() + user?.membershipType.slice(1)} Member
                  </Chip>
                  
                  {user?.verificationStatus.email && (
                    <Chip
                      color="success"
                      variant="flat"
                      size="sm"
                      startContent={<Icon icon="lucide:check" className="text-success" />}
                    >
                      Verified
                    </Chip>
                  )}
                </div>
                
                <Divider className="my-4" />
                
                <div className="w-full">
                  <div className="flex justify-between mb-2">
                    <span className="text-sm">Profile Completion</span>
                    <span className="text-sm font-medium">{profileCompletion}%</span>
                  </div>
                  <Progress 
                    value={profileCompletion} 
                    color={profileCompletion < 50 ? "warning" : "success"}
                    className="mb-4"
                  />
                  
                  {profileCompletion < 100 && (
                    <Link to="/profile-creation">
                      <Button 
                        color="primary" 
                        variant="flat" 
                        size="sm" 
                        className="w-full"
                      >
                        Complete Your Profile
                      </Button>
                    </Link>
                  )}
                </div>
                
                <Divider className="my-4" />
                
                <div className="grid grid-cols-2 w-full gap-4">
                  <Link to="/settings">
                    <Button 
                      variant="flat" 
                      color="default" 
                      className="w-full"
                      startContent={<Icon icon="lucide:settings" />}
                    >
                      Settings
                    </Button>
                  </Link>
                  
                  <Link to="/profile/me">
                    <Button 
                      variant="flat" 
                      color="default" 
                      className="w-full"
                      startContent={<Icon icon="lucide:user" />}
                    >
                      View Profile
                    </Button>
                  </Link>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:bell" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Notifications</p>
                  <p className="text-small text-default-500">Your recent activities</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody className="p-0">
                <div className="flex flex-col divide-y divide-divider">
                  <div className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="bg-primary-100 p-2 rounded-full">
                        <Icon icon="lucide:heart" className="text-primary" />
                      </div>
                      <div>
                        <p className="text-sm">You have received 3 new interests</p>
                        <p className="text-xs text-default-500">2 hours ago</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="bg-success-100 p-2 rounded-full">
                        <Icon icon="lucide:check" className="text-success" />
                      </div>
                      <div>
                        <p className="text-sm">Your profile has been verified</p>
                        <p className="text-xs text-default-500">1 day ago</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="bg-warning-100 p-2 rounded-full">
                        <Icon icon="lucide:eye" className="text-warning" />
                      </div>
                      <div>
                        <p className="text-sm">5 people viewed your profile</p>
                        <p className="text-xs text-default-500">2 days ago</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
              <Divider />
              <CardFooter>
                <Link to="/notifications" className="w-full">
                  <Button variant="light" color="primary" className="w-full">
                    View All Notifications
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:crown" className="text-warning text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Upgrade Membership</p>
                  <p className="text-small text-default-500">Get more features</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody>
                <ul className="space-y-2">
                  <li className="flex items-center gap-2">
                    <Icon icon="lucide:check" className="text-success" />
                    <span className="text-sm">Contact details of interested profiles</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Icon icon="lucide:check" className="text-success" />
                    <span className="text-sm">Advanced search filters</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Icon icon="lucide:check" className="text-success" />
                    <span className="text-sm">Priority listing in search results</span>
                  </li>
                  <li className="flex items-center gap-2">
                    <Icon icon="lucide:check" className="text-success" />
                    <span className="text-sm">Dedicated relationship manager</span>
                  </li>
                </ul>
              </CardBody>
              <Divider />
              <CardFooter>
                <Link to="/subscription" className="w-full">
                  <Button color="primary" className="w-full">
                    Upgrade Now
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </div>
          
          {/* Middle Column - Matches */}
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:heart" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Today's Matches</p>
                  <p className="text-small text-default-500">Profiles that match your preferences</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody className="p-0">
                <div className="flex flex-col divide-y divide-divider">
                  {recentMatches.map((match) => (
                    <div key={match.id} className="p-4">
                      <div className="flex gap-4">
                        <Avatar
                          src={match.photo}
                          className="w-16 h-16"
                          isBordered={match.isPremium}
                          color={match.isPremium ? "warning" : "default"}
                        />
                        <div className="flex-1">
                          <div className="flex justify-between items-start">
                            <div>
                              <h3 className="font-semibold flex items-center gap-1">
                                {match.name}
                                {match.isVerified && (
                                  <Icon icon="lucide:check-circle" className="text-success text-sm" />
                                )}
                              </h3>
                              <p className="text-xs text-default-500">{match.age} yrs, {match.location}</p>
                              <p className="text-xs text-default-500">{match.profession}</p>
                            </div>
                            <Chip color="primary" size="sm" variant="flat">
                              {match.compatibilityScore}% Match
                            </Chip>
                          </div>
                          
                          <div className="flex gap-2 mt-3">
                            <Link to={`/profile/${match.userId}`}>
                              <Button size="sm" variant="flat">
                                View Profile
                              </Button>
                            </Link>
                            
                            {match.interestReceived ? (
                              <Button size="sm" color="success" variant="flat">
                                Accept Interest
                              </Button>
                            ) : match.interestSent ? (
                              <Button size="sm" color="primary" variant="flat" isDisabled>
                                Interest Sent
                              </Button>
                            ) : (
                              <Button size="sm" color="primary" variant="solid">
                                Send Interest
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
              <Divider />
              <CardFooter>
                <Link to="/matches" className="w-full">
                  <Button variant="light" color="primary" className="w-full">
                    View All Matches
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:message-circle" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Recent Messages</p>
                  <p className="text-small text-default-500">Your recent conversations</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody className="p-0">
                <div className="flex flex-col divide-y divide-divider">
                  <div className="p-4">
                    <div className="flex gap-4">
                      <Avatar src="https://img.heroui.chat/image/avatar?w=200&h=200&u=2" />
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <h3 className="font-semibold">Anjali Sharma</h3>
                          <span className="text-xs text-default-500">2h ago</span>
                        </div>
                        <p className="text-sm text-default-500 line-clamp-1">
                          Hi, I saw your profile and would like to know more about you...
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex gap-4">
                      <Avatar src="https://img.heroui.chat/image/avatar?w=200&h=200&u=3" />
                      <div className="flex-1">
                        <div className="flex justify-between">
                          <h3 className="font-semibold">Neha Patel</h3>
                          <span className="text-xs text-default-500">1d ago</span>
                        </div>
                        <p className="text-sm text-default-500 line-clamp-1">
                          Thank you for accepting my interest. I would love to connect...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
              <Divider />
              <CardFooter>
                <Link to="/messages" className="w-full">
                  <Button variant="light" color="primary" className="w-full">
                    View All Messages
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          </div>
          
          {/* Right Column - Activities & Recommendations */}
          <div className="space-y-6">
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:activity" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Account Activity</p>
                  <p className="text-small text-default-500">Your profile statistics</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-default-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-primary">24</p>
                    <p className="text-xs text-default-600">Profile Views</p>
                  </div>
                  
                  <div className="bg-default-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-primary">8</p>
                    <p className="text-xs text-default-600">Interests Received</p>
                  </div>
                  
                  <div className="bg-default-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-primary">5</p>
                    <p className="text-xs text-default-600">Interests Sent</p>
                  </div>
                  
                  <div className="bg-default-50 p-4 rounded-lg text-center">
                    <p className="text-2xl font-bold text-primary">3</p>
                    <p className="text-xs text-default-600">Mutual Matches</p>
                  </div>
                </div>
              </CardBody>
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:eye" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Recent Profile Visitors</p>
                  <p className="text-small text-default-500">Who viewed your profile</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody className="p-0">
                <div className="flex flex-col divide-y divide-divider">
                  {recentVisitors.map((visitor) => (
                    <div key={visitor.id} className="p-4">
                      <div className="flex gap-4">
                        <Avatar
                          src={visitor.photo}
                          className="w-12 h-12"
                          isBordered={visitor.isPremium}
                          color={visitor.isPremium ? "warning" : "default"}
                        />
                        <div className="flex-1">
                          <div className="flex justify-between">
                            <h3 className="font-semibold text-sm">{visitor.name}</h3>
                            <span className="text-xs text-default-500">
                              {new Date(visitor.lastActive).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-xs text-default-500">{visitor.age} yrs, {visitor.location}</p>
                          
                          <div className="flex gap-2 mt-2">
                            <Link to={`/profile/${visitor.userId}`}>
                              <Button size="sm" variant="flat">
                                View Profile
                              </Button>
                            </Link>
                            
                            <Button size="sm" color="primary">
                              Send Interest
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
              <Divider />
              <CardFooter>
                <Link to="/visitors" className="w-full">
                  <Button variant="light" color="primary" className="w-full">
                    View All Visitors
                  </Button>
                </Link>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:calendar" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Upcoming Events</p>
                  <p className="text-small text-default-500">Matrimony meetups near you</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody className="p-0">
                <div className="flex flex-col divide-y divide-divider">
                  <div className="p-4">
                    <div className="flex gap-4">
                      <div className="bg-primary-100 p-2 rounded-lg text-center min-w-[60px]">
                        <p className="text-primary font-bold">15</p>
                        <p className="text-xs text-primary-600">JUN</p>
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">Mumbai Matrimony Meet</h3>
                        <p className="text-xs text-default-500">Taj Hotel, Mumbai</p>
                        <p className="text-xs text-default-500">10:00 AM - 6:00 PM</p>
                        <Button size="sm" variant="flat" color="primary" className="mt-2">
                          Register
                        </Button>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <div className="flex gap-4">
                      <div className="bg-primary-100 p-2 rounded-lg text-center min-w-[60px]">
                        <p className="text-primary font-bold">22</p>
                        <p className="text-xs text-primary-600">JUN</p>
                      </div>
                      <div>
                        <h3 className="font-semibold text-sm">Delhi Matrimony Conclave</h3>
                        <p className="text-xs text-default-500">The Lalit, Delhi</p>
                        <p className="text-xs text-default-500">11:00 AM - 7:00 PM</p>
                        <Button size="sm" variant="flat" color="primary" className="mt-2">
                          Register
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};