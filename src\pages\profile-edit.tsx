import React from "react";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Textarea,
  Divider,
  Progress,
  Spinner,
  Avatar
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAuth } from "../contexts/auth-context";
import { userAPI } from "../services/api";

export const ProfileEditPage: React.FC = () => {
  const { user, setUser } = useAuth();
  const [loading, setLoading] = React.useState(false);
  const [saving, setSaving] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [activeTab, setActiveTab] = React.useState("personal");

  // Form state
  const [personalInfo, setPersonalInfo] = React.useState({
    dateOfBirth: "",
    age: "",
    height: "",
    weight: "",
    maritalStatus: "",
    motherTongue: "",
    city: "",
    state: "",
    country: "India",
    aboutMe: ""
  });

  const [educationCareer, setEducationCareer] = React.useState({
    highestEducation: "",
    educationDetails: "",
    occupation: "",
    companyName: "",
    annualIncome: "",
    workLocation: ""
  });

  const [familyInfo, setFamilyInfo] = React.useState({
    familyType: "",
    familyStatus: "",
    familyValues: "",
    fatherOccupation: "",
    motherOccupation: "",
    siblings: "",
    familyLocation: ""
  });

  const [lifestyle, setLifestyle] = React.useState({
    diet: "",
    smoking: "",
    drinking: "",
    hobbies: "",
    interests: "",
    music: "",
    movies: "",
    sports: ""
  });

  const [religiousInfo, setReligiousInfo] = React.useState({
    religion: "",
    caste: "",
    subCaste: "",
    gotra: "",
    manglik: "",
    star: "",
    raasi: ""
  });

  const [partnerPreferences, setPartnerPreferences] = React.useState({
    ageRange: { min: 21, max: 35 },
    heightRange: { min: 150, max: 180 },
    maritalStatus: [],
    education: [],
    occupation: [],
    location: [],
    religion: "",
    caste: "",
    manglik: "",
    diet: "",
    smoking: "",
    drinking: ""
  });

  // Load user data on mount
  React.useEffect(() => {
    if (user) {
      setPersonalInfo(user.personalInfo || {});
      setEducationCareer(user.educationCareer || {});
      setFamilyInfo(user.familyInfo || {});
      setLifestyle(user.lifestyle || {});
      setReligiousInfo(user.religiousInfo || {});
      setPartnerPreferences(user.partnerPreferences || {});
    }
  }, [user]);

  const saveSection = async (section: string, data: any) => {
    setSaving(true);
    setError(null);

    try {
      const updateData = { [section]: data };
      const response = await userAPI.updateProfile(updateData);

      if (response.success) {
        // Update user context
        setUser(response.data.user);
        
        // Show success message
        console.log("Profile updated successfully");
      } else {
        setError(response.message || "Failed to update profile");
      }
    } catch (err: any) {
      console.error("Profile update error:", err);
      setError(err.response?.data?.message || "Failed to update profile");
    } finally {
      setSaving(false);
    }
  };

  const handlePersonalInfoSave = () => {
    saveSection("personalInfo", personalInfo);
  };

  const handleEducationCareerSave = () => {
    saveSection("educationCareer", educationCareer);
  };

  const handleFamilyInfoSave = () => {
    saveSection("familyInfo", familyInfo);
  };

  const handleLifestyleSave = () => {
    saveSection("lifestyle", lifestyle);
  };

  const handleReligiousInfoSave = () => {
    saveSection("religiousInfo", religiousInfo);
  };

  const handlePartnerPreferencesSave = () => {
    saveSection("partnerPreferences", partnerPreferences);
  };

  const uploadProfilePicture = async (file: File) => {
    try {
      setLoading(true);
      const response = await userAPI.uploadProfilePicture(file);
      
      if (response.success) {
        setUser(prev => prev ? { ...prev, profilePicture: response.data.url } : null);
      }
    } catch (error) {
      console.error("Upload error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      uploadProfilePicture(file);
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Edit Profile</h1>
        <p className="text-default-500">
          Complete your profile to get better matches
        </p>
        
        {/* Profile completion */}
        <div className="mt-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Profile Completion</span>
            <span className="text-sm text-primary">{user.profileCompletionPercentage}%</span>
          </div>
          <Progress 
            value={user.profileCompletionPercentage} 
            color="primary" 
            className="max-w-md"
          />
        </div>
      </div>

      {/* Error message */}
      {error && (
        <Card className="mb-6 border-danger">
          <CardBody>
            <div className="flex items-center gap-2 text-danger">
              <Icon icon="lucide:alert-circle" />
              <span>{error}</span>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Profile Picture Section */}
      <Card className="mb-6">
        <CardHeader>
          <h2 className="text-xl font-semibold">Profile Picture</h2>
        </CardHeader>
        <CardBody>
          <div className="flex items-center gap-6">
            <Avatar
              src={user.profilePicture}
              size="lg"
              className="w-24 h-24"
              fallback={<Icon icon="lucide:user" size={32} />}
            />
            <div>
              <p className="text-sm text-default-500 mb-3">
                Upload a clear photo of yourself. Profiles with photos get 5x more responses.
              </p>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
                id="profile-picture-upload"
              />
              <Button
                as="label"
                htmlFor="profile-picture-upload"
                color="primary"
                variant="flat"
                startContent={<Icon icon="lucide:camera" />}
                isLoading={loading}
              >
                {user.profilePicture ? "Change Photo" : "Upload Photo"}
              </Button>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Navigation Tabs */}
      <div className="flex flex-wrap gap-2 mb-6">
        {[
          { key: "personal", label: "Personal Info", icon: "lucide:user" },
          { key: "education", label: "Education & Career", icon: "lucide:graduation-cap" },
          { key: "family", label: "Family Info", icon: "lucide:users" },
          { key: "lifestyle", label: "Lifestyle", icon: "lucide:heart" },
          { key: "religious", label: "Religious Info", icon: "lucide:star" },
          { key: "preferences", label: "Partner Preferences", icon: "lucide:search" }
        ].map((tab) => (
          <Button
            key={tab.key}
            variant={activeTab === tab.key ? "solid" : "flat"}
            color={activeTab === tab.key ? "primary" : "default"}
            startContent={<Icon icon={tab.icon} size={16} />}
            onPress={() => setActiveTab(tab.key)}
            size="sm"
          >
            {tab.label}
          </Button>
        ))}
      </div>

      {/* Personal Info Tab */}
      {activeTab === "personal" && (
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">Personal Information</h2>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Date of Birth"
                type="date"
                value={personalInfo.dateOfBirth}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, dateOfBirth: e.target.value }))}
              />
              <Input
                label="Age"
                type="number"
                value={personalInfo.age}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, age: e.target.value }))}
              />
              <Input
                label="Height (cm)"
                type="number"
                value={personalInfo.height}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, height: e.target.value }))}
              />
              <Input
                label="Weight (kg)"
                type="number"
                value={personalInfo.weight}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, weight: e.target.value }))}
              />
              <Select
                label="Marital Status"
                selectedKeys={personalInfo.maritalStatus ? [personalInfo.maritalStatus] : []}
                onSelectionChange={(keys) => {
                  const value = Array.from(keys)[0] as string;
                  setPersonalInfo(prev => ({ ...prev, maritalStatus: value }));
                }}
              >
                <SelectItem key="never_married">Never Married</SelectItem>
                <SelectItem key="divorced">Divorced</SelectItem>
                <SelectItem key="widowed">Widowed</SelectItem>
                <SelectItem key="separated">Separated</SelectItem>
              </Select>
              <Input
                label="Mother Tongue"
                value={personalInfo.motherTongue}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, motherTongue: e.target.value }))}
              />
              <Input
                label="City"
                value={personalInfo.city}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, city: e.target.value }))}
              />
              <Input
                label="State"
                value={personalInfo.state}
                onChange={(e) => setPersonalInfo(prev => ({ ...prev, state: e.target.value }))}
              />
            </div>
            
            <Textarea
              label="About Me"
              placeholder="Tell us about yourself..."
              value={personalInfo.aboutMe}
              onChange={(e) => setPersonalInfo(prev => ({ ...prev, aboutMe: e.target.value }))}
              maxRows={4}
            />

            <div className="flex justify-end">
              <Button
                color="primary"
                onPress={handlePersonalInfoSave}
                isLoading={saving}
                startContent={<Icon icon="lucide:save" />}
              >
                Save Personal Info
              </Button>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Add other tabs here... */}
      {/* For brevity, I'll add the other tabs in the next part */}
    </div>
  );
};
