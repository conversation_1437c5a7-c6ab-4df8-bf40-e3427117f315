import React from 'react';
import { Link } from 'react-router-dom';
import { Icon } from '@iconify/react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-white border-t border-divider py-8 mt-auto">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center gap-2 mb-4">
              <Icon icon="lucide:heart" className="text-primary text-2xl" />
              <h3 className="font-bold text-xl">BharatMatrimony</h3>
            </div>
            <p className="text-default-600 text-sm mb-4">
              India's most trusted matrimony service for Indians worldwide
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-default-600 hover:text-primary">
                <Icon icon="logos:facebook" className="text-xl" />
              </a>
              <a href="#" className="text-default-600 hover:text-primary">
                <Icon icon="logos:instagram-icon" className="text-xl" />
              </a>
              <a href="#" className="text-default-600 hover:text-primary">
                <Icon icon="logos:twitter" className="text-xl" />
              </a>
              <a href="#" className="text-default-600 hover:text-primary">
                <Icon icon="logos:youtube-icon" className="text-xl" />
              </a>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-base mb-4">Company</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/about" className="text-default-600 hover:text-primary">About Us</Link>
              </li>
              <li>
                <Link to="/success-stories" className="text-default-600 hover:text-primary">Success Stories</Link>
              </li>
              <li>
                <Link to="/careers" className="text-default-600 hover:text-primary">Careers</Link>
              </li>
              <li>
                <Link to="/contact" className="text-default-600 hover:text-primary">Contact Us</Link>
              </li>
              <li>
                <Link to="/press" className="text-default-600 hover:text-primary">Press & Media</Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-base mb-4">Privacy & Terms</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/privacy-policy" className="text-default-600 hover:text-primary">Privacy Policy</Link>
              </li>
              <li>
                <Link to="/terms" className="text-default-600 hover:text-primary">Terms of Service</Link>
              </li>
              <li>
                <Link to="/refund-policy" className="text-default-600 hover:text-primary">Refund Policy</Link>
              </li>
              <li>
                <Link to="/safety-tips" className="text-default-600 hover:text-primary">Safety Tips</Link>
              </li>
              <li>
                <Link to="/report" className="text-default-600 hover:text-primary">Report Misuse</Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h4 className="font-semibold text-base mb-4">Help & Support</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/help" className="text-default-600 hover:text-primary">Help Center</Link>
              </li>
              <li>
                <Link to="/faq" className="text-default-600 hover:text-primary">FAQs</Link>
              </li>
              <li>
                <Link to="/feedback" className="text-default-600 hover:text-primary">Feedback</Link>
              </li>
              <li>
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:phone" className="text-primary" />
                  <span className="text-default-600">+91 1800-123-4567</span>
                </div>
              </li>
              <li>
                <div className="flex items-center gap-2">
                  <Icon icon="lucide:mail" className="text-primary" />
                  <span className="text-default-600"><EMAIL></span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-8 pt-6 border-t border-divider">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-default-500 text-sm mb-4 md:mb-0">
              © {new Date().getFullYear()} BharatMatrimony. All rights reserved.
            </p>
            <div className="flex items-center space-x-4">
              <img src="https://img.heroui.chat/image/finance?w=40&h=24&u=1" alt="Visa" className="h-6" />
              <img src="https://img.heroui.chat/image/finance?w=40&h=24&u=2" alt="Mastercard" className="h-6" />
              <img src="https://img.heroui.chat/image/finance?w=40&h=24&u=3" alt="PayPal" className="h-6" />
              <img src="https://img.heroui.chat/image/finance?w=40&h=24&u=4" alt="UPI" className="h-6" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};