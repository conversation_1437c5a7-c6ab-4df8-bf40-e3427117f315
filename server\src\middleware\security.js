const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const validator = require('validator');

// Rate limiting configurations
const createRateLimit = (windowMs, max, message, skipSuccessfulRequests = false) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      message,
      retryAfter: Math.round(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests,
    handler: (req, res) => {
      console.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
      res.status(429).json({
        success: false,
        message,
        retryAfter: Math.round(windowMs / 1000)
      });
    }
  });
};

// Different rate limits for different endpoints
const rateLimits = {
  // General API rate limit
  general: createRateLimit(15 * 60 * 1000, 100, 'Too many requests, please try again later'),
  
  // Authentication endpoints (stricter)
  auth: createRateLimit(15 * 60 * 1000, 5, 'Too many authentication attempts, please try again later'),
  
  // Password reset (very strict)
  passwordReset: createRateLimit(60 * 60 * 1000, 3, 'Too many password reset attempts, please try again in an hour'),
  
  // File uploads
  upload: createRateLimit(60 * 60 * 1000, 10, 'Too many file uploads, please try again later'),
  
  // Search endpoints
  search: createRateLimit(60 * 1000, 30, 'Too many search requests, please slow down'),
  
  // Message sending
  messaging: createRateLimit(60 * 1000, 20, 'Too many messages sent, please slow down'),
  
  // Interest sending
  interests: createRateLimit(60 * 60 * 1000, 50, 'Too many interests sent, please try again later')
};

// Security headers middleware
const securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "ws:", "wss:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginResourcePolicy: { policy: "cross-origin" },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

// Input validation and sanitization
const validateAndSanitize = {
  // Email validation
  email: (email) => {
    if (!email || typeof email !== 'string') return false;
    return validator.isEmail(email) && email.length <= 255;
  },

  // Password validation
  password: (password) => {
    if (!password || typeof password !== 'string') return false;
    return password.length >= 6 && password.length <= 128;
  },

  // Name validation
  name: (name) => {
    if (!name || typeof name !== 'string') return false;
    const sanitized = validator.escape(name.trim());
    return sanitized.length >= 2 && sanitized.length <= 100;
  },

  // Phone validation
  phone: (phone) => {
    if (!phone) return true; // Optional field
    if (typeof phone !== 'string') return false;
    return validator.isMobilePhone(phone, 'en-IN');
  },

  // Text content validation
  text: (text, maxLength = 1000) => {
    if (!text) return true; // Optional field
    if (typeof text !== 'string') return false;
    const sanitized = validator.escape(text.trim());
    return sanitized.length <= maxLength;
  },

  // URL validation
  url: (url) => {
    if (!url) return true; // Optional field
    if (typeof url !== 'string') return false;
    return validator.isURL(url, {
      protocols: ['http', 'https'],
      require_protocol: true
    });
  },

  // MongoDB ObjectId validation
  objectId: (id) => {
    if (!id || typeof id !== 'string') return false;
    return validator.isMongoId(id);
  },

  // UUID validation
  uuid: (id) => {
    if (!id || typeof id !== 'string') return false;
    return validator.isUUID(id);
  },

  // Age validation
  age: (age) => {
    if (!age) return true; // Optional field
    const numAge = parseInt(age);
    return !isNaN(numAge) && numAge >= 18 && numAge <= 100;
  },

  // Height validation (in cm)
  height: (height) => {
    if (!height) return true; // Optional field
    const numHeight = parseInt(height);
    return !isNaN(numHeight) && numHeight >= 100 && numHeight <= 250;
  }
};

// Request sanitization middleware
const sanitizeRequest = (req, res, next) => {
  // Sanitize common fields
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        // Remove potential XSS attempts
        req.body[key] = validator.escape(req.body[key].trim());
      }
    });
  }

  // Sanitize query parameters
  if (req.query) {
    Object.keys(req.query).forEach(key => {
      if (typeof req.query[key] === 'string') {
        req.query[key] = validator.escape(req.query[key].trim());
      }
    });
  }

  next();
};

// Suspicious activity detection
const detectSuspiciousActivity = (req, res, next) => {
  const suspiciousPatterns = [
    /\.\./,  // Path traversal
    /<script/i,  // XSS attempts
    /union.*select/i,  // SQL injection
    /javascript:/i,  // JavaScript injection
    /eval\(/i,  // Code injection
    /document\.cookie/i,  // Cookie theft attempts
    /alert\(/i,  // XSS alert attempts
  ];

  const userAgent = req.get('User-Agent') || '';
  const fullUrl = req.protocol + '://' + req.get('host') + req.originalUrl;
  const body = JSON.stringify(req.body || {});

  // Check for suspicious patterns
  const isSuspicious = suspiciousPatterns.some(pattern => 
    pattern.test(fullUrl) || 
    pattern.test(userAgent) || 
    pattern.test(body)
  );

  if (isSuspicious) {
    console.warn(`🚨 Suspicious request detected:`, {
      ip: req.ip,
      method: req.method,
      url: req.originalUrl,
      userAgent,
      timestamp: new Date().toISOString()
    });

    return res.status(400).json({
      success: false,
      message: 'Invalid request detected'
    });
  }

  next();
};

// File upload security
const validateFileUpload = (req, res, next) => {
  if (!req.file) return next();

  const file = req.file;
  const allowedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf'
  ];

  const maxFileSize = 5 * 1024 * 1024; // 5MB

  // Check file type
  if (!allowedMimeTypes.includes(file.mimetype)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file type. Only images and PDFs are allowed.'
    });
  }

  // Check file size
  if (file.size > maxFileSize) {
    return res.status(400).json({
      success: false,
      message: 'File too large. Maximum size is 5MB.'
    });
  }

  // Check for malicious file names
  const suspiciousExtensions = /\.(php|js|html|htm|exe|bat|cmd|scr)$/i;
  if (suspiciousExtensions.test(file.originalname)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid file name detected.'
    });
  }

  next();
};

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:3000',
      process.env.CLIENT_URL,
      process.env.ADMIN_URL
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, etc.)
    if (!origin) return callback(null, true);

    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'Accept',
    'Origin'
  ],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hours
};

// Request logging for security monitoring
const securityLogger = (req, res, next) => {
  const startTime = Date.now();

  // Log request
  console.log(`📥 ${req.method} ${req.originalUrl} from ${req.ip}`);

  // Log response
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    const logLevel = res.statusCode >= 400 ? '🚨' : '✅';
    
    console.log(`${logLevel} ${req.method} ${req.originalUrl} - ${res.statusCode} (${duration}ms)`);

    // Log failed authentication attempts
    if (req.originalUrl.includes('/auth/') && res.statusCode === 401) {
      console.warn(`🔐 Failed authentication attempt from ${req.ip} to ${req.originalUrl}`);
    }
  });

  next();
};

module.exports = {
  rateLimits,
  securityHeaders,
  validateAndSanitize,
  sanitizeRequest,
  detectSuspiciousActivity,
  validateFileUpload,
  corsOptions,
  securityLogger
};
