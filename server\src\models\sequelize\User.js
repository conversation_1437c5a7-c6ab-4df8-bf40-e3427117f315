const { DataTypes } = require("sequelize");
const { sequelize } = require("../../config/database");
const bcrypt = require("bcryptjs");

const User = sequelize.define(
  "User",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100],
      },
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [6, 255],
      },
    },
    phone: {
      type: DataTypes.STRING(20),
      allowNull: true,
      validate: {
        is: /^[\+]?[1-9][\d]{0,15}$/,
      },
    },
    profileType: {
      type: DataTypes.ENUM(
        "self",
        "son",
        "daughter",
        "brother",
        "sister",
        "relative",
        "friend"
      ),
      defaultValue: "self",
    },
    membershipType: {
      type: DataTypes.ENUM("free", "silver", "gold", "platinum"),
      defaultValue: "free",
    },
    profilePicture: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    profilePhotos: {
      type: DataTypes.JSON,
      defaultValue: [],
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
    isBlocked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    profileCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    profileCompletionPercentage: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      validate: {
        min: 0,
        max: 100,
      },
    },
    profileViews: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
    },
    lastActive: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    subscriptionExpiry: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    emailVerificationToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    emailVerifiedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    phoneVerificationOTP: {
      type: DataTypes.STRING(6),
      allowNull: true,
    },
    phoneVerificationOTPExpiry: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    phoneVerifiedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    passwordResetToken: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    passwordResetExpiry: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    // Personal Information
    personalInfo: {
      type: DataTypes.JSON,
      defaultValue: {},
      validate: {
        isValidPersonalInfo(value) {
          if (value && typeof value !== "object") {
            throw new Error("Personal info must be an object");
          }
        },
      },
    },
    // Family Information
    familyInfo: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Education and Career
    educationCareer: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Lifestyle
    lifestyle: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Religious Information
    religiousInfo: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Partner Preferences
    partnerPreferences: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Verification Status
    verificationStatus: {
      type: DataTypes.JSON,
      defaultValue: {
        email: false,
        phone: false,
        photo: false,
        document: false,
      },
    },
    // Premium Features
    premiumFeatures: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Horoscope Information
    horoscope: {
      type: DataTypes.JSON,
      defaultValue: {},
    },
    // Blocked Users
    blockedUsers: {
      type: DataTypes.JSON,
      defaultValue: [],
    },
    // Shortlisted Profiles
    shortlistedProfiles: {
      type: DataTypes.JSON,
      defaultValue: [],
    },
    // Notification Preferences
    notificationPreferences: {
      type: DataTypes.JSON,
      defaultValue: {
        email: {
          newInterest: true,
          interestAccepted: true,
          newMessage: true,
          profileViewed: false,
          systemUpdates: true,
        },
        sms: {
          newInterest: true,
          interestAccepted: true,
          newMessage: false,
          profileViewed: false,
          systemUpdates: false,
        },
        push: {
          newInterest: true,
          interestAccepted: true,
          newMessage: true,
          profileViewed: true,
          systemUpdates: true,
        },
      },
    },
    // Admin fields
    isAdmin: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    blockReason: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    blockedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: "users",
    indexes: [
      { fields: ["email"], unique: true },
      { fields: ["phone"] },
      { fields: ["isActive"] },
      { fields: ["lastActive"] },
    ],
    hooks: {
      beforeCreate: async (user) => {
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed("password")) {
          user.password = await bcrypt.hash(user.password, 12);
        }
      },
    },
  }
);

// Instance methods
User.prototype.comparePassword = async function (password) {
  return bcrypt.compare(password, this.password);
};

User.prototype.calculateProfileCompletion = function () {
  let completionScore = 0;
  const maxScore = 100;

  // Basic info (30 points)
  if (this.name) completionScore += 5;
  if (this.email) completionScore += 5;
  if (this.phone) completionScore += 5;
  if (this.profilePicture) completionScore += 15;

  // Personal info (25 points)
  const personalInfo = this.personalInfo || {};
  if (personalInfo.dateOfBirth) completionScore += 5;
  if (personalInfo.height) completionScore += 3;
  if (personalInfo.maritalStatus) completionScore += 3;
  if (personalInfo.motherTongue) completionScore += 3;
  if (personalInfo.city) completionScore += 5;
  if (personalInfo.state) completionScore += 3;
  if (personalInfo.country) completionScore += 3;

  // Education & Career (20 points)
  const educationCareer = this.educationCareer || {};
  if (educationCareer.highestEducation) completionScore += 7;
  if (educationCareer.occupation) completionScore += 7;
  if (educationCareer.annualIncome) completionScore += 6;

  // Family info (10 points)
  const familyInfo = this.familyInfo || {};
  if (familyInfo.familyType) completionScore += 3;
  if (familyInfo.familyValues) completionScore += 3;
  if (familyInfo.familyStatus) completionScore += 4;

  // Religious info (10 points)
  const religiousInfo = this.religiousInfo || {};
  if (religiousInfo.religion) completionScore += 5;
  if (religiousInfo.caste) completionScore += 3;
  if (religiousInfo.manglik !== undefined) completionScore += 2;

  // Lifestyle (5 points)
  const lifestyle = this.lifestyle || {};
  if (lifestyle.diet) completionScore += 2;
  if (lifestyle.smoking) completionScore += 1;
  if (lifestyle.drinking) completionScore += 2;

  this.profileCompletionPercentage = Math.min(completionScore, maxScore);
  this.profileCompleted = this.profileCompletionPercentage >= 80;

  return this.profileCompletionPercentage;
};

User.prototype.updateLastActive = function () {
  this.lastActive = new Date();
  return this.save({ fields: ["lastActive"] });
};

User.prototype.isEmailVerified = function () {
  return !!this.emailVerifiedAt;
};

User.prototype.isPhoneVerified = function () {
  return !!this.phoneVerifiedAt;
};

// Class methods
User.getActiveUsers = function (options = {}) {
  return this.findAll({
    where: {
      isActive: true,
      isBlocked: false,
      ...options.where,
    },
    ...options,
  });
};

User.searchUsers = function (criteria = {}, options = {}) {
  const where = {
    isActive: true,
    isBlocked: false,
  };

  // Add search criteria
  if (criteria.ageRange) {
    where["personalInfo.age"] = {
      [sequelize.Sequelize.Op.between]: criteria.ageRange,
    };
  }

  if (criteria.city) {
    where["personalInfo.city"] = {
      [sequelize.Sequelize.Op.iLike]: `%${criteria.city}%`,
    };
  }

  if (criteria.religion) {
    where["religiousInfo.religion"] = criteria.religion;
  }

  return this.findAll({
    where,
    ...options,
  });
};

module.exports = User;
