const express = require('express');
const { body, query, validationResult } = require('express-validator');
const VideoCall = require('../models/VideoCall');
const User = require('../models/User');
const Interest = require('../models/Interest');
const { authenticateToken, requireFeature } = require('../middleware/auth');
const notificationService = require('../services/notificationService');

const router = express.Router();

// Initiate video call
router.post('/initiate', [
  authenticateToken,
  requireFeature('videoCall'),
  body('calleeId').isMongoId().withMessage('Invalid callee ID'),
  body('callType').optional().isIn(['video', 'audio']).withMessage('Invalid call type'),
  body('scheduledFor').optional().isISO8601().withMessage('Invalid scheduled time')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { calleeId, callType = 'video', scheduledFor } = req.body;
    const callerId = req.user._id;

    // Check if calling self
    if (callerId.toString() === calleeId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot call yourself'
      });
    }

    // Check if callee exists and is active
    const callee = await User.findById(calleeId);
    if (!callee || !callee.isActive) {
      return res.status(404).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    // Check if caller is blocked by callee
    if (callee.blockedUsers.includes(callerId)) {
      return res.status(403).json({
        success: false,
        message: 'You cannot call this user'
      });
    }

    // Check if there's mutual interest or premium feature
    const mutualInterest = await Interest.findOne({
      $or: [
        { from: callerId, to: calleeId, status: 'accepted' },
        { from: calleeId, to: callerId, status: 'accepted' }
      ]
    });

    if (!mutualInterest && req.user.membershipType === 'free') {
      return res.status(403).json({
        success: false,
        message: 'Video calling requires mutual interest or premium membership',
        code: 'PREMIUM_REQUIRED'
      });
    }

    // Check if callee has video call feature
    if (callee.membershipType === 'free' && !callee.premiumFeatures?.videoCall) {
      return res.status(403).json({
        success: false,
        message: 'The user you are trying to call does not have video calling enabled'
      });
    }

    // Create call room
    const isScheduled = !!scheduledFor;
    const call = await VideoCall.createCallRoom(callerId, calleeId, {
      callType,
      isScheduled,
      scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined
    });

    if (isScheduled) {
      // Send notification for scheduled call
      await notificationService.createNotification('call_scheduled', calleeId, {
        callerName: req.user.name,
        callerId,
        scheduledTime: scheduledFor,
        callType,
        actionUrl: `/calls/${call._id}`
      }, {
        sendEmail: true
      });

      res.status(201).json({
        success: true,
        message: 'Call scheduled successfully',
        data: {
          callId: call._id,
          roomId: call.roomId,
          scheduledFor: call.scheduledFor,
          status: call.status
        }
      });
    } else {
      // Initiate immediate call
      call.status = 'ringing';
      await call.save();

      // Send real-time notification to callee
      const io = req.app.get('io');
      if (io) {
        io.to(calleeId.toString()).emit('incoming-call', {
          callId: call._id,
          roomId: call.roomId,
          caller: {
            id: req.user._id,
            name: req.user.name,
            profilePicture: req.user.profilePicture
          },
          callType
        });
      }

      // Send push notification
      await notificationService.createNotification('incoming_call', calleeId, {
        callerName: req.user.name,
        callerId,
        callType,
        actionUrl: `/calls/${call._id}`
      });

      res.status(201).json({
        success: true,
        message: 'Call initiated successfully',
        data: {
          callId: call._id,
          roomId: call.roomId,
          status: call.status,
          callee: {
            id: callee._id,
            name: callee.name,
            profilePicture: callee.profilePicture
          }
        }
      });
    }
  } catch (error) {
    console.error('Initiate call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to initiate call'
    });
  }
});

// Accept video call
router.put('/:callId/accept', [
  authenticateToken,
  requireFeature('videoCall')
], async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user._id;

    const call = await VideoCall.findById(callId);

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user is the callee
    if (call.callee.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to accept this call'
      });
    }

    // Check if call is in ringing state
    if (call.status !== 'ringing') {
      return res.status(400).json({
        success: false,
        message: 'Call cannot be accepted in current state'
      });
    }

    await call.startCall();

    res.json({
      success: true,
      message: 'Call accepted successfully',
      data: {
        callId: call._id,
        roomId: call.roomId,
        status: call.status,
        startTime: call.startTime
      }
    });
  } catch (error) {
    console.error('Accept call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to accept call'
    });
  }
});

// Decline video call
router.put('/:callId/decline', [
  authenticateToken,
  body('reason').optional().isLength({ max: 200 }).withMessage('Reason too long')
], async (req, res) => {
  try {
    const { callId } = req.params;
    const { reason } = req.body;
    const userId = req.user._id;

    const call = await VideoCall.findById(callId);

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user is the callee
    if (call.callee.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to decline this call'
      });
    }

    // Check if call can be declined
    if (!['ringing', 'initiated'].includes(call.status)) {
      return res.status(400).json({
        success: false,
        message: 'Call cannot be declined in current state'
      });
    }

    await call.declineCall(reason);

    res.json({
      success: true,
      message: 'Call declined successfully',
      data: {
        callId: call._id,
        status: call.status
      }
    });
  } catch (error) {
    console.error('Decline call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to decline call'
    });
  }
});

// End video call
router.put('/:callId/end', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user._id;

    const call = await VideoCall.findById(callId);

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user is participant
    const isParticipant = [call.caller.toString(), call.callee.toString()].includes(userId.toString());
    if (!isParticipant) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to end this call'
      });
    }

    // Check if call can be ended
    if (!['accepted', 'ringing'].includes(call.status)) {
      return res.status(400).json({
        success: false,
        message: 'Call cannot be ended in current state'
      });
    }

    await call.endCall(userId);

    res.json({
      success: true,
      message: 'Call ended successfully',
      data: {
        callId: call._id,
        status: call.status,
        duration: call.duration,
        endTime: call.endTime
      }
    });
  } catch (error) {
    console.error('End call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to end call'
    });
  }
});

// Get call details
router.get('/:callId', authenticateToken, async (req, res) => {
  try {
    const { callId } = req.params;
    const userId = req.user._id;

    const call = await VideoCall.findById(callId)
      .populate('caller callee', 'name profilePicture');

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user is participant
    const isParticipant = [call.caller._id.toString(), call.callee._id.toString()].includes(userId.toString());
    if (!isParticipant) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to view this call'
      });
    }

    res.json({
      success: true,
      data: {
        call: {
          id: call._id,
          roomId: call.roomId,
          status: call.status,
          callType: call.callType,
          duration: call.duration,
          durationInMinutes: call.durationInMinutes,
          startTime: call.startTime,
          endTime: call.endTime,
          caller: call.caller,
          callee: call.callee,
          isScheduled: call.isScheduled,
          scheduledFor: call.scheduledFor,
          quality: call.quality,
          isSuccessful: call.isSuccessful,
          createdAt: call.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Get call details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call details'
    });
  }
});

// Get call history
router.get('/', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('status').optional().isIn(['initiated', 'ringing', 'accepted', 'declined', 'ended', 'missed']).withMessage('Invalid status'),
  query('callType').optional().isIn(['video', 'audio']).withMessage('Invalid call type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      status,
      callType
    } = req.query;

    const userId = req.user._id;

    const calls = await VideoCall.getCallHistory(userId, {
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      callType
    });

    const totalCount = await VideoCall.countDocuments({
      $or: [{ caller: userId }, { callee: userId }],
      ...(status && { status }),
      ...(callType && { callType })
    });

    // Format calls for response
    const formattedCalls = calls.map(call => {
      const isIncoming = call.callee._id.toString() === userId.toString();
      const otherParticipant = isIncoming ? call.caller : call.callee;

      return {
        id: call._id,
        type: isIncoming ? 'incoming' : 'outgoing',
        participant: {
          id: otherParticipant._id,
          name: otherParticipant.name,
          profilePicture: otherParticipant.profilePicture
        },
        status: call.status,
        callType: call.callType,
        duration: call.duration,
        durationInMinutes: call.durationInMinutes,
        startTime: call.startTime,
        endTime: call.endTime,
        isSuccessful: call.isSuccessful,
        createdAt: call.createdAt
      };
    });

    res.json({
      success: true,
      data: {
        calls: formattedCalls,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get call history error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call history'
    });
  }
});

// Get call statistics
router.get('/stats/summary', [
  authenticateToken,
  query('timeframe').optional().matches(/^\d+d$/).withMessage('Invalid timeframe format (e.g., 30d)')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const userId = req.user._id;

    const stats = await VideoCall.getCallStats(userId, timeframe);

    res.json({
      success: true,
      data: {
        stats,
        timeframe
      }
    });
  } catch (error) {
    console.error('Get call stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get call statistics'
    });
  }
});

// Rate call quality
router.put('/:callId/rate', [
  authenticateToken,
  body('rating').isInt({ min: 1, max: 5 }).withMessage('Rating must be between 1 and 5'),
  body('feedback').optional().isLength({ max: 500 }).withMessage('Feedback too long'),
  body('technicalIssues').optional().isArray().withMessage('Technical issues must be an array')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { callId } = req.params;
    const { rating, feedback, technicalIssues } = req.body;
    const userId = req.user._id;

    const call = await VideoCall.findById(callId);

    if (!call) {
      return res.status(404).json({
        success: false,
        message: 'Call not found'
      });
    }

    // Check if user is participant
    const isParticipant = [call.caller.toString(), call.callee.toString()].includes(userId.toString());
    if (!isParticipant) {
      return res.status(403).json({
        success: false,
        message: 'You are not authorized to rate this call'
      });
    }

    // Check if call is completed
    if (call.status !== 'ended') {
      return res.status(400).json({
        success: false,
        message: 'Can only rate completed calls'
      });
    }

    call.quality = {
      rating,
      feedback,
      technicalIssues: technicalIssues || []
    };

    await call.save();

    res.json({
      success: true,
      message: 'Call rated successfully',
      data: {
        callId: call._id,
        quality: call.quality
      }
    });
  } catch (error) {
    console.error('Rate call error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to rate call'
    });
  }
});

module.exports = router;
