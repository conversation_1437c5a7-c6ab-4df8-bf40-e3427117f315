const User = require('../models/User');
const Interest = require('../models/Interest');
const { Message, Conversation } = require('../models/Message');
const { UserSubscription } = require('../models/Subscription');
const SuccessStory = require('../models/SuccessStory');

class AnalyticsService {
  // User Analytics
  async getUserAnalytics(userId, timeframe = '30d') {
    try {
      const user = await User.findById(userId);
      if (!user) throw new Error('User not found');

      const days = this.parseTimeframe(timeframe);
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const analytics = {
        profile: await this.getProfileAnalytics(userId, startDate),
        interests: await this.getInterestAnalytics(userId, startDate),
        messages: await this.getMessageAnalytics(userId, startDate),
        matches: await this.getMatchAnalytics(userId, startDate),
        engagement: await this.getEngagementAnalytics(userId, startDate)
      };

      return analytics;
    } catch (error) {
      console.error('Error getting user analytics:', error);
      throw error;
    }
  }

  // Profile Analytics
  async getProfileAnalytics(userId, startDate) {
    const user = await User.findById(userId);
    
    // Profile views over time
    const profileViews = await this.getProfileViewsOverTime(userId, startDate);
    
    // Profile completion score
    const completionScore = user.profileCompletionPercentage;
    
    // Verification status
    const verificationStatus = user.verificationStatus;
    
    return {
      totalViews: user.profileViews,
      viewsInPeriod: profileViews.total,
      viewsOverTime: profileViews.timeline,
      completionScore,
      verificationStatus,
      membershipType: user.membershipType,
      lastActive: user.lastActive
    };
  }

  // Interest Analytics
  async getInterestAnalytics(userId, startDate) {
    const [sentStats, receivedStats] = await Promise.all([
      Interest.aggregate([
        {
          $match: {
            from: userId,
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]),
      Interest.aggregate([
        {
          $match: {
            to: userId,
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    // Interest success rate
    const sentAccepted = sentStats.find(s => s._id === 'accepted')?.count || 0;
    const sentTotal = sentStats.reduce((sum, s) => sum + s.count, 0);
    const successRate = sentTotal > 0 ? (sentAccepted / sentTotal) * 100 : 0;

    return {
      sent: {
        total: sentTotal,
        byStatus: sentStats,
        successRate: Math.round(successRate)
      },
      received: {
        total: receivedStats.reduce((sum, s) => sum + s.count, 0),
        byStatus: receivedStats
      }
    };
  }

  // Message Analytics
  async getMessageAnalytics(userId, startDate) {
    const [sentMessages, receivedMessages, conversations] = await Promise.all([
      Message.countDocuments({
        sender: userId,
        createdAt: { $gte: startDate }
      }),
      Message.countDocuments({
        receiver: userId,
        createdAt: { $gte: startDate }
      }),
      Conversation.countDocuments({
        participants: userId,
        createdAt: { $gte: startDate }
      })
    ]);

    // Response rate
    const responseRate = await this.calculateResponseRate(userId, startDate);

    return {
      sent: sentMessages,
      received: receivedMessages,
      newConversations: conversations,
      responseRate: Math.round(responseRate)
    };
  }

  // Match Analytics
  async getMatchAnalytics(userId, startDate) {
    // Mutual interests (matches)
    const mutualInterests = await Interest.aggregate([
      {
        $match: {
          $or: [
            { from: userId, status: 'accepted' },
            { to: userId, status: 'accepted' }
          ],
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ['$from', userId] },
              '$to',
              '$from'
            ]
          }
        }
      },
      {
        $count: 'total'
      }
    ]);

    const totalMatches = mutualInterests[0]?.total || 0;

    return {
      totalMatches,
      matchQuality: await this.calculateMatchQuality(userId),
      topMatchCriteria: await this.getTopMatchCriteria(userId)
    };
  }

  // Engagement Analytics
  async getEngagementAnalytics(userId, startDate) {
    const user = await User.findById(userId);
    
    // Days active in period
    const daysInPeriod = Math.ceil((new Date() - startDate) / (1000 * 60 * 60 * 24));
    const lastActiveDate = new Date(user.lastActive);
    const daysActive = lastActiveDate >= startDate ? 1 : 0; // Simplified calculation

    // Engagement score based on various activities
    const engagementScore = await this.calculateEngagementScore(userId, startDate);

    return {
      daysActive,
      daysInPeriod,
      engagementScore,
      lastActive: user.lastActive
    };
  }

  // Platform Analytics (Admin)
  async getPlatformAnalytics(timeframe = '30d') {
    try {
      const days = this.parseTimeframe(timeframe);
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

      const analytics = {
        users: await this.getUserStats(startDate),
        interests: await this.getInterestStats(startDate),
        messages: await this.getMessageStats(startDate),
        subscriptions: await this.getSubscriptionStats(startDate),
        success: await this.getSuccessStats(startDate),
        engagement: await this.getPlatformEngagement(startDate)
      };

      return analytics;
    } catch (error) {
      console.error('Error getting platform analytics:', error);
      throw error;
    }
  }

  // User Statistics
  async getUserStats(startDate) {
    const [totalUsers, newUsers, activeUsers, verifiedUsers] = await Promise.all([
      User.countDocuments({ isActive: true }),
      User.countDocuments({ createdAt: { $gte: startDate } }),
      User.countDocuments({ 
        lastActive: { $gte: startDate },
        isActive: true 
      }),
      User.countDocuments({ 
        'verificationStatus.email': true,
        'verificationStatus.phone': true 
      })
    ]);

    // User growth over time
    const userGrowth = await User.aggregate([
      {
        $match: { createdAt: { $gte: startDate } }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    return {
      total: totalUsers,
      new: newUsers,
      active: activeUsers,
      verified: verifiedUsers,
      growth: userGrowth
    };
  }

  // Interest Statistics
  async getInterestStats(startDate) {
    const [totalInterests, acceptanceRate] = await Promise.all([
      Interest.countDocuments({ createdAt: { $gte: startDate } }),
      Interest.aggregate([
        {
          $match: { createdAt: { $gte: startDate } }
        },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ])
    ]);

    const accepted = acceptanceRate.find(r => r._id === 'accepted')?.count || 0;
    const total = acceptanceRate.reduce((sum, r) => sum + r.count, 0);
    const rate = total > 0 ? (accepted / total) * 100 : 0;

    return {
      total: totalInterests,
      acceptanceRate: Math.round(rate),
      byStatus: acceptanceRate
    };
  }

  // Helper methods
  parseTimeframe(timeframe) {
    const match = timeframe.match(/(\d+)([dwmy])/);
    if (!match) return 30; // Default to 30 days

    const [, num, unit] = match;
    const multipliers = { d: 1, w: 7, m: 30, y: 365 };
    return parseInt(num) * (multipliers[unit] || 1);
  }

  async getProfileViewsOverTime(userId, startDate) {
    // This would require implementing a profile view tracking system
    // For now, return mock data
    return {
      total: 50,
      timeline: [
        { date: '2024-01-01', views: 5 },
        { date: '2024-01-02', views: 8 },
        // ... more data points
      ]
    };
  }

  async calculateResponseRate(userId, startDate) {
    const [sentMessages, responses] = await Promise.all([
      Message.countDocuments({
        sender: userId,
        createdAt: { $gte: startDate }
      }),
      Message.countDocuments({
        receiver: userId,
        createdAt: { $gte: startDate }
      })
    ]);

    return sentMessages > 0 ? (responses / sentMessages) * 100 : 0;
  }

  async calculateMatchQuality(userId) {
    // Calculate based on mutual interests and successful conversations
    // This is a simplified calculation
    const mutualInterests = await Interest.countDocuments({
      $or: [
        { from: userId, status: 'accepted' },
        { to: userId, status: 'accepted' }
      ]
    });

    return Math.min(mutualInterests * 10, 100); // Cap at 100
  }

  async getTopMatchCriteria(userId) {
    // Analyze user's successful matches to identify top criteria
    // This would require more complex analysis
    return ['Same City', 'Similar Education', 'Compatible Lifestyle'];
  }

  async calculateEngagementScore(userId, startDate) {
    const [profileViews, interestsSent, messagesExchanged] = await Promise.all([
      User.findById(userId).select('profileViews'),
      Interest.countDocuments({ from: userId, createdAt: { $gte: startDate } }),
      Message.countDocuments({ 
        $or: [{ sender: userId }, { receiver: userId }],
        createdAt: { $gte: startDate }
      })
    ]);

    // Simple engagement score calculation
    const score = (profileViews?.profileViews || 0) * 0.1 + 
                  interestsSent * 2 + 
                  messagesExchanged * 1;

    return Math.min(Math.round(score), 100);
  }

  async getMessageStats(startDate) {
    const totalMessages = await Message.countDocuments({ 
      createdAt: { $gte: startDate } 
    });

    return { total: totalMessages };
  }

  async getSubscriptionStats(startDate) {
    const [activeSubscriptions, newSubscriptions, revenue] = await Promise.all([
      UserSubscription.countDocuments({ status: 'active' }),
      UserSubscription.countDocuments({ createdAt: { $gte: startDate } }),
      UserSubscription.aggregate([
        {
          $match: { 
            createdAt: { $gte: startDate },
            status: 'active'
          }
        },
        {
          $group: {
            _id: null,
            total: { $sum: '$amountPaid' }
          }
        }
      ])
    ]);

    return {
      active: activeSubscriptions,
      new: newSubscriptions,
      revenue: revenue[0]?.total || 0
    };
  }

  async getSuccessStats(startDate) {
    const successStories = await SuccessStory.countDocuments({
      createdAt: { $gte: startDate },
      isApproved: true
    });

    return { stories: successStories };
  }

  async getPlatformEngagement(startDate) {
    const dailyActiveUsers = await User.countDocuments({
      lastActive: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) }
    });

    return { dailyActiveUsers };
  }
}

module.exports = new AnalyticsService();
