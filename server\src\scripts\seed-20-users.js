const { sequelize } = require("../config/database");
const User = require("../models/sequelize/User");

const create20Users = async () => {
  try {
    console.log("🌱 Creating 20 comprehensive seed users...");

    // Create 20 diverse sample users representing different Indian demographics
    const sampleUsers = [
      // User 1 - Software Engineer from Mumbai (Male, Gold Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "<PERSON><PERSON><PERSON>",
        phone: "+************",
        dateOfBirth: "1992-03-15",
        gender: "male",
        membershipType: "gold",
        personalInfo: {
          firstName: "<PERSON><PERSON><PERSON>",
          lastName: "<PERSON>",
          age: 31,
          height: 178,
          weight: 72,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Maharashtra",
          city: "Mumbai",
          complexion: "fair",
          bodyType: "athletic",
          aboutMe:
            "Software engineer passionate about technology and travel. Looking for an educated and understanding life partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Business",
          motherOccupation: "Teacher",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Computer Science",
          occupation: "Software Engineer",
          designation: "Senior Developer",
          companyType: "private",
          workExperienceYears: 8,
          annualIncomeRange: "15_20_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Sharma",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },

      // User 2 - Doctor from Delhi (Female, Platinum Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Dr. Priya Gupta",
        phone: "+************",
        dateOfBirth: "1993-08-20",
        gender: "female",
        membershipType: "platinum",
        personalInfo: {
          firstName: "Priya",
          lastName: "Gupta",
          age: 30,
          height: 162,
          weight: 55,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Delhi",
          city: "New Delhi",
          complexion: "wheatish",
          bodyType: "slim",
          aboutMe:
            "Pediatrician by profession, love reading and classical music. Seeking a caring and family-oriented partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "rich",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Doctor",
          motherOccupation: "Professor",
        },
        educationCareer: {
          highestEducation: "doctorate",
          educationField: "Medicine",
          occupation: "Doctor",
          designation: "Pediatrician",
          companyType: "private",
          workExperienceYears: 6,
          annualIncomeRange: "20_30_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Gupta",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["masters", "doctorate"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },

      // User 3 - Business Owner from Ahmedabad (Male, Silver Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Rahul Patel",
        phone: "+************",
        dateOfBirth: "1988-12-10",
        gender: "male",
        membershipType: "silver",
        personalInfo: {
          firstName: "Rahul",
          lastName: "Patel",
          age: 35,
          height: 180,
          weight: 75,
          maritalStatus: "never_married",
          motherTongue: "gujarati",
          country: "India",
          state: "Gujarat",
          city: "Ahmedabad",
          complexion: "fair",
          bodyType: "average",
          aboutMe:
            "Family business owner with traditional values. Love cricket and traveling. Looking for a homely and caring life partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "rich",
          familyValues: "traditional",
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 2,
          marriedSisters: 2,
          fatherOccupation: "Business",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Commerce",
          occupation: "Business Owner",
          designation: "Managing Director",
          companyType: "business",
          workExperienceYears: 12,
          annualIncomeRange: "above_50_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Patel",
          manglikStatus: "anshik",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      },

      // User 4 - Teacher from Bangalore (Female, Free Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Kavya Reddy",
        phone: "+************",
        dateOfBirth: "1994-06-25",
        gender: "female",
        membershipType: "free",
        personalInfo: {
          firstName: "Kavya",
          lastName: "Reddy",
          age: 29,
          height: 158,
          weight: 52,
          maritalStatus: "never_married",
          motherTongue: "telugu",
          country: "India",
          state: "Karnataka",
          city: "Bangalore",
          complexion: "wheatish",
          bodyType: "slim",
          aboutMe:
            "School teacher who loves children and education. Enjoy cooking and classical dance. Seeking a supportive and understanding partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 0,
          fatherOccupation: "Government Employee",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Education",
          occupation: "Teacher",
          designation: "Senior Teacher",
          companyType: "government",
          workExperienceYears: 5,
          annualIncomeRange: "5_7_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Reddy",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 165,
          heightMaxCm: 180,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },

      // User 5 - CA from Pune (Male, Gold Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Vikram Joshi",
        phone: "+************",
        dateOfBirth: "1990-11-08",
        gender: "male",
        membershipType: "gold",
        personalInfo: {
          firstName: "Vikram",
          lastName: "Joshi",
          age: 33,
          height: 175,
          weight: 70,
          maritalStatus: "never_married",
          motherTongue: "marathi",
          country: "India",
          state: "Maharashtra",
          city: "Pune",
          complexion: "fair",
          bodyType: "average",
          aboutMe:
            "Chartered Accountant with own practice. Love music and photography. Looking for an educated and independent partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "moderate",
          totalBrothers: 0,
          marriedBrothers: 0,
          totalSisters: 2,
          marriedSisters: 1,
          fatherOccupation: "Retired Bank Manager",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "professional",
          educationField: "Chartered Accountancy",
          occupation: "Chartered Accountant",
          designation: "Partner",
          companyType: "self_employed",
          workExperienceYears: 10,
          annualIncomeRange: "15_20_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "socially",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Joshi",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 168,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters", "professional"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
      },

      // User 6 - Data Scientist from Hyderabad (Female, Gold Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Ananya Singh",
        phone: "+************",
        dateOfBirth: "1995-04-12",
        gender: "female",
        membershipType: "gold",
        personalInfo: {
          firstName: "Ananya",
          lastName: "Singh",
          age: 28,
          height: 165,
          weight: 58,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Telangana",
          city: "Hyderabad",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Data scientist passionate about AI and machine learning. Love hiking and reading. Looking for an intellectually compatible partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "liberal",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Engineer",
          motherOccupation: "Doctor",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Data Science",
          occupation: "Data Scientist",
          designation: "Senior Data Scientist",
          companyType: "private",
          workExperienceYears: 5,
          annualIncomeRange: "12_15_lakhs",
        },
        lifestyle: {
          diet: "non_vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Singh",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["masters", "doctorate"],
          preferredDiet: ["vegetarian", "non_vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },

      // User 7 - Lawyer from Chennai (Male, Platinum Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Karthik Kumar",
        phone: "+************",
        dateOfBirth: "1989-09-18",
        gender: "male",
        membershipType: "platinum",
        personalInfo: {
          firstName: "Karthik",
          lastName: "Kumar",
          age: 34,
          height: 182,
          weight: 78,
          maritalStatus: "never_married",
          motherTongue: "tamil",
          country: "India",
          state: "Tamil Nadu",
          city: "Chennai",
          complexion: "wheatish",
          bodyType: "athletic",
          aboutMe:
            "Corporate lawyer with strong family values. Enjoy classical music and tennis. Seeking a well-educated and cultured partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "rich",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 2,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Retired Judge",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Law",
          occupation: "Lawyer",
          designation: "Senior Partner",
          companyType: "private",
          workExperienceYears: 11,
          annualIncomeRange: "30_50_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "socially",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Kumar",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },

      // User 8 - Nurse from Kochi (Female, Silver Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Meera Nair",
        phone: "+************",
        dateOfBirth: "1996-01-30",
        gender: "female",
        membershipType: "silver",
        personalInfo: {
          firstName: "Meera",
          lastName: "Nair",
          age: 27,
          height: 160,
          weight: 54,
          maritalStatus: "never_married",
          motherTongue: "malayalam",
          country: "India",
          state: "Kerala",
          city: "Kochi",
          complexion: "wheatish",
          bodyType: "slim",
          aboutMe:
            "Dedicated nurse who loves helping people. Enjoy cooking traditional Kerala dishes and gardening. Looking for a kind and caring partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Government Employee",
          motherOccupation: "Teacher",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Nursing",
          occupation: "Nurse",
          designation: "Staff Nurse",
          companyType: "government",
          workExperienceYears: 4,
          annualIncomeRange: "3_5_lakhs",
        },
        lifestyle: {
          diet: "non_vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Nair",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 27,
          ageMax: 35,
          heightMinCm: 165,
          heightMaxCm: 180,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian", "non_vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      },

      // User 9 - Bank Manager from Jaipur (Male, Free Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Amit Agarwal",
        phone: "+************",
        dateOfBirth: "1987-07-14",
        gender: "male",
        membershipType: "free",
        personalInfo: {
          firstName: "Amit",
          lastName: "Agarwal",
          age: 36,
          height: 173,
          weight: 74,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Rajasthan",
          city: "Jaipur",
          complexion: "fair",
          bodyType: "average",
          aboutMe:
            "Bank manager with stable career. Love traveling and photography. Looking for a simple and caring life partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "middle_class",
          familyValues: "traditional",
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Retired Teacher",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Finance",
          occupation: "Bank Manager",
          designation: "Assistant General Manager",
          companyType: "government",
          workExperienceYears: 12,
          annualIncomeRange: "10_15_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Agarwal",
          manglikStatus: "yes",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 168,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },

      // User 10 - Pharmacist from Indore (Female, Gold Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Sneha Malhotra",
        phone: "+************",
        dateOfBirth: "1994-12-03",
        gender: "female",
        membershipType: "gold",
        personalInfo: {
          firstName: "Sneha",
          lastName: "Malhotra",
          age: 29,
          height: 163,
          weight: 56,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Madhya Pradesh",
          city: "Indore",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Pharmacist with own medical store. Love reading and yoga. Seeking an understanding and supportive partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Doctor",
          motherOccupation: "Pharmacist",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Pharmacy",
          occupation: "Pharmacist",
          designation: "Owner",
          companyType: "business",
          workExperienceYears: 6,
          annualIncomeRange: "8_10_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Malhotra",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 168,
          heightMaxCm: 180,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
      },

      // User 11 - Civil Engineer from Lucknow (Male, Silver Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Rajesh Verma",
        phone: "+************",
        dateOfBirth: "1991-02-28",
        gender: "male",
        membershipType: "silver",
        personalInfo: {
          firstName: "Rajesh",
          lastName: "Verma",
          age: 32,
          height: 176,
          weight: 71,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Uttar Pradesh",
          city: "Lucknow",
          complexion: "wheatish",
          bodyType: "average",
          aboutMe:
            "Civil engineer working on infrastructure projects. Love cricket and music. Looking for a traditional and family-oriented partner.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "middle_class",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Government Engineer",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Civil Engineering",
          occupation: "Civil Engineer",
          designation: "Project Manager",
          companyType: "government",
          workExperienceYears: 9,
          annualIncomeRange: "7_10_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Verma",
          manglikStatus: "anshik",
        },
        partnerPreferences: {
          ageMin: 24,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 165,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      },

      // User 12 - Fashion Designer from Kolkata (Female, Platinum Member)
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Riya Das",
        phone: "+************",
        dateOfBirth: "1995-10-16",
        gender: "female",
        membershipType: "platinum",
        personalInfo: {
          firstName: "Riya",
          lastName: "Das",
          age: 28,
          height: 167,
          weight: 59,
          maritalStatus: "never_married",
          motherTongue: "bengali",
          country: "India",
          state: "West Bengal",
          city: "Kolkata",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Creative fashion designer with own boutique. Love art, culture and traveling. Seeking a creative and understanding partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "liberal",
          totalBrothers: 0,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 0,
          fatherOccupation: "Artist",
          motherOccupation: "Fashion Designer",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Fashion Design",
          occupation: "Fashion Designer",
          designation: "Creative Director",
          companyType: "business",
          workExperienceYears: 6,
          annualIncomeRange: "12_15_lakhs",
        },
        lifestyle: {
          diet: "non_vegetarian",
          smoking: "never",
          drinking: "socially",
          fitnessLevel: "moderate",
        },
        religiousInfo: { religion: "hindu", caste: "Das", manglikStatus: "no" },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian", "non_vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },

      // Users 13-20: Completing the 20 user dataset
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Deepak Singh",
        phone: "+************",
        dateOfBirth: "1993-05-22",
        gender: "male",
        membershipType: "free",
        personalInfo: {
          firstName: "Deepak",
          lastName: "Singh",
          age: 30,
          height: 174,
          weight: 69,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Bihar",
          city: "Patna",
          complexion: "wheatish",
          bodyType: "average",
          aboutMe:
            "Government officer with stable job. Love reading and social service.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "middle_class",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Farmer",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Public Administration",
          occupation: "Government Officer",
          designation: "Deputy Collector",
          companyType: "government",
          workExperienceYears: 7,
          annualIncomeRange: "8_10_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Singh",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 24,
          ageMax: 28,
          heightMinCm: 155,
          heightMaxCm: 165,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Pooja Sharma",
        phone: "+************",
        dateOfBirth: "1996-08-11",
        gender: "female",
        membershipType: "silver",
        personalInfo: {
          firstName: "Pooja",
          lastName: "Sharma",
          age: 27,
          height: 159,
          weight: 53,
          maritalStatus: "never_married",
          motherTongue: "punjabi",
          country: "India",
          state: "Punjab",
          city: "Chandigarh",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "HR professional who loves music and dance. Looking for a caring and understanding partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "upper_middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Business",
          motherOccupation: "Teacher",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Human Resources",
          occupation: "HR Manager",
          designation: "Senior HR Manager",
          companyType: "private",
          workExperienceYears: 5,
          annualIncomeRange: "10_12_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "sikh",
          caste: "Sharma",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 27,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["sikh", "hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Suresh Kumar",
        phone: "+************",
        dateOfBirth: "1985-11-07",
        gender: "male",
        membershipType: "gold",
        personalInfo: {
          firstName: "Suresh",
          lastName: "Kumar",
          age: 38,
          height: 177,
          weight: 76,
          maritalStatus: "divorced",
          motherTongue: "tamil",
          country: "India",
          state: "Tamil Nadu",
          city: "Coimbatore",
          complexion: "wheatish",
          bodyType: "average",
          aboutMe:
            "Experienced mechanical engineer and single father. Looking for a understanding life partner.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 2,
          marriedSisters: 2,
          fatherOccupation: "Retired Engineer",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Mechanical Engineering",
          occupation: "Mechanical Engineer",
          designation: "Chief Engineer",
          companyType: "private",
          workExperienceYears: 15,
          annualIncomeRange: "18_25_lakhs",
        },
        lifestyle: {
          diet: "non_vegetarian",
          smoking: "never",
          drinking: "occasionally",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Kumar",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian", "non_vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Nisha Agarwal",
        phone: "+************",
        dateOfBirth: "1997-03-19",
        gender: "female",
        membershipType: "free",
        personalInfo: {
          firstName: "Nisha",
          lastName: "Agarwal",
          age: 26,
          height: 161,
          weight: 51,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Haryana",
          city: "Gurgaon",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Marketing executive with creative mindset. Love traveling and photography.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "moderate",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 0,
          fatherOccupation: "Manager",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Marketing",
          occupation: "Marketing Executive",
          designation: "Senior Executive",
          companyType: "private",
          workExperienceYears: 3,
          annualIncomeRange: "5_7_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "socially",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Agarwal",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 26,
          ageMax: 32,
          heightMinCm: 170,
          heightMaxCm: 180,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Manish Gupta",
        phone: "+************",
        dateOfBirth: "1990-09-25",
        gender: "male",
        membershipType: "platinum",
        personalInfo: {
          firstName: "Manish",
          lastName: "Gupta",
          age: 33,
          height: 179,
          weight: 73,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Uttar Pradesh",
          city: "Noida",
          complexion: "fair",
          bodyType: "athletic",
          aboutMe:
            "IT consultant and fitness enthusiast. Love technology and outdoor activities.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "rich",
          familyValues: "liberal",
          totalBrothers: 0,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Business",
          motherOccupation: "Doctor",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Information Technology",
          occupation: "IT Consultant",
          designation: "Senior Consultant",
          companyType: "private",
          workExperienceYears: 10,
          annualIncomeRange: "25_30_lakhs",
        },
        lifestyle: {
          diet: "non_vegetarian",
          smoking: "never",
          drinking: "socially",
          fitnessLevel: "high",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Gupta",
          manglikStatus: "no",
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 160,
          heightMaxCm: 170,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian", "non_vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Divya Reddy",
        phone: "+************",
        dateOfBirth: "1994-01-14",
        gender: "female",
        membershipType: "gold",
        personalInfo: {
          firstName: "Divya",
          lastName: "Reddy",
          age: 29,
          height: 164,
          weight: 57,
          maritalStatus: "never_married",
          motherTongue: "telugu",
          country: "India",
          state: "Andhra Pradesh",
          city: "Visakhapatnam",
          complexion: "wheatish",
          bodyType: "average",
          aboutMe:
            "Biotechnology researcher passionate about science. Love classical music and cooking.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "upper_middle_class",
          familyValues: "traditional",
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: "Professor",
          motherOccupation: "Scientist",
        },
        educationCareer: {
          highestEducation: "doctorate",
          educationField: "Biotechnology",
          occupation: "Research Scientist",
          designation: "Senior Scientist",
          companyType: "government",
          workExperienceYears: 6,
          annualIncomeRange: "12_15_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Reddy",
          manglikStatus: "anshik",
        },
        partnerPreferences: {
          ageMin: 29,
          ageMax: 36,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["masters", "doctorate"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Rohit Jain",
        phone: "+************",
        dateOfBirth: "1992-06-08",
        gender: "male",
        membershipType: "silver",
        personalInfo: {
          firstName: "Rohit",
          lastName: "Jain",
          age: 31,
          height: 172,
          weight: 68,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Rajasthan",
          city: "Jodhpur",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Textile business owner from traditional family. Love music and family gatherings.",
        },
        familyInfo: {
          familyType: "joint",
          familyStatus: "rich",
          familyValues: "traditional",
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Business",
          motherOccupation: "Homemaker",
        },
        educationCareer: {
          highestEducation: "bachelors",
          educationField: "Commerce",
          occupation: "Business Owner",
          designation: "Director",
          companyType: "business",
          workExperienceYears: 8,
          annualIncomeRange: "20_30_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: { religion: "jain", caste: "Jain", manglikStatus: "no" },
        partnerPreferences: {
          ageMin: 24,
          ageMax: 29,
          heightMinCm: 155,
          heightMaxCm: 165,
          preferredReligions: ["jain", "hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      },
      {
        email: "<EMAIL>",
        password: "password123",
        name: "Swati Mishra",
        phone: "+************",
        dateOfBirth: "1995-12-02",
        gender: "female",
        membershipType: "free",
        personalInfo: {
          firstName: "Swati",
          lastName: "Mishra",
          age: 28,
          height: 157,
          weight: 50,
          maritalStatus: "never_married",
          motherTongue: "hindi",
          country: "India",
          state: "Madhya Pradesh",
          city: "Bhopal",
          complexion: "fair",
          bodyType: "slim",
          aboutMe:
            "Government school teacher dedicated to education. Love reading and social work.",
        },
        familyInfo: {
          familyType: "nuclear",
          familyStatus: "middle_class",
          familyValues: "traditional",
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: "Government Employee",
          motherOccupation: "Teacher",
        },
        educationCareer: {
          highestEducation: "masters",
          educationField: "Education",
          occupation: "Teacher",
          designation: "Head Teacher",
          companyType: "government",
          workExperienceYears: 5,
          annualIncomeRange: "4_6_lakhs",
        },
        lifestyle: {
          diet: "vegetarian",
          smoking: "never",
          drinking: "never",
          fitnessLevel: "moderate",
        },
        religiousInfo: {
          religion: "hindu",
          caste: "Mishra",
          manglikStatus: "yes",
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 165,
          heightMaxCm: 180,
          preferredReligions: ["hindu"],
          preferredEducationLevels: ["bachelors", "masters"],
          preferredDiet: ["vegetarian"],
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
      },
    ];

    // Create users in database
    for (const userData of sampleUsers) {
      try {
        const user = await User.create(userData);
        user.calculateProfileCompletion();
        await user.save();
        console.log(`✅ Created user: ${user.email} (${user.membershipType})`);
      } catch (error) {
        console.error(
          `❌ Failed to create user ${userData.email}:`,
          error.message
        );
      }
    }

    console.log(
      `✅ Successfully created ${sampleUsers.length} users with diverse profiles`
    );
    return sampleUsers;
  } catch (error) {
    console.error("❌ Failed to create seed data:", error);
    throw error;
  }
};

// Run if called directly
if (require.main === module) {
  const { sequelize } = require("../config/database");

  create20Users()
    .then(() => {
      console.log("🎉 20 users seeded successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 Seeding failed:", error);
      process.exit(1);
    });
}

module.exports = { create20Users };
