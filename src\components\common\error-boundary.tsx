import React from 'react';
import { <PERSON>, Card<PERSON>ody, But<PERSON>, Alert } from '@heroui/react';
import { Icon } from '@iconify/react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />;
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.handleRetry} />;
    }

    return this.props.children;
  }
}

interface DefaultErrorFallbackProps {
  error: Error;
  retry: () => void;
}

const DefaultErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, retry }) => {
  const isAPIError = error.message.includes('API') || error.message.includes('fetch');
  const isMissingFeature = error.message.includes('export') || error.message.includes('module');

  return (
    <div className="flex justify-center items-center min-h-[400px] p-4">
      <Card className="max-w-md w-full">
        <CardBody className="text-center space-y-4">
          <div className="w-16 h-16 bg-danger-100 rounded-full flex items-center justify-center mx-auto">
            <Icon 
              icon={isMissingFeature ? "lucide:code" : isAPIError ? "lucide:wifi-off" : "lucide:alert-triangle"} 
              size={32} 
              className="text-danger-600" 
            />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">
              {isMissingFeature ? 'Feature Under Development' : 
               isAPIError ? 'Connection Error' : 'Something went wrong'}
            </h3>
            
            <p className="text-sm text-default-600 mb-4">
              {isMissingFeature ? 
                'This feature is currently being developed and will be available soon.' :
                isAPIError ?
                'Unable to connect to the server. Please check your internet connection.' :
                'An unexpected error occurred. Please try again.'}
            </p>
            
            {process.env.NODE_ENV === 'development' && (
              <Alert color="warning" variant="flat" className="text-left mb-4">
                <details>
                  <summary className="cursor-pointer font-medium">Error Details (Development)</summary>
                  <pre className="text-xs mt-2 whitespace-pre-wrap break-words">
                    {error.message}
                  </pre>
                </details>
              </Alert>
            )}
          </div>
          
          <div className="flex gap-2 justify-center">
            <Button 
              color="primary" 
              variant="flat"
              onPress={retry}
              startContent={<Icon icon="lucide:refresh-cw" />}
            >
              Try Again
            </Button>
            
            <Button 
              variant="light"
              onPress={() => window.location.href = '/dashboard'}
              startContent={<Icon icon="lucide:home" />}
            >
              Go Home
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};

// Hook for handling async errors
export const useAsyncError = () => {
  const [, setError] = React.useState();
  return React.useCallback(
    (error: Error) => {
      setError(() => {
        throw error;
      });
    },
    [setError]
  );
};

// Higher-order component for wrapping components with error boundary
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Specific error fallback for API-related errors
export const APIErrorFallback: React.FC<DefaultErrorFallbackProps> = ({ error, retry }) => (
  <div className="flex justify-center items-center min-h-[200px] p-4">
    <div className="text-center space-y-4">
      <div className="w-12 h-12 bg-warning-100 rounded-full flex items-center justify-center mx-auto">
        <Icon icon="lucide:server-off" size={24} className="text-warning-600" />
      </div>
      
      <div>
        <h3 className="font-semibold mb-1">Service Temporarily Unavailable</h3>
        <p className="text-sm text-default-600 mb-3">
          This feature is currently being set up. Please try again later.
        </p>
      </div>
      
      <Button 
        size="sm" 
        variant="flat" 
        onPress={retry}
        startContent={<Icon icon="lucide:refresh-cw" />}
      >
        Retry
      </Button>
    </div>
  </div>
);

// Feature not available fallback
export const FeatureUnavailableFallback: React.FC<{ featureName: string; retry?: () => void }> = ({ 
  featureName, 
  retry 
}) => (
  <div className="flex justify-center items-center min-h-[300px] p-4">
    <Card className="max-w-sm w-full">
      <CardBody className="text-center space-y-4">
        <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto">
          <Icon icon="lucide:construction" size={32} className="text-primary-600" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold mb-2">{featureName} Coming Soon</h3>
          <p className="text-sm text-default-600 mb-4">
            We're working hard to bring you this feature. It will be available in the next update.
          </p>
        </div>
        
        <div className="flex gap-2 justify-center">
          {retry && (
            <Button 
              color="primary" 
              variant="flat"
              onPress={retry}
              startContent={<Icon icon="lucide:refresh-cw" />}
            >
              Check Again
            </Button>
          )}
          
          <Button 
            variant="light"
            onPress={() => window.location.href = '/dashboard'}
            startContent={<Icon icon="lucide:arrow-left" />}
          >
            Go Back
          </Button>
        </div>
      </CardBody>
    </Card>
  </div>
);

export default ErrorBoundary;
