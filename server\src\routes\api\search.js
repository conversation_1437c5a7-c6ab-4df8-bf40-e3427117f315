const express = require('express');
const router = express.Router();
const { Op } = require('sequelize');
const { authenticateToken } = require('../../middleware/auth');
const { rateLimits } = require('../../middleware/rateLimiting');
const User = require('../../models/sequelize/User');
const { query, validationResult } = require('express-validator');

// Advanced search with filters
router.get('/', authenticateToken, rateLimits.search, [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  query('ageMin').optional().isInt({ min: 18, max: 100 }).toInt(),
  query('ageMax').optional().isInt({ min: 18, max: 100 }).toInt(),
  query('heightMin').optional().isInt({ min: 100, max: 250 }).toInt(),
  query('heightMax').optional().isInt({ min: 100, max: 250 }).toInt(),
  query('maritalStatus').optional().isIn(['never_married', 'divorced', 'widowed', 'separated', 'awaiting_divorce']),
  query('religion').optional().isIn(['hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain', 'parsi', 'other']),
  query('motherTongue').optional().isLength({ min: 2, max: 50 }),
  query('education').optional().isIn(['high_school', 'diploma', 'bachelors', 'masters', 'doctorate', 'professional']),
  query('diet').optional().isIn(['vegetarian', 'non_vegetarian', 'vegan', 'jain_food', 'occasionally_non_veg']),
  query('smoking').optional().isIn(['never', 'occasionally', 'regularly', 'trying_to_quit']),
  query('drinking').optional().isIn(['never', 'occasionally', 'socially', 'regularly']),
  query('manglik').optional().isIn(['yes', 'no', 'anshik', 'dont_know']),
  query('withPhoto').optional().isBoolean().toBoolean(),
  query('verified').optional().isBoolean().toBoolean(),
  query('sortBy').optional().isIn(['compatibility', 'lastActive', 'newest', 'profileCompletion'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      ageMin,
      ageMax,
      heightMin,
      heightMax,
      maritalStatus,
      religion,
      caste,
      subCaste,
      motherTongue,
      education,
      occupation,
      incomeMin,
      incomeMax,
      country,
      state,
      city,
      diet,
      smoking,
      drinking,
      manglik,
      withPhoto,
      verified,
      sortBy = 'lastActive'
    } = req.query;

    const currentUserId = req.user.id;
    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = {
      id: { [Op.ne]: currentUserId },
      isActive: true,
      isBlocked: false,
      profileCompletionPercentage: { [Op.gte]: 50 }
    };

    // Personal details filters
    const personalInfoConditions = {};
    if (ageMin || ageMax) {
      personalInfoConditions.age = {};
      if (ageMin) personalInfoConditions.age[Op.gte] = ageMin;
      if (ageMax) personalInfoConditions.age[Op.lte] = ageMax;
    }
    if (heightMin || heightMax) {
      personalInfoConditions.height = {};
      if (heightMin) personalInfoConditions.height[Op.gte] = heightMin;
      if (heightMax) personalInfoConditions.height[Op.lte] = heightMax;
    }
    if (maritalStatus) personalInfoConditions.maritalStatus = maritalStatus;
    if (motherTongue) personalInfoConditions.motherTongue = motherTongue;
    if (country) personalInfoConditions.country = country;
    if (state) personalInfoConditions.state = state;
    if (city) personalInfoConditions.city = { [Op.iLike]: `%${city}%` };

    // Religious details filters
    const religiousInfoConditions = {};
    if (religion) religiousInfoConditions.religion = religion;
    if (caste) religiousInfoConditions.caste = { [Op.iLike]: `%${caste}%` };
    if (subCaste) religiousInfoConditions.subCaste = { [Op.iLike]: `%${subCaste}%` };
    if (manglik) religiousInfoConditions.manglikStatus = manglik;

    // Education/Career filters
    const educationCareerConditions = {};
    if (education) educationCareerConditions.highestEducation = education;
    if (occupation) educationCareerConditions.occupation = { [Op.iLike]: `%${occupation}%` };
    if (incomeMin || incomeMax) {
      // This would need proper income range handling
      educationCareerConditions.annualIncomeRange = { [Op.ne]: null };
    }

    // Lifestyle filters
    const lifestyleConditions = {};
    if (diet) lifestyleConditions.diet = diet;
    if (smoking) lifestyleConditions.smoking = smoking;
    if (drinking) lifestyleConditions.drinking = drinking;

    // Photo filter
    if (withPhoto) {
      whereConditions.profilePicture = { [Op.ne]: null };
    }

    // Verification filter
    if (verified) {
      whereConditions.emailVerifiedAt = { [Op.ne]: null };
      whereConditions.phoneVerifiedAt = { [Op.ne]: null };
    }

    // Apply JSON field conditions (simplified for SQLite)
    if (Object.keys(personalInfoConditions).length > 0) {
      // For SQLite, we'll need to use JSON_EXTRACT or similar
      // This is a simplified version - in production, you'd want proper JSON querying
      Object.keys(personalInfoConditions).forEach(key => {
        whereConditions[`personalInfo.${key}`] = personalInfoConditions[key];
      });
    }

    // Find users
    const { count, rows: users } = await User.findAndCountAll({
      where: whereConditions,
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
      limit: parseInt(limit),
      offset: offset,
      order: [
        sortBy === 'lastActive' ? ['lastActive', 'DESC'] :
        sortBy === 'newest' ? ['createdAt', 'DESC'] :
        sortBy === 'profileCompletion' ? ['profileCompletionPercentage', 'DESC'] :
        ['lastActive', 'DESC']
      ]
    });

    // Format profiles for response
    const profiles = users.map(user => {
      const personal = user.personalInfo || {};
      const religious = user.religiousInfo || {};
      const professional = user.educationCareer || {};
      const photos = user.profilePhotos || [];
      const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

      return {
        id: user.id,
        name: personal.firstName && personal.lastName ? 
          `${personal.firstName} ${personal.lastName}` : 'Unknown',
        age: personal.age,
        height: personal.height,
        location: personal.city && personal.state ? 
          `${personal.city}, ${personal.state}` : '',
        occupation: professional.occupation,
        education: professional.highestEducation,
        religion: religious.religion,
        caste: religious.caste,
        maritalStatus: personal.maritalStatus,
        profilePicture: profilePicture?.filePath,
        membershipType: user.membershipType,
        isVerified: user.emailVerifiedAt && user.phoneVerifiedAt,
        lastActive: user.lastActive,
        profileCompletionPercentage: user.profileCompletionPercentage,
        compatibilityScore: null // Would be calculated based on user preferences
      };
    });

    // Calculate pagination info
    const totalPages = Math.ceil(count / limit);
    const pagination = {
      currentPage: parseInt(page),
      totalPages,
      totalResults: count,
      hasNext: page < totalPages,
      hasPrev: page > 1
    };

    res.json({
      success: true,
      data: {
        profiles,
        pagination
      }
    });

  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed'
    });
  }
});

// Get recommendations based on user preferences
router.get('/recommendations', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 20 }).toInt()
], async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const currentUserId = req.user.id;

    // Get current user to access their preferences
    const currentUser = await User.findByPk(currentUserId);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const preferences = currentUser.partnerPreferences || {};
    
    // Build search criteria based on preferences
    const whereConditions = {
      id: { [Op.ne]: currentUserId },
      isActive: true,
      isBlocked: false,
      profileCompletionPercentage: { [Op.gte]: 70 }
    };

    // Apply preference filters
    if (preferences.ageMin && preferences.ageMax) {
      // This would need proper JSON querying for personalInfo.age
      whereConditions.personalInfo = {
        age: {
          [Op.between]: [preferences.ageMin, preferences.ageMax]
        }
      };
    }

    // Find recommended users
    const users = await User.findAll({
      where: whereConditions,
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
      limit: parseInt(limit),
      order: [
        ['profileCompletionPercentage', 'DESC'],
        ['lastActive', 'DESC']
      ]
    });

    // Format profiles
    const profiles = users.map(user => {
      const personal = user.personalInfo || {};
      const religious = user.religiousInfo || {};
      const professional = user.educationCareer || {};
      const photos = user.profilePhotos || [];
      const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

      // Calculate basic compatibility score
      let compatibilityScore = 50; // Base score
      
      if (preferences.preferredReligions && religious.religion) {
        if (preferences.preferredReligions.includes(religious.religion)) {
          compatibilityScore += 20;
        }
      }
      
      if (preferences.preferredEducationLevels && professional.highestEducation) {
        if (preferences.preferredEducationLevels.includes(professional.highestEducation)) {
          compatibilityScore += 15;
        }
      }

      return {
        id: user.id,
        name: personal.firstName && personal.lastName ? 
          `${personal.firstName} ${personal.lastName}` : 'Unknown',
        age: personal.age,
        height: personal.height,
        location: personal.city && personal.state ? 
          `${personal.city}, ${personal.state}` : '',
        occupation: professional.occupation,
        education: professional.highestEducation,
        religion: religious.religion,
        caste: religious.caste,
        maritalStatus: personal.maritalStatus,
        profilePicture: profilePicture?.filePath,
        membershipType: user.membershipType,
        isVerified: user.emailVerifiedAt && user.phoneVerifiedAt,
        lastActive: user.lastActive,
        profileCompletionPercentage: user.profileCompletionPercentage,
        compatibilityScore: Math.min(compatibilityScore, 100)
      };
    });

    res.json({
      success: true,
      data: { profiles }
    });

  } catch (error) {
    console.error('Recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations'
    });
  }
});

// Get trending profiles
router.get('/trending', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 20 }).toInt()
], async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const currentUserId = req.user.id;

    // Get trending profiles (most viewed, recently active, high completion)
    const users = await User.findAll({
      where: {
        id: { [Op.ne]: currentUserId },
        isActive: true,
        isBlocked: false,
        profileCompletionPercentage: { [Op.gte]: 80 }
      },
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] },
      limit: parseInt(limit),
      order: [
        ['profileViews', 'DESC'],
        ['lastActive', 'DESC'],
        ['profileCompletionPercentage', 'DESC']
      ]
    });

    // Format profiles
    const profiles = users.map(user => {
      const personal = user.personalInfo || {};
      const religious = user.religiousInfo || {};
      const professional = user.educationCareer || {};
      const photos = user.profilePhotos || [];
      const profilePicture = photos.find(p => p.isProfilePicture) || photos[0];

      return {
        id: user.id,
        name: personal.firstName && personal.lastName ? 
          `${personal.firstName} ${personal.lastName}` : 'Unknown',
        age: personal.age,
        height: personal.height,
        location: personal.city && personal.state ? 
          `${personal.city}, ${personal.state}` : '',
        occupation: professional.occupation,
        education: professional.highestEducation,
        religion: religious.religion,
        caste: religious.caste,
        maritalStatus: personal.maritalStatus,
        profilePicture: profilePicture?.filePath,
        membershipType: user.membershipType,
        isVerified: user.emailVerifiedAt && user.phoneVerifiedAt,
        lastActive: user.lastActive,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileViews: user.profileViews
      };
    });

    res.json({
      success: true,
      data: { profiles }
    });

  } catch (error) {
    console.error('Trending profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending profiles'
    });
  }
});

// Get profile by ID
router.get('/profile/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const currentUserId = req.user.id;

    if (id === currentUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot view your own profile through this endpoint'
      });
    }

    const user = await User.findOne({
      where: {
        id,
        isActive: true,
        isBlocked: false
      },
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    // Increment profile views
    await user.increment('profileViews');

    // Format profile data
    const personal = user.personalInfo || {};
    const family = user.familyInfo || {};
    const education = user.educationCareer || {};
    const lifestyle = user.lifestyle || {};
    const religious = user.religiousInfo || {};
    const photos = user.profilePhotos || [];

    const profile = {
      id: user.id,
      personalDetails: personal,
      familyDetails: family,
      educationDetails: education,
      lifestyleDetails: lifestyle,
      religiousDetails: religious,
      photos: photos,
      membershipType: user.membershipType,
      isVerified: user.emailVerifiedAt && user.phoneVerifiedAt,
      lastActive: user.lastActive,
      profileCompletionPercentage: user.profileCompletionPercentage,
      profileViews: user.profileViews + 1
    };

    res.json({
      success: true,
      data: { profile }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
});

module.exports = router;
