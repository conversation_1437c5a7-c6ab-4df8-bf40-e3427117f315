import React from "react";
import { Link, useLocation, useHistory } from "react-router-dom";
import {
  Navbar as HeroNavbar,
  NavbarBrand,
  NavbarContent,
  NavbarItem,
  Button,
  NavbarMenuToggle,
  NavbarMenu,
  NavbarMenuItem,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  Avatar,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAuth } from "../contexts/auth-context";

export const Navbar: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();
  const history = useHistory();

  const handleLogout = () => {
    logout();
    history.push("/");
  };

  return (
    <HeroNavbar
      isBordered
      isMenuOpen={isMenuOpen}
      onMenuOpenChange={setIsMenuOpen}
      className="bg-white"
    >
      <NavbarContent>
        <NavbarMenuToggle
          aria-label={isMenuOpen ? "Close menu" : "Open menu"}
          className="sm:hidden"
        />
        <NavbarBrand>
          <Link to="/" className="flex items-center gap-2">
            <Icon icon="lucide:heart" className="text-primary text-2xl" />
            <p className="font-bold text-xl text-inherit">BharatMatrimony</p>
          </Link>
        </NavbarBrand>
      </NavbarContent>

      <NavbarContent className="hidden sm:flex gap-4" justify="center">
        <NavbarItem isActive={location.pathname === "/"}>
          <Link
            to="/"
            className={`text-sm ${
              location.pathname === "/"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Home
          </Link>
        </NavbarItem>
        <NavbarItem isActive={location.pathname === "/search"}>
          <Link
            to="/search"
            className={`text-sm ${
              location.pathname === "/search"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Search
          </Link>
        </NavbarItem>
        <NavbarItem isActive={location.pathname === "/matches"}>
          <Link
            to="/matches"
            className={`text-sm ${
              location.pathname === "/matches"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Matches
          </Link>
        </NavbarItem>
        <NavbarItem isActive={location.pathname === "/interests"}>
          <Link
            to="/interests"
            className={`text-sm ${
              location.pathname === "/interests"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Interests
          </Link>
        </NavbarItem>
        <NavbarItem isActive={location.pathname === "/success-stories"}>
          <Link
            to="/success-stories"
            className={`text-sm ${
              location.pathname === "/success-stories"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Success Stories
          </Link>
        </NavbarItem>
        <NavbarItem isActive={location.pathname === "/help"}>
          <Link
            to="/help"
            className={`text-sm ${
              location.pathname === "/help"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Help
          </Link>
        </NavbarItem>
      </NavbarContent>

      <NavbarContent justify="end">
        {isAuthenticated ? (
          <Dropdown placement="bottom-end">
            <DropdownTrigger>
              <Avatar
                isBordered
                as="button"
                className="transition-transform"
                color="primary"
                name={user?.name || "User"}
                size="sm"
                src={
                  user?.profilePicture ||
                  "https://img.heroui.chat/image/avatar?w=200&h=200&u=1"
                }
              />
            </DropdownTrigger>
            <DropdownMenu aria-label="Profile Actions" variant="flat">
              <DropdownItem key="profile" className="h-14 gap-2">
                <p className="font-semibold">Signed in as</p>
                <p className="font-semibold">{user?.email}</p>
              </DropdownItem>
              <DropdownItem key="dashboard" as={Link} to="/dashboard">
                Dashboard
              </DropdownItem>
              <DropdownItem key="messages" as={Link} to="/messages">
                Messages
              </DropdownItem>
              <DropdownItem key="interests" as={Link} to="/interests">
                Interests
              </DropdownItem>
              <DropdownItem key="profile-edit" as={Link} to="/profile-edit">
                Edit Profile
              </DropdownItem>
              <DropdownItem key="subscription" as={Link} to="/subscription">
                Subscription
              </DropdownItem>
              <DropdownItem key="settings" as={Link} to="/settings">
                Settings
              </DropdownItem>
              <DropdownItem key="logout" color="danger" onClick={handleLogout}>
                Log Out
              </DropdownItem>
            </DropdownMenu>
          </Dropdown>
        ) : (
          <div className="flex gap-3">
            <NavbarItem className="hidden sm:flex">
              <Link to="/login">
                <Button color="default" variant="flat" size="sm">
                  Login
                </Button>
              </Link>
            </NavbarItem>
            <NavbarItem>
              <Link to="/register">
                <Button color="primary" variant="solid" size="sm">
                  Register Free
                </Button>
              </Link>
            </NavbarItem>
          </div>
        )}
      </NavbarContent>

      <NavbarMenu>
        <NavbarMenuItem>
          <Link
            to="/"
            className={`w-full ${
              location.pathname === "/"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Home
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link
            to="/search"
            className={`w-full ${
              location.pathname === "/search"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Search
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link
            to="/matches"
            className={`w-full ${
              location.pathname === "/matches"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Matches
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link
            to="/interests"
            className={`w-full ${
              location.pathname === "/interests"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Interests
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link
            to="/success-stories"
            className={`w-full ${
              location.pathname === "/success-stories"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Success Stories
          </Link>
        </NavbarMenuItem>
        <NavbarMenuItem>
          <Link
            to="/help"
            className={`w-full ${
              location.pathname === "/help"
                ? "text-primary font-medium"
                : "text-default-600"
            }`}
          >
            Help
          </Link>
        </NavbarMenuItem>
        {!isAuthenticated && (
          <NavbarMenuItem>
            <Link to="/login" className="w-full text-default-600">
              Login
            </Link>
          </NavbarMenuItem>
        )}
      </NavbarMenu>
    </HeroNavbar>
  );
};
