const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { authenticateToken, requireFeature } = require('../middleware/auth');

const router = express.Router();

// Horoscope data structure
const horoscopeSchema = {
  birthTime: String,
  birthPlace: String,
  kundliDetails: {
    rashi: String,
    nakshatra: String,
    charan: String,
    nadi: String,
    gan: String,
    yoni: String,
    gotra: String,
    manglik: String,
    kujDosha: Boolean,
    sarpaDosha: Boolean,
    kaalsarpaDosha: Boolean
  },
  planetPositions: [{
    planet: String,
    sign: String,
    degree: Number,
    house: Number
  }],
  houses: [{
    house: Number,
    sign: String,
    lord: String,
    planets: [String]
  }],
  doshas: [{
    name: String,
    present: Boolean,
    severity: String,
    remedies: [String]
  }],
  compatibility: {
    gunaMatching: {
      varna: Number,
      vashya: Number,
      tara: Number,
      yoni: Number,
      graha: Number,
      gana: Number,
      rashi: Number,
      nadi: Number,
      total: Number
    },
    manglikMatching: String,
    overallCompatibility: String
  }
};

// Update horoscope information
router.put('/update', [
  authenticateToken,
  requireFeature('horoscopeMatching'),
  body('birthTime').notEmpty().withMessage('Birth time is required'),
  body('birthPlace').notEmpty().withMessage('Birth place is required'),
  body('kundliDetails.rashi').optional().isLength({ min: 1 }).withMessage('Rashi is required'),
  body('kundliDetails.nakshatra').optional().isLength({ min: 1 }).withMessage('Nakshatra is required'),
  body('kundliDetails.manglik').optional().isIn(['yes', 'no', 'anshik']).withMessage('Invalid manglik status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const horoscopeData = req.body;

    // Update horoscope information
    user.horoscope = { ...user.horoscope, ...horoscopeData };
    
    // Update religious info with horoscope data
    if (horoscopeData.kundliDetails) {
      user.religiousInfo = {
        ...user.religiousInfo,
        star: horoscopeData.kundliDetails.nakshatra,
        rashi: horoscopeData.kundliDetails.rashi,
        manglik: horoscopeData.kundliDetails.manglik
      };
    }

    user.calculateProfileCompletion();
    await user.save();

    res.json({
      success: true,
      message: 'Horoscope information updated successfully',
      data: {
        horoscope: user.horoscope,
        religiousInfo: user.religiousInfo,
        profileCompletionPercentage: user.profileCompletionPercentage
      }
    });
  } catch (error) {
    console.error('Update horoscope error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update horoscope information'
    });
  }
});

// Get horoscope compatibility
router.post('/compatibility/:userId', [
  authenticateToken,
  requireFeature('horoscopeMatching')
], async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    // Get target user's horoscope
    const targetUser = await User.findById(userId).select('horoscope religiousInfo name');
    
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (!currentUser.horoscope || !targetUser.horoscope) {
      return res.status(400).json({
        success: false,
        message: 'Horoscope information not available for compatibility check'
      });
    }

    // Calculate Guna matching (Ashtakoot matching)
    const gunaMatching = calculateGunaMatching(currentUser.horoscope, targetUser.horoscope);
    
    // Check Manglik compatibility
    const manglikCompatibility = checkManglikCompatibility(
      currentUser.religiousInfo?.manglik,
      targetUser.religiousInfo?.manglik
    );

    // Calculate overall compatibility
    const overallCompatibility = calculateOverallCompatibility(gunaMatching, manglikCompatibility);

    const compatibilityResult = {
      users: {
        user1: {
          name: currentUser.name,
          rashi: currentUser.religiousInfo?.rashi,
          nakshatra: currentUser.horoscope?.kundliDetails?.nakshatra,
          manglik: currentUser.religiousInfo?.manglik
        },
        user2: {
          name: targetUser.name,
          rashi: targetUser.religiousInfo?.rashi,
          nakshatra: targetUser.horoscope?.kundliDetails?.nakshatra,
          manglik: targetUser.religiousInfo?.manglik
        }
      },
      gunaMatching,
      manglikCompatibility,
      overallCompatibility,
      recommendations: generateRecommendations(gunaMatching, manglikCompatibility),
      calculatedAt: new Date()
    };

    res.json({
      success: true,
      data: {
        compatibility: compatibilityResult
      }
    });
  } catch (error) {
    console.error('Horoscope compatibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate horoscope compatibility'
    });
  }
});

// Get horoscope matching suggestions
router.get('/suggestions', [
  authenticateToken,
  requireFeature('horoscopeMatching')
], async (req, res) => {
  try {
    const currentUser = req.user;

    if (!currentUser.horoscope || !currentUser.religiousInfo) {
      return res.status(400).json({
        success: false,
        message: 'Please complete your horoscope information first'
      });
    }

    // Find users with compatible horoscopes
    const compatibleUsers = await User.find({
      _id: { $ne: currentUser._id },
      isActive: true,
      profileCompleted: true,
      'horoscope.kundliDetails.rashi': { $exists: true },
      'religiousInfo.religion': currentUser.religiousInfo.religion
    })
    .select('name profilePicture horoscope religiousInfo personalInfo.age personalInfo.city')
    .limit(20);

    // Calculate compatibility for each user
    const suggestions = compatibleUsers.map(user => {
      const gunaMatching = calculateGunaMatching(currentUser.horoscope, user.horoscope);
      const manglikCompatibility = checkManglikCompatibility(
        currentUser.religiousInfo?.manglik,
        user.religiousInfo?.manglik
      );
      const overallCompatibility = calculateOverallCompatibility(gunaMatching, manglikCompatibility);

      return {
        user: {
          id: user._id,
          name: user.name,
          profilePicture: user.profilePicture,
          age: user.personalInfo?.age,
          city: user.personalInfo?.city,
          rashi: user.religiousInfo?.rashi,
          nakshatra: user.horoscope?.kundliDetails?.nakshatra
        },
        compatibility: {
          gunaScore: gunaMatching.total,
          manglikCompatible: manglikCompatibility.compatible,
          overallRating: overallCompatibility.rating,
          recommendation: overallCompatibility.recommendation
        }
      };
    })
    .filter(suggestion => suggestion.compatibility.gunaScore >= 18) // Minimum acceptable score
    .sort((a, b) => b.compatibility.gunaScore - a.compatibility.gunaScore);

    res.json({
      success: true,
      data: {
        suggestions,
        criteria: {
          minimumGunaScore: 18,
          religion: currentUser.religiousInfo.religion,
          totalFound: suggestions.length
        }
      }
    });
  } catch (error) {
    console.error('Horoscope suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get horoscope suggestions'
    });
  }
});

// Helper functions for horoscope calculations

function calculateGunaMatching(horoscope1, horoscope2) {
  // Simplified Guna matching calculation
  // In a real implementation, this would involve complex astrological calculations
  
  const gunas = {
    varna: Math.floor(Math.random() * 2), // 0-1 points
    vashya: Math.floor(Math.random() * 3), // 0-2 points
    tara: Math.floor(Math.random() * 4), // 0-3 points
    yoni: Math.floor(Math.random() * 5), // 0-4 points
    graha: Math.floor(Math.random() * 6), // 0-5 points
    gana: Math.floor(Math.random() * 7), // 0-6 points
    rashi: Math.floor(Math.random() * 8), // 0-7 points
    nadi: Math.floor(Math.random() * 9) // 0-8 points
  };

  const total = Object.values(gunas).reduce((sum, points) => sum + points, 0);

  return {
    ...gunas,
    total,
    maxPossible: 36,
    percentage: Math.round((total / 36) * 100)
  };
}

function checkManglikCompatibility(manglik1, manglik2) {
  const compatibility = {
    compatible: false,
    reason: '',
    severity: 'low'
  };

  if (manglik1 === 'no' && manglik2 === 'no') {
    compatibility.compatible = true;
    compatibility.reason = 'Both are non-manglik';
  } else if (manglik1 === 'yes' && manglik2 === 'yes') {
    compatibility.compatible = true;
    compatibility.reason = 'Both are manglik';
  } else if (manglik1 === 'anshik' || manglik2 === 'anshik') {
    compatibility.compatible = true;
    compatibility.reason = 'Partial manglik compatibility';
    compatibility.severity = 'medium';
  } else {
    compatibility.compatible = false;
    compatibility.reason = 'Manglik dosha mismatch';
    compatibility.severity = 'high';
  }

  return compatibility;
}

function calculateOverallCompatibility(gunaMatching, manglikCompatibility) {
  let rating = 'poor';
  let recommendation = 'Not recommended';

  if (gunaMatching.total >= 28 && manglikCompatibility.compatible) {
    rating = 'excellent';
    recommendation = 'Highly recommended';
  } else if (gunaMatching.total >= 24 && manglikCompatibility.compatible) {
    rating = 'very good';
    recommendation = 'Recommended';
  } else if (gunaMatching.total >= 20 && manglikCompatibility.compatible) {
    rating = 'good';
    recommendation = 'Acceptable';
  } else if (gunaMatching.total >= 18) {
    rating = 'average';
    recommendation = 'Consult astrologer';
  }

  return {
    rating,
    recommendation,
    score: gunaMatching.total,
    manglikCompatible: manglikCompatibility.compatible
  };
}

function generateRecommendations(gunaMatching, manglikCompatibility) {
  const recommendations = [];

  if (gunaMatching.total < 18) {
    recommendations.push('Guna matching score is below recommended minimum');
  }

  if (!manglikCompatibility.compatible) {
    recommendations.push('Manglik dosha compatibility issue detected');
  }

  if (gunaMatching.nadi === 0) {
    recommendations.push('Nadi dosha present - consult astrologer for remedies');
  }

  if (recommendations.length === 0) {
    recommendations.push('Good astrological compatibility');
  }

  return recommendations;
}

module.exports = router;
