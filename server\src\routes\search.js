const express = require('express');
const { query, validationResult } = require('express-validator');
const User = require('../models/User');
const { authenticateToken, requireCompleteProfile, checkUsageLimit } = require('../middleware/auth');

const router = express.Router();

// Basic search
router.get('/', [
  authenticateToken,
  requireCompleteProfile,
  checkUsageLimit('searchesPerformed'),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('ageMin').optional().isInt({ min: 18, max: 80 }).withMessage('Invalid minimum age'),
  query('ageMax').optional().isInt({ min: 18, max: 80 }).withMessage('Invalid maximum age'),
  query('religion').optional().isIn(['hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain', 'parsi', 'jewish', 'other']).withMessage('Invalid religion'),
  query('maritalStatus').optional().isIn(['never_married', 'divorced', 'widowed', 'separated']).withMessage('Invalid marital status'),
  query('diet').optional().isIn(['vegetarian', 'non_vegetarian', 'vegan', 'jain_food']).withMessage('Invalid diet preference')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const currentUser = req.user;
    const {
      page = 1,
      limit = 20,
      ageMin,
      ageMax,
      heightMin,
      heightMax,
      religion,
      caste,
      maritalStatus,
      education,
      occupation,
      incomeMin,
      incomeMax,
      city,
      state,
      country,
      diet,
      smoking,
      drinking,
      manglik,
      withPhoto,
      verified,
      recentlyJoined,
      sortBy = 'lastActive'
    } = req.query;

    // Build search query
    const searchQuery = {
      _id: { $ne: currentUser._id }, // Exclude current user
      isActive: true,
      isBlocked: false,
      profileCompleted: true,
      blockedUsers: { $ne: currentUser._id } // Exclude users who blocked current user
    };

    // Age filter
    if (ageMin || ageMax) {
      searchQuery['personalInfo.age'] = {};
      if (ageMin) searchQuery['personalInfo.age'].$gte = parseInt(ageMin);
      if (ageMax) searchQuery['personalInfo.age'].$lte = parseInt(ageMax);
    }

    // Height filter
    if (heightMin || heightMax) {
      // Height comparison logic would need to be implemented based on your height format
      // For now, using string comparison
      if (heightMin) searchQuery['personalInfo.height'] = { $gte: heightMin };
      if (heightMax) searchQuery['personalInfo.height'] = { ...searchQuery['personalInfo.height'], $lte: heightMax };
    }

    // Religion filter
    if (religion) {
      searchQuery['religiousInfo.religion'] = religion;
    }

    // Caste filter
    if (caste) {
      searchQuery['religiousInfo.caste'] = new RegExp(caste, 'i');
    }

    // Marital status filter
    if (maritalStatus) {
      searchQuery['personalInfo.maritalStatus'] = maritalStatus;
    }

    // Education filter
    if (education) {
      searchQuery['educationCareer.highestEducation'] = new RegExp(education, 'i');
    }

    // Occupation filter
    if (occupation) {
      searchQuery['educationCareer.occupation'] = new RegExp(occupation, 'i');
    }

    // Income filter
    if (incomeMin || incomeMax) {
      // Income comparison would need proper implementation based on income format
      if (incomeMin) searchQuery['educationCareer.annualIncome'] = { $gte: incomeMin };
      if (incomeMax) searchQuery['educationCareer.annualIncome'] = { ...searchQuery['educationCareer.annualIncome'], $lte: incomeMax };
    }

    // Location filters
    if (city) {
      searchQuery['personalInfo.city'] = new RegExp(city, 'i');
    }
    if (state) {
      searchQuery['personalInfo.state'] = new RegExp(state, 'i');
    }
    if (country) {
      searchQuery['personalInfo.country'] = new RegExp(country, 'i');
    }

    // Lifestyle filters
    if (diet) {
      searchQuery['lifestyle.diet'] = diet;
    }
    if (smoking) {
      searchQuery['lifestyle.smoking'] = smoking;
    }
    if (drinking) {
      searchQuery['lifestyle.drinking'] = drinking;
    }

    // Manglik filter
    if (manglik) {
      searchQuery['religiousInfo.manglik'] = manglik;
    }

    // Photo filter
    if (withPhoto === 'true') {
      searchQuery.profilePicture = { $exists: true, $ne: '' };
    }

    // Verification filter
    if (verified === 'true') {
      searchQuery['verificationStatus.email'] = true;
      searchQuery['verificationStatus.phone'] = true;
    }

    // Recently joined filter (last 30 days)
    if (recentlyJoined === 'true') {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      searchQuery.createdAt = { $gte: thirtyDaysAgo };
    }

    // Sort options
    let sortOptions = {};
    switch (sortBy) {
      case 'lastActive':
        sortOptions = { lastActive: -1 };
        break;
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'profileViews':
        sortOptions = { profileViews: -1 };
        break;
      case 'age':
        sortOptions = { 'personalInfo.age': 1 };
        break;
      default:
        sortOptions = { lastActive: -1 };
    }

    // Premium users get priority in search results
    const aggregationPipeline = [
      { $match: searchQuery },
      {
        $addFields: {
          priorityScore: {
            $switch: {
              branches: [
                { case: { $eq: ['$membershipType', 'platinum'] }, then: 4 },
                { case: { $eq: ['$membershipType', 'gold'] }, then: 3 },
                { case: { $eq: ['$membershipType', 'silver'] }, then: 2 },
                { case: { $eq: ['$membershipType', 'free'] }, then: 1 }
              ],
              default: 1
            }
          }
        }
      },
      { $sort: { priorityScore: -1, ...sortOptions } },
      { $skip: (page - 1) * limit },
      { $limit: parseInt(limit) },
      {
        $project: {
          password: 0,
          emailVerificationToken: 0,
          phoneVerificationOTP: 0,
          phoneVerificationExpires: 0,
          passwordResetToken: 0,
          passwordResetExpires: 0,
          blockedUsers: 0,
          shortlistedProfiles: 0
        }
      }
    ];

    const profiles = await User.aggregate(aggregationPipeline);

    // Get total count for pagination
    const totalCount = await User.countDocuments(searchQuery);

    // Increment usage count
    if (req.subscription) {
      await req.subscription.incrementUsage('searchesPerformed');
    }

    res.json({
      success: true,
      data: {
        profiles,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        },
        filters: {
          ageMin,
          ageMax,
          religion,
          caste,
          maritalStatus,
          education,
          occupation,
          city,
          state,
          country,
          diet,
          smoking,
          drinking,
          manglik,
          withPhoto,
          verified,
          recentlyJoined,
          sortBy
        }
      }
    });
  } catch (error) {
    console.error('Search error:', error);
    res.status(500).json({
      success: false,
      message: 'Search failed'
    });
  }
});

// Advanced search (premium feature)
router.get('/advanced', [
  authenticateToken,
  requireCompleteProfile,
  checkUsageLimit('searchesPerformed')
], async (req, res) => {
  try {
    const currentUser = req.user;

    // Check if user has advanced search feature
    if (currentUser.membershipType === 'free') {
      return res.status(403).json({
        success: false,
        message: 'Advanced search requires premium membership',
        code: 'PREMIUM_REQUIRED'
      });
    }

    // Advanced search implementation with more complex filters
    // This would include features like:
    // - Multiple location preferences
    // - Income range with currency conversion
    // - Education level matching
    // - Family background matching
    // - Horoscope compatibility
    // - Lifestyle compatibility scoring

    res.json({
      success: true,
      message: 'Advanced search feature - implementation in progress',
      data: {
        availableFilters: [
          'multipleLocations',
          'incomeRangeWithCurrency',
          'educationLevelMatching',
          'familyBackgroundMatching',
          'horoscopeCompatibility',
          'lifestyleCompatibilityScoring'
        ]
      }
    });
  } catch (error) {
    console.error('Advanced search error:', error);
    res.status(500).json({
      success: false,
      message: 'Advanced search failed'
    });
  }
});

// Get search suggestions
router.get('/suggestions', authenticateToken, async (req, res) => {
  try {
    const currentUser = req.user;

    // Get suggestions based on user's partner preferences
    const preferences = currentUser.partnerPreferences;
    
    if (!preferences) {
      return res.status(400).json({
        success: false,
        message: 'Please set your partner preferences first'
      });
    }

    const suggestionQuery = {
      _id: { $ne: currentUser._id },
      isActive: true,
      isBlocked: false,
      profileCompleted: true,
      blockedUsers: { $ne: currentUser._id }
    };

    // Apply preference filters
    if (preferences.ageRange) {
      suggestionQuery['personalInfo.age'] = {
        $gte: preferences.ageRange.min || 18,
        $lte: preferences.ageRange.max || 80
      };
    }

    if (preferences.religion && preferences.religion.length > 0) {
      suggestionQuery['religiousInfo.religion'] = { $in: preferences.religion };
    }

    if (preferences.maritalStatus && preferences.maritalStatus.length > 0) {
      suggestionQuery['personalInfo.maritalStatus'] = { $in: preferences.maritalStatus };
    }

    if (preferences.education && preferences.education.length > 0) {
      suggestionQuery['educationCareer.highestEducation'] = { 
        $in: preferences.education.map(edu => new RegExp(edu, 'i'))
      };
    }

    if (preferences.location && preferences.location.length > 0) {
      suggestionQuery.$or = [
        { 'personalInfo.city': { $in: preferences.location.map(loc => new RegExp(loc, 'i')) } },
        { 'personalInfo.state': { $in: preferences.location.map(loc => new RegExp(loc, 'i')) } }
      ];
    }

    const suggestions = await User.find(suggestionQuery)
      .select('-password -emailVerificationToken -phoneVerificationOTP -phoneVerificationExpires -passwordResetToken -passwordResetExpires -blockedUsers -shortlistedProfiles')
      .sort({ lastActive: -1, membershipType: -1 })
      .limit(10);

    res.json({
      success: true,
      data: {
        suggestions,
        basedOn: 'partner_preferences',
        count: suggestions.length
      }
    });
  } catch (error) {
    console.error('Get suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get suggestions'
    });
  }
});

// Get recently viewed profiles
router.get('/recently-viewed', authenticateToken, async (req, res) => {
  try {
    // This would require implementing a view history tracking system
    // For now, returning a placeholder response
    
    res.json({
      success: true,
      data: {
        recentlyViewed: [],
        message: 'Recently viewed profiles feature - implementation in progress'
      }
    });
  } catch (error) {
    console.error('Recently viewed error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recently viewed profiles'
    });
  }
});

module.exports = router;
