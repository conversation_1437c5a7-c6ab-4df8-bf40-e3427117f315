const nodemailer = require('nodemailer');

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.EMAIL_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.EMAIL_PORT) || 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS
    }
  });
};

// Email templates
const emailTemplates = {
  emailVerification: (data) => ({
    subject: 'Verify Your Email - Matrimony Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Welcome to Matrimony Platform!</h2>
        <p>Dear ${data.name},</p>
        <p>Thank you for registering with us. Please verify your email address by clicking the button below:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.verificationLink}" 
             style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Verify Email Address
          </a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #007bff;">${data.verificationLink}</p>
        <p>This link will expire in 24 hours for security reasons.</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          If you didn't create an account with us, please ignore this email.
        </p>
      </div>
    `
  }),

  newInterest: (data) => ({
    subject: 'New Interest Received - Matrimony Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">New Interest Received!</h2>
        <p>Dear ${data.receiverName},</p>
        <p>Great news! ${data.senderName} has expressed interest in your profile.</p>
        ${data.message ? `<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; font-style: italic;">"${data.message}"</p>
        </div>` : ''}
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.profileLink}" 
             style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            View Profile
          </a>
        </div>
        <p>Log in to your account to respond to this interest and start your journey towards finding your perfect match!</p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          You received this email because someone expressed interest in your matrimony profile.
        </p>
      </div>
    `
  }),

  interestResponse: (data) => ({
    subject: `Interest ${data.status.charAt(0).toUpperCase() + data.status.slice(1)} - Matrimony Platform`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: ${data.status === 'accepted' ? '#28a745' : '#dc3545'};">
          Interest ${data.status.charAt(0).toUpperCase() + data.status.slice(1)}!
        </h2>
        <p>Dear ${data.senderName},</p>
        <p>
          ${data.status === 'accepted' 
            ? `Congratulations! ${data.receiverName} has accepted your interest.` 
            : `${data.receiverName} has declined your interest.`
          }
        </p>
        ${data.message ? `<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="margin: 0; font-style: italic;">"${data.message}"</p>
        </div>` : ''}
        ${data.status === 'accepted' ? `
          <div style="text-align: center; margin: 30px 0;">
            <a href="${data.profileLink}" 
               style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
              Start Conversation
            </a>
          </div>
          <p>You can now start messaging each other. Best of luck with your journey!</p>
        ` : `
          <p>Don't worry, there are many other profiles waiting for you. Keep exploring and find your perfect match!</p>
        `}
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          This is an automated notification from Matrimony Platform.
        </p>
      </div>
    `
  }),

  passwordReset: (data) => ({
    subject: 'Reset Your Password - Matrimony Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">Password Reset Request</h2>
        <p>Dear ${data.name},</p>
        <p>We received a request to reset your password. Click the button below to create a new password:</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.resetLink}" 
             style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset Password
          </a>
        </div>
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #dc3545;">${data.resetLink}</p>
        <p>This link will expire in 1 hour for security reasons.</p>
        <p><strong>If you didn't request a password reset, please ignore this email.</strong></p>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          For security reasons, this link will only work once and will expire soon.
        </p>
      </div>
    `
  }),

  subscriptionActivated: (data) => ({
    subject: 'Subscription Activated - Matrimony Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">Subscription Activated Successfully!</h2>
        <p>Dear ${data.name},</p>
        <p>Congratulations! Your ${data.planName} subscription has been activated successfully.</p>
        <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #333;">Subscription Details:</h3>
          <ul style="list-style: none; padding: 0;">
            <li><strong>Plan:</strong> ${data.planName}</li>
            <li><strong>Duration:</strong> ${data.duration} months</li>
            <li><strong>Start Date:</strong> ${data.startDate}</li>
            <li><strong>End Date:</strong> ${data.endDate}</li>
            <li><strong>Amount Paid:</strong> ₹${data.amountPaid}</li>
          </ul>
        </div>
        <p>You now have access to all premium features. Start exploring and find your perfect match!</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.dashboardLink}" 
             style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Go to Dashboard
          </a>
        </div>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          Thank you for choosing Matrimony Platform for your matrimonial journey.
        </p>
      </div>
    `
  }),

  profileVerified: (data) => ({
    subject: 'Profile Verified - Matrimony Platform',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #28a745;">Profile Verified Successfully!</h2>
        <p>Dear ${data.name},</p>
        <p>Great news! Your profile has been verified by our team.</p>
        <div style="background-color: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
          <p style="margin: 0;"><strong>Verification Status:</strong> ${data.verificationType} verification completed</p>
        </div>
        <p>Verified profiles get better visibility and more trust from other users. This will help you get more genuine interests!</p>
        <div style="text-align: center; margin: 30px 0;">
          <a href="${data.profileLink}" 
             style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
            View Your Profile
          </a>
        </div>
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="color: #666; font-size: 12px;">
          Keep your profile updated to get the best matches.
        </p>
      </div>
    `
  })
};

// Send email function
const sendEmail = async ({ to, subject, template, data, html, text }) => {
  try {
    const transporter = createTransporter();

    let emailContent = {};

    if (template && emailTemplates[template]) {
      emailContent = emailTemplates[template](data);
    } else if (html || text) {
      emailContent = { subject, html, text };
    } else {
      throw new Error('Either template or html/text content is required');
    }

    const mailOptions = {
      from: `"Matrimony Platform" <${process.env.EMAIL_USER}>`,
      to,
      subject: emailContent.subject || subject,
      html: emailContent.html,
      text: emailContent.text
    };

    const result = await transporter.sendMail(mailOptions);
    
    console.log('Email sent successfully:', result.messageId);
    return {
      success: true,
      messageId: result.messageId
    };
  } catch (error) {
    console.error('Email sending failed:', error);
    throw new Error(`Failed to send email: ${error.message}`);
  }
};

// Send bulk emails
const sendBulkEmails = async (emails) => {
  try {
    const transporter = createTransporter();
    const results = [];

    for (const email of emails) {
      try {
        const result = await sendEmail(email);
        results.push({ ...email, success: true, messageId: result.messageId });
      } catch (error) {
        results.push({ ...email, success: false, error: error.message });
      }
    }

    return results;
  } catch (error) {
    console.error('Bulk email sending failed:', error);
    throw new Error(`Failed to send bulk emails: ${error.message}`);
  }
};

// Verify email configuration
const verifyEmailConfig = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('Email configuration verified successfully');
    return true;
  } catch (error) {
    console.error('Email configuration verification failed:', error);
    return false;
  }
};

module.exports = {
  sendEmail,
  sendBulkEmails,
  verifyEmailConfig,
  emailTemplates
};
