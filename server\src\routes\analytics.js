const express = require('express');
const { query, validationResult } = require('express-validator');
const analyticsService = require('../services/analyticsService');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// Get user analytics
router.get('/user', [
  authenticateToken,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format (e.g., 30d, 1w, 3m)')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { timeframe = '30d' } = req.query;
    const userId = req.user._id;

    const analytics = await analyticsService.getUserAnalytics(userId, timeframe);

    res.json({
      success: true,
      data: {
        analytics,
        timeframe,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Get user analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user analytics'
    });
  }
});

// Get user profile insights
router.get('/user/profile-insights', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;
    const user = req.user;

    // Generate profile insights and recommendations
    const insights = {
      profileStrength: {
        score: user.profileCompletionPercentage,
        recommendations: []
      },
      visibility: {
        score: calculateVisibilityScore(user),
        tips: []
      },
      engagement: {
        score: await calculateEngagementScore(userId),
        suggestions: []
      }
    };

    // Add recommendations based on profile analysis
    if (user.profileCompletionPercentage < 80) {
      insights.profileStrength.recommendations.push('Complete your profile to increase match quality');
    }

    if (!user.profilePicture) {
      insights.visibility.tips.push('Add a profile picture to get 5x more views');
    }

    if (!user.verificationStatus.email) {
      insights.visibility.tips.push('Verify your email to build trust');
    }

    if (!user.verificationStatus.phone) {
      insights.visibility.tips.push('Verify your phone number for better credibility');
    }

    res.json({
      success: true,
      data: {
        insights,
        lastUpdated: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Get profile insights error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile insights'
    });
  }
});

// Get match analytics
router.get('/user/matches', [
  authenticateToken,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const userId = req.user._id;

    const matchAnalytics = await analyticsService.getMatchAnalytics(userId, timeframe);

    // Add additional match insights
    const insights = {
      ...matchAnalytics,
      recommendations: generateMatchRecommendations(matchAnalytics),
      trends: await getMatchTrends(userId, timeframe)
    };

    res.json({
      success: true,
      data: {
        matchAnalytics: insights,
        timeframe
      }
    });
  } catch (error) {
    console.error('Get match analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get match analytics'
    });
  }
});

// Get communication analytics
router.get('/user/communication', [
  authenticateToken,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;
    const userId = req.user._id;

    const communicationAnalytics = await analyticsService.getMessageAnalytics(userId, timeframe);

    // Add communication insights
    const insights = {
      ...communicationAnalytics,
      tips: generateCommunicationTips(communicationAnalytics),
      bestTimes: await getBestCommunicationTimes(userId)
    };

    res.json({
      success: true,
      data: {
        communicationAnalytics: insights,
        timeframe
      }
    });
  } catch (error) {
    console.error('Get communication analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get communication analytics'
    });
  }
});

// Admin: Get platform analytics
router.get('/platform', [
  authenticateToken,
  requireAdmin,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;

    const platformAnalytics = await analyticsService.getPlatformAnalytics(timeframe);

    res.json({
      success: true,
      data: {
        platformAnalytics,
        timeframe,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Get platform analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get platform analytics'
    });
  }
});

// Admin: Get user behavior analytics
router.get('/platform/user-behavior', [
  authenticateToken,
  requireAdmin,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;

    // Get user behavior patterns
    const behaviorAnalytics = await getUserBehaviorAnalytics(timeframe);

    res.json({
      success: true,
      data: {
        behaviorAnalytics,
        timeframe
      }
    });
  } catch (error) {
    console.error('Get user behavior analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user behavior analytics'
    });
  }
});

// Admin: Get revenue analytics
router.get('/platform/revenue', [
  authenticateToken,
  requireAdmin,
  query('timeframe').optional().matches(/^\d+[dwmy]$/).withMessage('Invalid timeframe format')
], async (req, res) => {
  try {
    const { timeframe = '30d' } = req.query;

    const revenueAnalytics = await getRevenueAnalytics(timeframe);

    res.json({
      success: true,
      data: {
        revenueAnalytics,
        timeframe
      }
    });
  } catch (error) {
    console.error('Get revenue analytics error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get revenue analytics'
    });
  }
});

// Helper functions
function calculateVisibilityScore(user) {
  let score = 0;
  
  if (user.profilePicture) score += 30;
  if (user.verificationStatus.email) score += 20;
  if (user.verificationStatus.phone) score += 20;
  if (user.profileCompletionPercentage >= 80) score += 20;
  if (user.membershipType !== 'free') score += 10;
  
  return Math.min(score, 100);
}

async function calculateEngagementScore(userId) {
  // This would calculate based on recent activity
  // For now, return a mock score
  return 75;
}

function generateMatchRecommendations(matchAnalytics) {
  const recommendations = [];
  
  if (matchAnalytics.totalMatches < 5) {
    recommendations.push('Expand your search criteria to find more compatible matches');
  }
  
  if (matchAnalytics.matchQuality < 70) {
    recommendations.push('Update your partner preferences for better quality matches');
  }
  
  return recommendations;
}

async function getMatchTrends(userId, timeframe) {
  // This would analyze match trends over time
  return {
    trending: 'up',
    change: '+15%',
    period: timeframe
  };
}

function generateCommunicationTips(communicationAnalytics) {
  const tips = [];
  
  if (communicationAnalytics.responseRate < 50) {
    tips.push('Personalize your messages to get better response rates');
  }
  
  if (communicationAnalytics.sent > communicationAnalytics.received * 2) {
    tips.push('Try to balance conversation - ask questions to encourage responses');
  }
  
  return tips;
}

async function getBestCommunicationTimes(userId) {
  // This would analyze when user gets best response rates
  return {
    bestDay: 'Sunday',
    bestTime: '7-9 PM',
    timezone: 'IST'
  };
}

async function getUserBehaviorAnalytics(timeframe) {
  // This would analyze user behavior patterns
  return {
    mostActiveHours: ['7-9 PM', '9-11 PM'],
    mostActiveDays: ['Sunday', 'Saturday'],
    averageSessionDuration: '15 minutes',
    bounceRate: '25%'
  };
}

async function getRevenueAnalytics(timeframe) {
  // This would calculate revenue analytics
  return {
    totalRevenue: 150000,
    growth: '+12%',
    averageRevenuePerUser: 2500,
    conversionRate: '8.5%'
  };
}

module.exports = router;
