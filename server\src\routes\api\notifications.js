const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, query, validationResult } = require('express-validator');

// In-memory storage for notifications (in production, use proper database table)
let notifications = [];
let notificationIdCounter = 1;

// Helper function to generate notification ID
const generateNotificationId = () => {
  return `notif_${notificationIdCounter++}_${Date.now()}`;
};

// Helper function to create notification
const createNotification = (userId, type, title, message, data = {}) => {
  const notification = {
    id: generateNotificationId(),
    userId,
    type,
    title,
    message,
    data,
    isRead: false,
    createdAt: new Date().toISOString(),
    readAt: null
  };
  
  notifications.push(notification);
  return notification;
};

// Get notifications for user
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  query('type').optional().isIn(['interest', 'message', 'profile_view', 'subscription', 'system'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { page = 1, limit = 20, type } = req.query;
    const userId = req.user.id;
    const offset = (page - 1) * limit;

    // Filter notifications for user
    let userNotifications = notifications.filter(n => n.userId === userId);
    
    // Filter by type if specified
    if (type) {
      userNotifications = userNotifications.filter(n => n.type === type);
    }

    // Sort by creation date (newest first)
    userNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Paginate
    const paginatedNotifications = userNotifications.slice(offset, offset + limit);
    const totalCount = userNotifications.length;

    res.json({
      success: true,
      data: {
        notifications: paginatedNotifications,
        pagination: {
          currentPage: page,
          totalPages: Math.ceil(totalCount / limit),
          totalCount,
          hasNext: offset + limit < totalCount,
          hasPrev: page > 1
        }
      }
    });

  } catch (error) {
    console.error('Get notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notifications'
    });
  }
});

// Mark notification as read
router.put('/:notificationId/read', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.id;

    const notificationIndex = notifications.findIndex(n => 
      n.id === notificationId && n.userId === userId
    );

    if (notificationIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    // Mark as read
    notifications[notificationIndex].isRead = true;
    notifications[notificationIndex].readAt = new Date().toISOString();

    res.json({
      success: true,
      message: 'Notification marked as read',
      data: { notification: notifications[notificationIndex] }
    });

  } catch (error) {
    console.error('Mark notification as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark notification as read'
    });
  }
});

// Mark all notifications as read
router.put('/read-all', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const currentTime = new Date().toISOString();

    // Mark all unread notifications as read
    let updatedCount = 0;
    notifications.forEach(notification => {
      if (notification.userId === userId && !notification.isRead) {
        notification.isRead = true;
        notification.readAt = currentTime;
        updatedCount++;
      }
    });

    res.json({
      success: true,
      message: 'All notifications marked as read',
      data: { updatedCount }
    });

  } catch (error) {
    console.error('Mark all notifications as read error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to mark all notifications as read'
    });
  }
});

// Get unread notification count
router.get('/unread-count', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    const unreadCount = notifications.filter(n => 
      n.userId === userId && !n.isRead
    ).length;

    res.json({
      success: true,
      data: { unreadCount }
    });

  } catch (error) {
    console.error('Get unread count error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get unread count'
    });
  }
});

// Update notification settings
router.put('/settings', authenticateToken, [
  body('emailNotifications').optional().isBoolean(),
  body('pushNotifications').optional().isBoolean(),
  body('smsNotifications').optional().isBoolean(),
  body('interestNotifications').optional().isBoolean(),
  body('messageNotifications').optional().isBoolean(),
  body('profileViewNotifications').optional().isBoolean(),
  body('marketingNotifications').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update notification settings
    const notificationSettings = { ...user.notificationSettings, ...req.body };
    user.notificationSettings = notificationSettings;
    await user.save();

    res.json({
      success: true,
      message: 'Notification settings updated successfully',
      data: { notificationSettings }
    });

  } catch (error) {
    console.error('Update notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update notification settings'
    });
  }
});

// Get notification settings
router.get('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const defaultSettings = {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      interestNotifications: true,
      messageNotifications: true,
      profileViewNotifications: true,
      marketingNotifications: false
    };

    const notificationSettings = { ...defaultSettings, ...user.notificationSettings };

    res.json({
      success: true,
      data: { notificationSettings }
    });

  } catch (error) {
    console.error('Get notification settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get notification settings'
    });
  }
});

// Create sample notifications for testing
router.post('/create-samples', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;

    // Create sample notifications
    const sampleNotifications = [
      {
        type: 'interest',
        title: 'New Interest Received',
        message: 'Priya Gupta has sent you an interest. Check her profile now!',
        data: { fromUserId: 'user_2', profileId: 'user_2' }
      },
      {
        type: 'message',
        title: 'New Message',
        message: 'You have received a new message from Ananya Singh.',
        data: { fromUserId: 'user_6', conversationId: 'conv_123' }
      },
      {
        type: 'profile_view',
        title: 'Profile Viewed',
        message: 'Your profile was viewed by 3 people today.',
        data: { viewCount: 3 }
      },
      {
        type: 'subscription',
        title: 'Subscription Expiring',
        message: 'Your Gold membership expires in 7 days. Renew now to continue enjoying premium features.',
        data: { membershipType: 'gold', daysLeft: 7 }
      },
      {
        type: 'system',
        title: 'Profile Completion',
        message: 'Complete your profile to get 5x more matches. Add photos and preferences.',
        data: { completionPercentage: 75 }
      }
    ];

    const createdNotifications = sampleNotifications.map(notif => 
      createNotification(userId, notif.type, notif.title, notif.message, notif.data)
    );

    res.json({
      success: true,
      message: 'Sample notifications created successfully',
      data: { notifications: createdNotifications }
    });

  } catch (error) {
    console.error('Create sample notifications error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create sample notifications'
    });
  }
});

// Delete notification
router.delete('/:notificationId', authenticateToken, async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.id;

    const notificationIndex = notifications.findIndex(n => 
      n.id === notificationId && n.userId === userId
    );

    if (notificationIndex === -1) {
      return res.status(404).json({
        success: false,
        message: 'Notification not found'
      });
    }

    // Remove notification
    notifications.splice(notificationIndex, 1);

    res.json({
      success: true,
      message: 'Notification deleted successfully'
    });

  } catch (error) {
    console.error('Delete notification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification'
    });
  }
});

// Utility function to send notification (can be used by other modules)
const sendNotification = (userId, type, title, message, data = {}) => {
  return createNotification(userId, type, title, message, data);
};

module.exports = router;
module.exports.sendNotification = sendNotification;
