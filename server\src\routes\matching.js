const express = require('express');
const { query, validationResult } = require('express-validator');
const User = require('../models/User');
const matchingService = require('../services/matchingService');
const { authenticateToken, requireCompleteProfile, checkUsageLimit } = require('../middleware/auth');

const router = express.Router();

// Get AI-powered match recommendations
router.get('/recommendations', [
  authenticateToken,
  requireCompleteProfile,
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { limit = 20 } = req.query;
    const userId = req.user._id;

    const matches = await matchingService.getTopMatches(userId, parseInt(limit));

    // Format response
    const recommendations = matches.map(match => ({
      profile: {
        id: match.user._id,
        name: match.user.name,
        profilePicture: match.user.profilePicture,
        age: match.user.personalInfo?.age,
        city: match.user.personalInfo?.city,
        education: match.user.educationCareer?.highestEducation,
        occupation: match.user.educationCareer?.occupation,
        religion: match.user.religiousInfo?.religion,
        caste: match.user.religiousInfo?.caste,
        membershipType: match.user.membershipType,
        verificationStatus: match.user.verificationStatus,
        lastActive: match.user.lastActive
      },
      compatibility: match.compatibility,
      matchReason: generateMatchReason(match.compatibility)
    }));

    res.json({
      success: true,
      data: {
        recommendations,
        totalFound: recommendations.length,
        algorithm: 'AI-powered compatibility matching'
      }
    });
  } catch (error) {
    console.error('Get recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations'
    });
  }
});

// Get compatibility score between two users
router.get('/compatibility/:userId', [
  authenticateToken,
  requireCompleteProfile
], async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUserId = req.user._id;

    if (currentUserId.toString() === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot calculate compatibility with yourself'
      });
    }

    const [currentUser, targetUser] = await Promise.all([
      User.findById(currentUserId),
      User.findById(userId)
    ]);

    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const compatibility = await matchingService.calculateCompatibility(currentUser, targetUser);

    res.json({
      success: true,
      data: {
        compatibility,
        users: {
          current: {
            id: currentUser._id,
            name: currentUser.name
          },
          target: {
            id: targetUser._id,
            name: targetUser.name
          }
        },
        matchReason: generateMatchReason(compatibility),
        recommendations: generateImprovementSuggestions(compatibility)
      }
    });
  } catch (error) {
    console.error('Get compatibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to calculate compatibility'
    });
  }
});

// Get smart search suggestions based on user behavior
router.get('/smart-suggestions', [
  authenticateToken,
  requireCompleteProfile
], async (req, res) => {
  try {
    const userId = req.user._id;
    const user = await User.findById(userId);

    // Get suggestions based on user's partner preferences and behavior
    const suggestions = await generateSmartSuggestions(user);

    res.json({
      success: true,
      data: {
        suggestions,
        basedOn: [
          'Your partner preferences',
          'Profile viewing history',
          'Interest patterns',
          'Similar user preferences'
        ]
      }
    });
  } catch (error) {
    console.error('Smart suggestions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get smart suggestions'
    });
  }
});

// Get trending profiles
router.get('/trending', [
  authenticateToken,
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20')
], async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const userId = req.user._id;

    // Get trending profiles based on views, interests, and recent activity
    const trendingProfiles = await User.aggregate([
      {
        $match: {
          _id: { $ne: userId },
          isActive: true,
          profileCompleted: true,
          blockedUsers: { $ne: userId }
        }
      },
      {
        $addFields: {
          trendingScore: {
            $add: [
              { $multiply: ['$profileViews', 0.3] },
              { $multiply: [{ $size: { $ifNull: ['$shortlistedProfiles', []] } }, 0.4] },
              {
                $cond: [
                  { $gte: ['$lastActive', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
                  20,
                  0
                ]
              }
            ]
          }
        }
      },
      { $sort: { trendingScore: -1, lastActive: -1 } },
      { $limit: parseInt(limit) },
      {
        $project: {
          name: 1,
          profilePicture: 1,
          'personalInfo.age': 1,
          'personalInfo.city': 1,
          'educationCareer.occupation': 1,
          'religiousInfo.religion': 1,
          membershipType: 1,
          verificationStatus: 1,
          profileViews: 1,
          lastActive: 1,
          trendingScore: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        trendingProfiles,
        algorithm: 'Trending based on views, interests, and activity'
      }
    });
  } catch (error) {
    console.error('Get trending profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending profiles'
    });
  }
});

// Get recently joined profiles
router.get('/recent', [
  authenticateToken,
  query('limit').optional().isInt({ min: 1, max: 20 }).withMessage('Limit must be between 1 and 20'),
  query('days').optional().isInt({ min: 1, max: 30 }).withMessage('Days must be between 1 and 30')
], async (req, res) => {
  try {
    const { limit = 10, days = 7 } = req.query;
    const userId = req.user._id;

    const cutoffDate = new Date(Date.now() - parseInt(days) * 24 * 60 * 60 * 1000);

    const recentProfiles = await User.find({
      _id: { $ne: userId },
      isActive: true,
      profileCompleted: true,
      blockedUsers: { $ne: userId },
      createdAt: { $gte: cutoffDate }
    })
    .select('name profilePicture personalInfo.age personalInfo.city educationCareer.occupation religiousInfo.religion membershipType verificationStatus createdAt')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit));

    res.json({
      success: true,
      data: {
        recentProfiles,
        timeframe: `Last ${days} days`,
        totalFound: recentProfiles.length
      }
    });
  } catch (error) {
    console.error('Get recent profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recent profiles'
    });
  }
});

// Helper functions
function generateMatchReason(compatibility) {
  const { breakdown, overallScore } = compatibility;
  const reasons = [];

  if (breakdown.religion >= 90) reasons.push('Same religious background');
  if (breakdown.location >= 80) reasons.push('Same city/region');
  if (breakdown.education >= 80) reasons.push('Similar education level');
  if (breakdown.lifestyle >= 80) reasons.push('Compatible lifestyle choices');
  if (breakdown.age >= 80) reasons.push('Suitable age match');
  if (breakdown.horoscope >= 80) reasons.push('Astrological compatibility');

  if (reasons.length === 0) {
    if (overallScore >= 70) reasons.push('Good overall compatibility');
    else if (overallScore >= 50) reasons.push('Moderate compatibility');
    else reasons.push('Basic compatibility factors match');
  }

  return reasons.slice(0, 3); // Return top 3 reasons
}

function generateImprovementSuggestions(compatibility) {
  const { breakdown } = compatibility;
  const suggestions = [];

  if (breakdown.education < 60) {
    suggestions.push('Consider profiles with different education backgrounds for broader compatibility');
  }
  if (breakdown.location < 60) {
    suggestions.push('Explore profiles from nearby cities or states');
  }
  if (breakdown.lifestyle < 60) {
    suggestions.push('Look for profiles with flexible lifestyle preferences');
  }
  if (breakdown.horoscope < 60) {
    suggestions.push('Consult an astrologer for detailed horoscope analysis');
  }

  return suggestions;
}

async function generateSmartSuggestions(user) {
  const suggestions = {
    searchFilters: [],
    profileTips: [],
    behaviorInsights: []
  };

  // Analyze user's partner preferences
  const preferences = user.partnerPreferences;
  
  if (preferences) {
    if (preferences.ageRange) {
      suggestions.searchFilters.push({
        type: 'age',
        suggestion: `Expand age range by 2-3 years for more matches`,
        current: `${preferences.ageRange.min}-${preferences.ageRange.max}`,
        suggested: `${preferences.ageRange.min - 2}-${preferences.ageRange.max + 2}`
      });
    }

    if (preferences.location && preferences.location.length === 1) {
      suggestions.searchFilters.push({
        type: 'location',
        suggestion: 'Consider nearby cities for more options',
        current: preferences.location[0],
        suggested: 'Include neighboring cities'
      });
    }
  }

  // Profile completion suggestions
  if (user.profileCompletionPercentage < 80) {
    suggestions.profileTips.push('Complete your profile to get better matches');
  }

  if (!user.profilePicture) {
    suggestions.profileTips.push('Add a profile picture to increase profile views');
  }

  return suggestions;
}

module.exports = router;
