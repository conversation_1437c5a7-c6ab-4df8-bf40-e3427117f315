const express = require('express');
const { query, body, validationResult } = require('express-validator');
const User = require('../models/User');
const Interest = require('../models/Interest');
const { Message, Conversation } = require('../models/Message');
const { SubscriptionPlan, UserSubscription } = require('../models/Subscription');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// All admin routes require authentication and admin privileges
router.use(authenticateToken);
router.use(requireAdmin);

// Dashboard statistics
router.get('/dashboard', async (req, res) => {
  try {
    const stats = await Promise.all([
      // User statistics
      User.countDocuments({ isActive: true }),
      User.countDocuments({ profileCompleted: true }),
      User.countDocuments({ 'verificationStatus.email': true }),
      User.countDocuments({ 'verificationStatus.phone': true }),
      User.countDocuments({ membershipType: { $ne: 'free' } }),
      
      // Interest statistics
      Interest.countDocuments({ status: 'pending' }),
      Interest.countDocuments({ status: 'accepted' }),
      Interest.countDocuments({ createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } }),
      
      // Message statistics
      Message.countDocuments({ createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } }),
      
      // Subscription statistics
      UserSubscription.countDocuments({ status: 'active' }),
      UserSubscription.aggregate([
        { $match: { status: 'active' } },
        { $group: { _id: null, totalRevenue: { $sum: '$amountPaid' } } }
      ])
    ]);

    const [
      totalUsers,
      completedProfiles,
      emailVerified,
      phoneVerified,
      premiumUsers,
      pendingInterests,
      acceptedInterests,
      todayInterests,
      todayMessages,
      activeSubscriptions,
      revenueData
    ] = stats;

    const totalRevenue = revenueData[0]?.totalRevenue || 0;

    // Recent registrations (last 7 days)
    const recentRegistrations = await User.aggregate([
      {
        $match: {
          createdAt: { $gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) }
        }
      },
      {
        $group: {
          _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
          count: { $sum: 1 }
        }
      },
      { $sort: { _id: 1 } }
    ]);

    res.json({
      success: true,
      data: {
        overview: {
          totalUsers,
          completedProfiles,
          emailVerified,
          phoneVerified,
          premiumUsers,
          pendingInterests,
          acceptedInterests,
          todayInterests,
          todayMessages,
          activeSubscriptions,
          totalRevenue
        },
        recentRegistrations
      }
    });
  } catch (error) {
    console.error('Admin dashboard error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get dashboard statistics'
    });
  }
});

// Get all users with pagination and filters
router.get('/users', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search').optional().isLength({ min: 1 }).withMessage('Search term required'),
  query('membershipType').optional().isIn(['free', 'silver', 'gold', 'platinum']).withMessage('Invalid membership type'),
  query('isActive').optional().isBoolean().withMessage('isActive must be boolean'),
  query('isBlocked').optional().isBoolean().withMessage('isBlocked must be boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      search,
      membershipType,
      isActive,
      isBlocked,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Build query
    const query = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    if (membershipType) query.membershipType = membershipType;
    if (isActive !== undefined) query.isActive = isActive === 'true';
    if (isBlocked !== undefined) query.isBlocked = isBlocked === 'true';

    // Sort options
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const users = await User.find(query)
      .select('-password -emailVerificationToken -phoneVerificationOTP -passwordResetToken')
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalCount = await User.countDocuments(query);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Admin get users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get users'
    });
  }
});

// Block/Unblock user
router.put('/users/:userId/block', [
  body('isBlocked').isBoolean().withMessage('isBlocked must be boolean'),
  body('reason').optional().isLength({ max: 500 }).withMessage('Reason too long')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { isBlocked, reason } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.isBlocked = isBlocked;
    if (reason) {
      user.blockReason = reason;
      user.blockedAt = isBlocked ? new Date() : undefined;
    }

    await user.save();

    res.json({
      success: true,
      message: `User ${isBlocked ? 'blocked' : 'unblocked'} successfully`,
      data: {
        userId: user._id,
        isBlocked: user.isBlocked,
        reason
      }
    });
  } catch (error) {
    console.error('Admin block user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update user block status'
    });
  }
});

// Verify user profile
router.put('/users/:userId/verify', [
  body('verificationType').isIn(['email', 'phone', 'photo', 'document']).withMessage('Invalid verification type'),
  body('verified').isBoolean().withMessage('verified must be boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { userId } = req.params;
    const { verificationType, verified } = req.body;

    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    user.verificationStatus[verificationType] = verified;
    await user.save();

    res.json({
      success: true,
      message: `User ${verificationType} verification ${verified ? 'approved' : 'rejected'}`,
      data: {
        userId: user._id,
        verificationStatus: user.verificationStatus
      }
    });
  } catch (error) {
    console.error('Admin verify user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update verification status'
    });
  }
});

// Get subscription plans
router.get('/subscription-plans', async (req, res) => {
  try {
    const plans = await SubscriptionPlan.find().sort({ sortOrder: 1 });

    res.json({
      success: true,
      data: {
        plans
      }
    });
  } catch (error) {
    console.error('Admin get plans error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscription plans'
    });
  }
});

// Create subscription plan
router.post('/subscription-plans', [
  body('name').notEmpty().withMessage('Plan name is required'),
  body('displayName').notEmpty().withMessage('Display name is required'),
  body('price').isNumeric().withMessage('Price must be a number'),
  body('duration').isInt({ min: 1 }).withMessage('Duration must be a positive integer')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const plan = new SubscriptionPlan(req.body);
    await plan.save();

    res.status(201).json({
      success: true,
      message: 'Subscription plan created successfully',
      data: {
        plan
      }
    });
  } catch (error) {
    console.error('Admin create plan error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to create subscription plan'
    });
  }
});

// Update subscription plan
router.put('/subscription-plans/:planId', async (req, res) => {
  try {
    const { planId } = req.params;

    const plan = await SubscriptionPlan.findByIdAndUpdate(
      planId,
      req.body,
      { new: true, runValidators: true }
    );

    if (!plan) {
      return res.status(404).json({
        success: false,
        message: 'Subscription plan not found'
      });
    }

    res.json({
      success: true,
      message: 'Subscription plan updated successfully',
      data: {
        plan
      }
    });
  } catch (error) {
    console.error('Admin update plan error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update subscription plan'
    });
  }
});

// Get user subscriptions
router.get('/subscriptions', [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['active', 'expired', 'cancelled', 'pending', 'failed']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const query = {};
    if (status) query.status = status;

    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const subscriptions = await UserSubscription.find(query)
      .populate('user', 'name email phone membershipType')
      .populate('plan', 'name displayName price duration')
      .sort(sort)
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalCount = await UserSubscription.countDocuments(query);

    res.json({
      success: true,
      data: {
        subscriptions,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Admin get subscriptions error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get subscriptions'
    });
  }
});

// Get reports and analytics
router.get('/reports', async (req, res) => {
  try {
    const { reportType = 'overview', startDate, endDate } = req.query;

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }

    let reportData = {};

    switch (reportType) {
      case 'overview':
        reportData = await generateOverviewReport(dateFilter);
        break;
      case 'revenue':
        reportData = await generateRevenueReport(dateFilter);
        break;
      case 'user-activity':
        reportData = await generateUserActivityReport(dateFilter);
        break;
      case 'matching':
        reportData = await generateMatchingReport(dateFilter);
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid report type'
        });
    }

    res.json({
      success: true,
      data: {
        reportType,
        dateRange: { startDate, endDate },
        ...reportData
      }
    });
  } catch (error) {
    console.error('Admin reports error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate report'
    });
  }
});

// Helper functions for reports
async function generateOverviewReport(dateFilter) {
  const [userStats, interestStats, messageStats] = await Promise.all([
    User.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: null,
          totalUsers: { $sum: 1 },
          maleUsers: { $sum: { $cond: [{ $eq: ['$personalInfo.gender', 'male'] }, 1, 0] } },
          femaleUsers: { $sum: { $cond: [{ $eq: ['$personalInfo.gender', 'female'] }, 1, 0] } },
          verifiedUsers: { $sum: { $cond: ['$verificationStatus.email', 1, 0] } }
        }
      }
    ]),
    Interest.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]),
    Message.aggregate([
      { $match: dateFilter },
      {
        $group: {
          _id: null,
          totalMessages: { $sum: 1 }
        }
      }
    ])
  ]);

  return {
    userStats: userStats[0] || {},
    interestStats,
    messageStats: messageStats[0] || {}
  };
}

async function generateRevenueReport(dateFilter) {
  const revenueData = await UserSubscription.aggregate([
    { $match: { ...dateFilter, status: 'active' } },
    {
      $group: {
        _id: {
          month: { $month: '$createdAt' },
          year: { $year: '$createdAt' }
        },
        totalRevenue: { $sum: '$amountPaid' },
        subscriptionCount: { $sum: 1 }
      }
    },
    { $sort: { '_id.year': 1, '_id.month': 1 } }
  ]);

  return { revenueData };
}

async function generateUserActivityReport(dateFilter) {
  const activityData = await User.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$lastActive' } }
        },
        activeUsers: { $sum: 1 }
      }
    },
    { $sort: { '_id.date': 1 } }
  ]);

  return { activityData };
}

async function generateMatchingReport(dateFilter) {
  const matchingData = await Interest.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: {
          date: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
        },
        totalInterests: { $sum: 1 },
        acceptedInterests: { $sum: { $cond: [{ $eq: ['$status', 'accepted'] }, 1, 0] } }
      }
    },
    { $sort: { '_id.date': 1 } }
  ]);

  return { matchingData };
}

module.exports = router;
