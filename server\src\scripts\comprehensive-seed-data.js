const { sequelize } = require('../config/database');
const User = require('../models/sequelize/User');
const bcrypt = require('bcryptjs');

const createComprehensiveSeedData = async () => {
  try {
    console.log('🌱 Creating comprehensive seed data with 20 diverse users...');
    
    // Hash password for all users
    const hashedPassword = await bcrypt.hash('password123', 12);
    
    // Create 20 diverse sample users representing different Indian demographics
    const sampleUsers = [
      // User 1 - Software Engineer from Mumbai (Male, Gold Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: '<PERSON><PERSON><PERSON>',
        phone: '+************',
        dateOfBirth: '1992-03-15',
        gender: 'male',
        membershipType: 'gold',
        personalInfo: {
          firstName: 'Arjun',
          lastName: 'Sharma',
          age: 31,
          height: 178,
          weight: 72,
          maritalStatus: 'never_married',
          motherTongue: 'hindi',
          country: 'India',
          state: 'Maharashtra',
          city: 'Mumbai',
          complexion: 'fair',
          bodyType: 'athletic',
          aboutMe: 'Software engineer passionate about technology and travel. Looking for an educated and understanding life partner.'
        },
        familyInfo: {
          familyType: 'nuclear',
          familyStatus: 'upper_middle_class',
          familyValues: 'moderate',
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: 'Business',
          motherOccupation: 'Teacher'
        },
        educationCareer: {
          highestEducation: 'masters',
          educationField: 'Computer Science',
          occupation: 'Software Engineer',
          designation: 'Senior Developer',
          companyType: 'private',
          workExperienceYears: 8,
          annualIncomeRange: '15_20_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'occasionally',
          fitnessLevel: 'high'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Sharma',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },

      // User 2 - Doctor from Delhi (Female, Platinum Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Dr. Priya Gupta',
        phone: '+************',
        dateOfBirth: '1993-08-20',
        gender: 'female',
        membershipType: 'platinum',
        personalInfo: {
          firstName: 'Priya',
          lastName: 'Gupta',
          age: 30,
          height: 162,
          weight: 55,
          maritalStatus: 'never_married',
          motherTongue: 'hindi',
          country: 'India',
          state: 'Delhi',
          city: 'New Delhi',
          complexion: 'wheatish',
          bodyType: 'slim',
          aboutMe: 'Pediatrician by profession, love reading and classical music. Seeking a caring and family-oriented partner.'
        },
        familyInfo: {
          familyType: 'joint',
          familyStatus: 'rich',
          familyValues: 'traditional',
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: 'Doctor',
          motherOccupation: 'Professor'
        },
        educationCareer: {
          highestEducation: 'doctorate',
          educationField: 'Medicine',
          occupation: 'Doctor',
          designation: 'Pediatrician',
          companyType: 'private',
          workExperienceYears: 6,
          annualIncomeRange: '20_30_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'never',
          fitnessLevel: 'moderate'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Gupta',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['masters', 'doctorate'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },

      // User 3 - Business Owner from Ahmedabad (Male, Silver Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Rahul Patel',
        phone: '+************',
        dateOfBirth: '1988-12-10',
        gender: 'male',
        membershipType: 'silver',
        personalInfo: {
          firstName: 'Rahul',
          lastName: 'Patel',
          age: 35,
          height: 180,
          weight: 75,
          maritalStatus: 'never_married',
          motherTongue: 'gujarati',
          country: 'India',
          state: 'Gujarat',
          city: 'Ahmedabad',
          complexion: 'fair',
          bodyType: 'average',
          aboutMe: 'Family business owner with traditional values. Love cricket and traveling. Looking for a homely and caring life partner.'
        },
        familyInfo: {
          familyType: 'joint',
          familyStatus: 'rich',
          familyValues: 'traditional',
          totalBrothers: 1,
          marriedBrothers: 1,
          totalSisters: 2,
          marriedSisters: 2,
          fatherOccupation: 'Business',
          motherOccupation: 'Homemaker'
        },
        educationCareer: {
          highestEducation: 'bachelors',
          educationField: 'Commerce',
          occupation: 'Business Owner',
          designation: 'Managing Director',
          companyType: 'business',
          workExperienceYears: 12,
          annualIncomeRange: 'above_50_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'occasionally',
          fitnessLevel: 'moderate'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Patel',
          manglikStatus: 'anshik'
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
      },

      // User 4 - Teacher from Bangalore (Female, Free Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Kavya Reddy',
        phone: '+************',
        dateOfBirth: '1994-06-25',
        gender: 'female',
        membershipType: 'free',
        personalInfo: {
          firstName: 'Kavya',
          lastName: 'Reddy',
          age: 29,
          height: 158,
          weight: 52,
          maritalStatus: 'never_married',
          motherTongue: 'telugu',
          country: 'India',
          state: 'Karnataka',
          city: 'Bangalore',
          complexion: 'wheatish',
          bodyType: 'slim',
          aboutMe: 'School teacher who loves children and education. Enjoy cooking and classical dance. Seeking a supportive and understanding partner.'
        },
        familyInfo: {
          familyType: 'nuclear',
          familyStatus: 'middle_class',
          familyValues: 'moderate',
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 1,
          marriedSisters: 0,
          fatherOccupation: 'Government Employee',
          motherOccupation: 'Homemaker'
        },
        educationCareer: {
          highestEducation: 'masters',
          educationField: 'Education',
          occupation: 'Teacher',
          designation: 'Senior Teacher',
          companyType: 'government',
          workExperienceYears: 5,
          annualIncomeRange: '5_7_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'never',
          fitnessLevel: 'moderate'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Reddy',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 165,
          heightMaxCm: 180,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true
      },

      // User 5 - CA from Pune (Male, Gold Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Vikram Joshi',
        phone: '+************',
        dateOfBirth: '1990-11-08',
        gender: 'male',
        membershipType: 'gold',
        personalInfo: {
          firstName: 'Vikram',
          lastName: 'Joshi',
          age: 33,
          height: 175,
          weight: 70,
          maritalStatus: 'never_married',
          motherTongue: 'marathi',
          country: 'India',
          state: 'Maharashtra',
          city: 'Pune',
          complexion: 'fair',
          bodyType: 'average',
          aboutMe: 'Chartered Accountant with own practice. Love music and photography. Looking for an educated and independent partner.'
        },
        familyInfo: {
          familyType: 'nuclear',
          familyStatus: 'upper_middle_class',
          familyValues: 'moderate',
          totalBrothers: 0,
          marriedBrothers: 0,
          totalSisters: 2,
          marriedSisters: 1,
          fatherOccupation: 'Retired Bank Manager',
          motherOccupation: 'Homemaker'
        },
        educationCareer: {
          highestEducation: 'professional',
          educationField: 'Chartered Accountancy',
          occupation: 'Chartered Accountant',
          designation: 'Partner',
          companyType: 'self_employed',
          workExperienceYears: 10,
          annualIncomeRange: '15_20_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'socially',
          fitnessLevel: 'moderate'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Joshi',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 30,
          heightMinCm: 155,
          heightMaxCm: 168,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters', 'professional'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000)
      }
    ];

      // User 6 - Data Scientist from Hyderabad (Female, Gold Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Ananya Singh',
        phone: '+************',
        dateOfBirth: '1995-04-12',
        gender: 'female',
        membershipType: 'gold',
        personalInfo: {
          firstName: 'Ananya',
          lastName: 'Singh',
          age: 28,
          height: 165,
          weight: 58,
          maritalStatus: 'never_married',
          motherTongue: 'hindi',
          country: 'India',
          state: 'Telangana',
          city: 'Hyderabad',
          complexion: 'fair',
          bodyType: 'slim',
          aboutMe: 'Data scientist passionate about AI and machine learning. Love hiking and reading. Looking for an intellectually compatible partner.'
        },
        familyInfo: {
          familyType: 'nuclear',
          familyStatus: 'upper_middle_class',
          familyValues: 'liberal',
          totalBrothers: 1,
          marriedBrothers: 0,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: 'Engineer',
          motherOccupation: 'Doctor'
        },
        educationCareer: {
          highestEducation: 'masters',
          educationField: 'Data Science',
          occupation: 'Data Scientist',
          designation: 'Senior Data Scientist',
          companyType: 'private',
          workExperienceYears: 5,
          annualIncomeRange: '12_15_lakhs'
        },
        lifestyle: {
          diet: 'non_vegetarian',
          smoking: 'never',
          drinking: 'occasionally',
          fitnessLevel: 'high'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Singh',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 28,
          ageMax: 35,
          heightMinCm: 170,
          heightMaxCm: 185,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['masters', 'doctorate'],
          preferredDiet: ['vegetarian', 'non_vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },

      // User 7 - Lawyer from Chennai (Male, Platinum Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Karthik Kumar',
        phone: '+************',
        dateOfBirth: '1989-09-18',
        gender: 'male',
        membershipType: 'platinum',
        personalInfo: {
          firstName: 'Karthik',
          lastName: 'Kumar',
          age: 34,
          height: 182,
          weight: 78,
          maritalStatus: 'never_married',
          motherTongue: 'tamil',
          country: 'India',
          state: 'Tamil Nadu',
          city: 'Chennai',
          complexion: 'wheatish',
          bodyType: 'athletic',
          aboutMe: 'Corporate lawyer with strong family values. Enjoy classical music and tennis. Seeking a well-educated and cultured partner.'
        },
        familyInfo: {
          familyType: 'joint',
          familyStatus: 'rich',
          familyValues: 'traditional',
          totalBrothers: 2,
          marriedBrothers: 2,
          totalSisters: 1,
          marriedSisters: 1,
          fatherOccupation: 'Retired Judge',
          motherOccupation: 'Homemaker'
        },
        educationCareer: {
          highestEducation: 'masters',
          educationField: 'Law',
          occupation: 'Lawyer',
          designation: 'Senior Partner',
          companyType: 'private',
          workExperienceYears: 11,
          annualIncomeRange: '30_50_lakhs'
        },
        lifestyle: {
          diet: 'vegetarian',
          smoking: 'never',
          drinking: 'socially',
          fitnessLevel: 'high'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Kumar',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 25,
          ageMax: 32,
          heightMinCm: 155,
          heightMaxCm: 170,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters'],
          preferredDiet: ['vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },

      // User 8 - Nurse from Kochi (Female, Silver Member)
      {
        email: '<EMAIL>',
        password: hashedPassword,
        name: 'Meera Nair',
        phone: '+************',
        dateOfBirth: '1996-01-30',
        gender: 'female',
        membershipType: 'silver',
        personalInfo: {
          firstName: 'Meera',
          lastName: 'Nair',
          age: 27,
          height: 160,
          weight: 54,
          maritalStatus: 'never_married',
          motherTongue: 'malayalam',
          country: 'India',
          state: 'Kerala',
          city: 'Kochi',
          complexion: 'wheatish',
          bodyType: 'slim',
          aboutMe: 'Dedicated nurse who loves helping people. Enjoy cooking traditional Kerala dishes and gardening. Looking for a kind and caring partner.'
        },
        familyInfo: {
          familyType: 'nuclear',
          familyStatus: 'middle_class',
          familyValues: 'traditional',
          totalBrothers: 2,
          marriedBrothers: 1,
          totalSisters: 0,
          marriedSisters: 0,
          fatherOccupation: 'Government Employee',
          motherOccupation: 'Teacher'
        },
        educationCareer: {
          highestEducation: 'bachelors',
          educationField: 'Nursing',
          occupation: 'Nurse',
          designation: 'Staff Nurse',
          companyType: 'government',
          workExperienceYears: 4,
          annualIncomeRange: '3_5_lakhs'
        },
        lifestyle: {
          diet: 'non_vegetarian',
          smoking: 'never',
          drinking: 'never',
          fitnessLevel: 'moderate'
        },
        religiousInfo: {
          religion: 'hindu',
          caste: 'Nair',
          manglikStatus: 'no'
        },
        partnerPreferences: {
          ageMin: 27,
          ageMax: 35,
          heightMinCm: 165,
          heightMaxCm: 180,
          preferredReligions: ['hindu'],
          preferredEducationLevels: ['bachelors', 'masters'],
          preferredDiet: ['vegetarian', 'non_vegetarian']
        },
        emailVerifiedAt: new Date(),
        phoneVerifiedAt: new Date(),
        isActive: true,
        isPremium: true,
        subscriptionExpiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)
      }
    ];

    return sampleUsers;
  } catch (error) {
    console.error('❌ Failed to create seed data:', error);
    throw error;
  }
};

module.exports = { createComprehensiveSeedData };
