const express = require('express');
require('dotenv').config();

const app = express();

// Basic middleware
app.use(express.json());

// Test route
app.get('/test', (req, res) => {
  res.json({ message: 'Server is working!' });
});

// Try importing routes one by one to find the problematic one
try {
  console.log('Loading auth routes...');
  const authRoutes = require('./src/routes/auth');
  app.use('/api/auth', authRoutes);
  console.log('Auth routes loaded successfully');
} catch (error) {
  console.error('Error loading auth routes:', error.message);
}

try {
  console.log('Loading user routes...');
  const userRoutes = require('./src/routes/users');
  app.use('/api/users', userRoutes);
  console.log('User routes loaded successfully');
} catch (error) {
  console.error('Error loading user routes:', error.message);
}

try {
  console.log('Loading profile routes...');
  const profileRoutes = require('./src/routes/profiles');
  app.use('/api/profiles', profileRoutes);
  console.log('Profile routes loaded successfully');
} catch (error) {
  console.error('Error loading profile routes:', error.message);
}

try {
  console.log('Loading search routes...');
  const searchRoutes = require('./src/routes/search');
  app.use('/api/search', searchRoutes);
  console.log('Search routes loaded successfully');
} catch (error) {
  console.error('Error loading search routes:', error.message);
}

try {
  console.log('Loading interest routes...');
  const interestRoutes = require('./src/routes/interests');
  app.use('/api/interests', interestRoutes);
  console.log('Interest routes loaded successfully');
} catch (error) {
  console.error('Error loading interest routes:', error.message);
}

try {
  console.log('Loading message routes...');
  const messageRoutes = require('./src/routes/messages');
  app.use('/api/messages', messageRoutes);
  console.log('Message routes loaded successfully');
} catch (error) {
  console.error('Error loading message routes:', error.message);
}

try {
  console.log('Loading subscription routes...');
  const subscriptionRoutes = require('./src/routes/subscriptions');
  app.use('/api/subscriptions', subscriptionRoutes);
  console.log('Subscription routes loaded successfully');
} catch (error) {
  console.error('Error loading subscription routes:', error.message);
}

try {
  console.log('Loading horoscope routes...');
  const horoscopeRoutes = require('./src/routes/horoscope');
  app.use('/api/horoscope', horoscopeRoutes);
  console.log('Horoscope routes loaded successfully');
} catch (error) {
  console.error('Error loading horoscope routes:', error.message);
}

try {
  console.log('Loading upload routes...');
  const uploadRoutes = require('./src/routes/upload');
  app.use('/api/upload', uploadRoutes);
  console.log('Upload routes loaded successfully');
} catch (error) {
  console.error('Error loading upload routes:', error.message);
}

try {
  console.log('Loading admin routes...');
  const adminRoutes = require('./src/routes/admin');
  app.use('/api/admin', adminRoutes);
  console.log('Admin routes loaded successfully');
} catch (error) {
  console.error('Error loading admin routes:', error.message);
}

const PORT = process.env.PORT || 5001;

app.listen(PORT, () => {
  console.log(`Test server is running on port ${PORT}`);
});
