# 🚀 Deployment Guide - Indian Matrimony Platform

## 📋 **Pre-Deployment Checklist**

### **Infrastructure Requirements**
- [ ] **Server**: 4GB RAM, 2 CPU cores minimum
- [ ] **Database**: PostgreSQL 13+ with 20GB storage
- [ ] **File Storage**: AWS S3 or equivalent (100GB)
- [ ] **Domain**: SSL certificate configured
- [ ] **Email Service**: SMTP server or service
- [ ] **SMS Service**: Twilio, MSG91, or equivalent
- [ ] **Payment Gateway**: Razorpay account setup

### **Environment Setup**
- [ ] Node.js 18+ installed
- [ ] PostgreSQL configured
- [ ] Redis installed (optional)
- [ ] Nginx configured as reverse proxy
- [ ] SSL certificates installed
- [ ] Firewall configured

## 🔧 **Backend Deployment**

### **1. Server Setup**

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Install Redis (optional)
sudo apt install redis-server

# Install Nginx
sudo apt install nginx

# Install PM2 for process management
sudo npm install -g pm2
```

### **2. Database Setup**

```bash
# Switch to postgres user
sudo -u postgres psql

# Create database and user
CREATE DATABASE matrimony_production;
CREATE USER matrimony_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE matrimony_production TO matrimony_user;
\q

# Run database migrations
cd server
npm run migrate:production
```

### **3. Application Deployment**

```bash
# Clone repository
git clone <your-repo-url> /var/www/matrimony
cd /var/www/matrimony

# Install dependencies
npm install --production

# Install server dependencies
cd server
npm install --production

# Build application
npm run build

# Set up environment variables
cp .env.example .env
nano .env
```

### **4. Environment Configuration**

Create `/var/www/matrimony/server/.env`:

```env
# Database
DATABASE_URL=postgresql://matrimony_user:your_secure_password@localhost:5432/matrimony_production
DB_HOST=localhost
DB_PORT=5432
DB_NAME=matrimony_production
DB_USER=matrimony_user
DB_PASSWORD=your_secure_password

# JWT
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_REFRESH_SECRET=your_super_secure_refresh_secret_key_here
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Server
NODE_ENV=production
PORT=3001
CLIENT_URL=https://yourdomain.com
SERVER_URL=https://api.yourdomain.com

# File Upload
UPLOAD_DIR=/var/www/matrimony/uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/gif,image/webp

# AWS S3 (if using)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your-matrimony-bucket

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Indian Matrimony

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Payment Gateway (Razorpay)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret

# Redis (if using)
REDIS_URL=redis://localhost:6379

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE=/var/log/matrimony/app.log
```

### **5. PM2 Configuration**

Create `/var/www/matrimony/ecosystem.config.js`:

```javascript
module.exports = {
  apps: [{
    name: 'matrimony-api',
    script: './server/src/comprehensive-server.js',
    cwd: '/var/www/matrimony',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: '/var/log/matrimony/err.log',
    out_file: '/var/log/matrimony/out.log',
    log_file: '/var/log/matrimony/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

### **6. Start Backend Services**

```bash
# Create log directory
sudo mkdir -p /var/log/matrimony
sudo chown -R $USER:$USER /var/log/matrimony

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

## 🌐 **Frontend Deployment**

### **1. Build Frontend**

```bash
cd /var/www/matrimony

# Install dependencies
npm install

# Build for production
npm run build

# Copy build files to web directory
sudo cp -r build/* /var/www/html/
```

### **2. Nginx Configuration**

Create `/etc/nginx/sites-available/matrimony`:

```nginx
# Frontend
server {
    listen 80;
    listen [::]:80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Root directory
    root /var/www/html;
    index index.html index.htm;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    # Handle React Router
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Static assets caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API proxy
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    # File uploads
    location /uploads/ {
        alias /var/www/matrimony/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
}

# API subdomain
server {
    listen 80;
    listen [::]:80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name api.yourdomain.com;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

    # API routes
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### **3. Enable Nginx Configuration**

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/matrimony /etc/nginx/sites-enabled/

# Test configuration
sudo nginx -t

# Restart Nginx
sudo systemctl restart nginx
```

## 🔒 **SSL Certificate Setup**

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com -d api.yourdomain.com

# Set up auto-renewal
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 **Monitoring & Logging**

### **1. Log Rotation**

Create `/etc/logrotate.d/matrimony`:

```
/var/log/matrimony/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload matrimony-api
    endscript
}
```

### **2. Health Checks**

Create `/var/www/matrimony/health-check.sh`:

```bash
#!/bin/bash

# Check if API is responding
if curl -f http://localhost:3001/health > /dev/null 2>&1; then
    echo "API is healthy"
else
    echo "API is down, restarting..."
    pm2 restart matrimony-api
fi

# Check database connection
if pg_isready -h localhost -p 5432 > /dev/null 2>&1; then
    echo "Database is healthy"
else
    echo "Database connection failed"
    # Send alert email
fi
```

### **3. Monitoring Setup**

```bash
# Make health check executable
chmod +x /var/www/matrimony/health-check.sh

# Add to crontab
crontab -e
# Add this line:
*/5 * * * * /var/www/matrimony/health-check.sh
```

## 🔧 **Performance Optimization**

### **1. Database Optimization**

```sql
-- Create indexes for better performance
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_users_active ON users(is_active);
CREATE INDEX CONCURRENTLY idx_personal_details_age ON personal_details(age);
CREATE INDEX CONCURRENTLY idx_personal_details_location ON personal_details(state, city);
CREATE INDEX CONCURRENTLY idx_interests_status ON interests(status);
CREATE INDEX CONCURRENTLY idx_messages_conversation ON messages(conversation_id);

-- Analyze tables
ANALYZE;
```

### **2. Redis Caching (Optional)**

```bash
# Configure Redis
sudo nano /etc/redis/redis.conf

# Set memory limit
maxmemory 256mb
maxmemory-policy allkeys-lru

# Restart Redis
sudo systemctl restart redis
```

## 🔐 **Security Hardening**

### **1. Firewall Configuration**

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### **2. Fail2Ban Setup**

```bash
# Install Fail2Ban
sudo apt install fail2ban

# Configure for Nginx
sudo nano /etc/fail2ban/jail.local
```

Add to `/etc/fail2ban/jail.local`:

```ini
[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
```

## 📱 **Mobile App Deployment (PWA)**

The platform is already configured as a Progressive Web App. To enhance mobile experience:

### **1. App Manifest**

Ensure `/public/manifest.json` is properly configured:

```json
{
  "name": "Indian Matrimony",
  "short_name": "Matrimony",
  "description": "Find your perfect life partner",
  "start_url": "/",
  "display": "standalone",
  "theme_color": "#007bff",
  "background_color": "#ffffff",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

### **2. Service Worker**

The service worker is automatically generated during build for offline functionality.

## 🚀 **Go Live Checklist**

### **Final Steps**
- [ ] All environment variables configured
- [ ] Database migrations completed
- [ ] SSL certificates installed
- [ ] Payment gateway tested
- [ ] Email/SMS services tested
- [ ] File uploads working
- [ ] All API endpoints tested
- [ ] Frontend build deployed
- [ ] Nginx configuration active
- [ ] PM2 processes running
- [ ] Monitoring setup complete
- [ ] Backup strategy implemented
- [ ] Domain DNS configured
- [ ] CDN setup (optional)

### **Post-Launch**
- [ ] Monitor application logs
- [ ] Check performance metrics
- [ ] Verify all features working
- [ ] Test user registration flow
- [ ] Verify payment processing
- [ ] Monitor database performance
- [ ] Check email deliverability
- [ ] Test mobile responsiveness

## 📞 **Support & Maintenance**

### **Regular Maintenance Tasks**
- **Daily**: Check application logs and health
- **Weekly**: Review performance metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Database optimization and cleanup

### **Backup Strategy**
```bash
# Database backup script
#!/bin/bash
pg_dump matrimony_production | gzip > /backups/matrimony_$(date +%Y%m%d_%H%M%S).sql.gz

# File backup
tar -czf /backups/uploads_$(date +%Y%m%d_%H%M%S).tar.gz /var/www/matrimony/uploads/
```

---

## 🎉 **Congratulations!**

Your comprehensive Indian matrimony platform is now live and ready to serve users! 

**Remember to monitor the application closely in the first few days and be prepared to scale resources as user traffic grows.**

---

*For technical support or questions, refer to the comprehensive documentation or contact the development team.*
