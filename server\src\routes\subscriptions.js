const express = require("express");
const { body, validationResult } = require("express-validator");
const {
  SubscriptionPlan,
  UserSubscription,
} = require("../models/Subscription");
const User = require("../models/User");
const { authenticateToken } = require("../middleware/auth");
const Razorpay = require("razorpay");

const router = express.Router();

// Initialize Razorpay (only if keys are provided)
let razorpay = null;
if (process.env.RAZORPAY_KEY_ID && process.env.RAZORPAY_KEY_SECRET) {
  razorpay = new Razorpay({
    key_id: process.env.RAZORPAY_KEY_ID,
    key_secret: process.env.RAZORPAY_KEY_SECRET,
  });
} else {
  console.warn(
    "Razorpay keys not configured. Payment features will be disabled."
  );
}

// Get all subscription plans
router.get("/plans", async (req, res) => {
  try {
    const plans = await SubscriptionPlan.find({
      isActive: true,
      $or: [
        { validUntil: { $exists: false } },
        { validUntil: { $gte: new Date() } },
      ],
    }).sort({ sortOrder: 1, price: 1 });

    res.json({
      success: true,
      data: {
        plans,
      },
    });
  } catch (error) {
    console.error("Get plans error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get subscription plans",
    });
  }
});

// Get user's current subscription
router.get("/current", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const subscription = await UserSubscription.getActiveSubscription(userId);

    if (!subscription) {
      return res.json({
        success: true,
        data: {
          subscription: null,
          message: "No active subscription found",
        },
      });
    }

    res.json({
      success: true,
      data: {
        subscription: {
          id: subscription._id,
          plan: subscription.plan,
          status: subscription.status,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          daysRemaining: subscription.daysRemaining,
          autoRenew: subscription.autoRenew,
          usage: subscription.usage,
          isActive: subscription.isActive,
        },
      },
    });
  } catch (error) {
    console.error("Get current subscription error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get current subscription",
    });
  }
});

// Create payment order
router.post(
  "/create-order",
  [
    authenticateToken,
    body("planId").isMongoId().withMessage("Invalid plan ID"),
    body("couponCode")
      .optional()
      .isLength({ min: 1 })
      .withMessage("Invalid coupon code"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { planId, couponCode } = req.body;
      const userId = req.user._id;

      // Get plan details
      const plan = await SubscriptionPlan.findById(planId);
      if (!plan || !plan.isActive) {
        return res.status(404).json({
          success: false,
          message: "Subscription plan not found",
        });
      }

      // Check if user already has an active subscription
      const existingSubscription = await UserSubscription.getActiveSubscription(
        userId
      );
      if (existingSubscription) {
        return res.status(400).json({
          success: false,
          message: "You already have an active subscription",
        });
      }

      // Calculate final amount (apply discounts, coupons, etc.)
      let finalAmount = plan.price;
      let discountApplied = 0;

      // Apply plan discount
      if (plan.discountPercentage > 0) {
        discountApplied = (plan.price * plan.discountPercentage) / 100;
        finalAmount = plan.price - discountApplied;
      }

      // Apply coupon code (implement coupon logic here)
      if (couponCode) {
        // Coupon validation and discount application logic
        // For now, just a placeholder
      }

      // Check if Razorpay is configured
      if (!razorpay) {
        return res.status(500).json({
          success: false,
          message: "Payment gateway not configured",
        });
      }

      // Create Razorpay order
      const orderOptions = {
        amount: Math.round(finalAmount * 100), // Amount in paise
        currency: "INR",
        receipt: `order_${userId}_${Date.now()}`,
        notes: {
          userId: userId.toString(),
          planId: planId.toString(),
          planName: plan.name,
        },
      };

      const order = await razorpay.orders.create(orderOptions);

      // Create pending subscription record
      const subscription = new UserSubscription({
        user: userId,
        plan: planId,
        status: "pending",
        amountPaid: finalAmount,
        discountApplied,
        couponCode,
        orderId: order.id,
        paymentMethod: "razorpay",
      });

      await subscription.save();

      res.json({
        success: true,
        data: {
          order: {
            id: order.id,
            amount: order.amount,
            currency: order.currency,
            receipt: order.receipt,
          },
          subscription: {
            id: subscription._id,
            planName: plan.name,
            originalAmount: plan.price,
            finalAmount,
            discountApplied,
          },
          razorpayKeyId: process.env.RAZORPAY_KEY_ID,
        },
      });
    } catch (error) {
      console.error("Create order error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to create payment order",
      });
    }
  }
);

// Verify payment and activate subscription
router.post(
  "/verify-payment",
  [
    authenticateToken,
    body("razorpay_order_id").notEmpty().withMessage("Order ID is required"),
    body("razorpay_payment_id")
      .notEmpty()
      .withMessage("Payment ID is required"),
    body("razorpay_signature").notEmpty().withMessage("Signature is required"),
    body("subscriptionId").isMongoId().withMessage("Invalid subscription ID"),
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const {
        razorpay_order_id,
        razorpay_payment_id,
        razorpay_signature,
        subscriptionId,
      } = req.body;

      const userId = req.user._id;

      // Check if Razorpay is configured
      if (!razorpay) {
        return res.status(500).json({
          success: false,
          message: "Payment gateway not configured",
        });
      }

      // Get subscription
      const subscription = await UserSubscription.findById(
        subscriptionId
      ).populate("plan");
      if (!subscription || subscription.user.toString() !== userId.toString()) {
        return res.status(404).json({
          success: false,
          message: "Subscription not found",
        });
      }

      // Verify payment signature
      const crypto = require("crypto");
      const expectedSignature = crypto
        .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET)
        .update(`${razorpay_order_id}|${razorpay_payment_id}`)
        .digest("hex");

      if (expectedSignature !== razorpay_signature) {
        subscription.status = "failed";
        await subscription.save();

        return res.status(400).json({
          success: false,
          message: "Payment verification failed",
        });
      }

      // Get payment details from Razorpay
      const payment = await razorpay.payments.fetch(razorpay_payment_id);

      // Update subscription
      subscription.status = "active";
      subscription.paymentId = razorpay_payment_id;
      subscription.transactionId = payment.id;
      subscription.startDate = new Date();
      subscription.endDate = new Date(
        Date.now() + subscription.plan.duration * 30 * 24 * 60 * 60 * 1000
      );
      subscription.paymentDetails = {
        gateway: "razorpay",
        gatewayTransactionId: payment.id,
        gatewayOrderId: razorpay_order_id,
        paymentStatus: payment.status,
        paymentDate: new Date(payment.created_at * 1000),
      };

      await subscription.save();

      // Update user membership type and premium features
      const user = await User.findById(userId);
      user.membershipType = subscription.plan.name;
      user.subscriptionExpiry = subscription.endDate;
      user.premiumFeatures = subscription.plan.premiumFeatures;

      await user.save();

      res.json({
        success: true,
        message: "Payment verified and subscription activated successfully",
        data: {
          subscription: {
            id: subscription._id,
            plan: subscription.plan,
            status: subscription.status,
            startDate: subscription.startDate,
            endDate: subscription.endDate,
            daysRemaining: subscription.daysRemaining,
          },
        },
      });
    } catch (error) {
      console.error("Verify payment error:", error);
      res.status(500).json({
        success: false,
        message: "Payment verification failed",
      });
    }
  }
);

// Get subscription history
router.get("/history", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const subscriptions = await UserSubscription.find({ user: userId })
      .populate("plan", "name displayName price duration")
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: {
        subscriptions,
      },
    });
  } catch (error) {
    console.error("Get subscription history error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get subscription history",
    });
  }
});

// Cancel subscription
router.put(
  "/cancel",
  [
    authenticateToken,
    body("reason")
      .optional()
      .isLength({ max: 500 })
      .withMessage("Reason too long"),
  ],
  async (req, res) => {
    try {
      const { reason } = req.body;
      const userId = req.user._id;

      const subscription = await UserSubscription.getActiveSubscription(userId);
      if (!subscription) {
        return res.status(404).json({
          success: false,
          message: "No active subscription found",
        });
      }

      subscription.status = "cancelled";
      subscription.autoRenew = false;
      subscription.cancellationReason = reason;
      subscription.cancelledAt = new Date();
      subscription.cancelledBy = userId;

      await subscription.save();

      // Update user membership (keep premium features until expiry)
      const user = await User.findById(userId);
      // Don't immediately downgrade, let it expire naturally

      res.json({
        success: true,
        message:
          "Subscription cancelled successfully. Premium features will remain active until expiry.",
        data: {
          subscription: {
            id: subscription._id,
            status: subscription.status,
            endDate: subscription.endDate,
            daysRemaining: subscription.daysRemaining,
          },
        },
      });
    } catch (error) {
      console.error("Cancel subscription error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to cancel subscription",
      });
    }
  }
);

// Get usage statistics
router.get("/usage", authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const subscription = await UserSubscription.getActiveSubscription(userId);
    if (!subscription) {
      return res.json({
        success: true,
        data: {
          usage: null,
          message: "No active subscription found",
        },
      });
    }

    const usage = subscription.usage;
    const limits = subscription.plan.limits;

    const usageData = {
      profileViews: {
        used: usage.profileViews,
        limit: limits.profileViews,
        unlimited: limits.profileViews === -1,
      },
      contactViews: {
        used: usage.contactViews,
        limit: limits.contactViews,
        unlimited: limits.contactViews === -1,
      },
      messagesSent: {
        used: usage.messagesSent,
        limit: limits.messagesPerDay,
        unlimited: limits.messagesPerDay === -1,
      },
      interestsSent: {
        used: usage.interestsSent,
        limit: limits.interestsPerDay,
        unlimited: limits.interestsPerDay === -1,
      },
      searchesPerformed: {
        used: usage.searchesPerformed,
        limit: limits.searchFilters,
        unlimited: limits.searchFilters === -1,
      },
      lastResetDate: usage.lastResetDate,
    };

    res.json({
      success: true,
      data: {
        usage: usageData,
      },
    });
  } catch (error) {
    console.error("Get usage error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get usage statistics",
    });
  }
});

module.exports = router;
