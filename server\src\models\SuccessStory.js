const mongoose = require('mongoose');

const successStorySchema = new mongoose.Schema({
  couple: {
    bride: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    groom: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  },
  coupleNames: {
    type: String,
    required: true
  },
  story: {
    type: String,
    required: true,
    maxlength: 2000
  },
  testimonial: {
    type: String,
    maxlength: 500
  },
  marriageDate: {
    type: Date,
    required: true
  },
  weddingLocation: {
    city: String,
    state: String,
    country: String
  },
  photos: [{
    url: String,
    caption: String,
    isMain: {
      type: Boolean,
      default: false
    }
  }],
  howTheyMet: {
    type: String,
    enum: ['through_platform', 'family_introduction', 'friends', 'other'],
    default: 'through_platform'
  },
  timelineEvents: [{
    event: String,
    date: Date,
    description: String
  }],
  isPublic: {
    type: Boolean,
    default: false
  },
  isApproved: {
    type: Boolean,
    default: false
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  approvedAt: Date,
  submittedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  views: {
    type: Number,
    default: 0
  },
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    likedAt: {
      type: Date,
      default: Date.now
    }
  }],
  comments: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    comment: {
      type: String,
      maxlength: 500
    },
    isApproved: {
      type: Boolean,
      default: false
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  tags: [String],
  featured: {
    type: Boolean,
    default: false
  },
  featuredUntil: Date
}, {
  timestamps: true
});

// Indexes
successStorySchema.index({ isPublic: 1, isApproved: 1 });
successStorySchema.index({ marriageDate: -1 });
successStorySchema.index({ featured: 1, featuredUntil: 1 });
successStorySchema.index({ views: -1 });
successStorySchema.index({ 'likes.user': 1 });

// Virtual for like count
successStorySchema.virtual('likeCount').get(function() {
  return this.likes.length;
});

// Virtual for comment count
successStorySchema.virtual('commentCount').get(function() {
  return this.comments.filter(comment => comment.isApproved).length;
});

// Method to check if user has liked the story
successStorySchema.methods.isLikedBy = function(userId) {
  return this.likes.some(like => like.user.toString() === userId.toString());
};

// Method to add like
successStorySchema.methods.addLike = function(userId) {
  if (!this.isLikedBy(userId)) {
    this.likes.push({ user: userId });
  }
  return this.save();
};

// Method to remove like
successStorySchema.methods.removeLike = function(userId) {
  this.likes = this.likes.filter(like => like.user.toString() !== userId.toString());
  return this.save();
};

// Method to increment views
successStorySchema.methods.incrementViews = function() {
  this.views += 1;
  return this.save();
};

// Static method to get featured stories
successStorySchema.statics.getFeaturedStories = function(limit = 5) {
  return this.find({
    featured: true,
    isPublic: true,
    isApproved: true,
    $or: [
      { featuredUntil: { $exists: false } },
      { featuredUntil: { $gte: new Date() } }
    ]
  })
  .populate('couple.bride couple.groom', 'name personalInfo.city')
  .sort({ createdAt: -1 })
  .limit(limit);
};

// Static method to get recent stories
successStorySchema.statics.getRecentStories = function(limit = 10) {
  return this.find({
    isPublic: true,
    isApproved: true
  })
  .populate('couple.bride couple.groom', 'name personalInfo.city')
  .sort({ marriageDate: -1 })
  .limit(limit);
};

// Static method to get popular stories
successStorySchema.statics.getPopularStories = function(limit = 10) {
  return this.find({
    isPublic: true,
    isApproved: true
  })
  .populate('couple.bride couple.groom', 'name personalInfo.city')
  .sort({ views: -1, likeCount: -1 })
  .limit(limit);
};

module.exports = mongoose.model('SuccessStory', successStorySchema);
