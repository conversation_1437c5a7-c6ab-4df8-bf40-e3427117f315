const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');

// Configure multer for horoscope file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../../uploads/horoscopes');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `horoscope-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and PDF are allowed.'));
    }
  }
});

// Upload horoscope
router.post('/upload', authenticateToken, upload.single('horoscope'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No horoscope file uploaded'
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update user's horoscope information
    const horoscopeInfo = user.horoscopeInfo || {};
    horoscopeInfo.fileName = req.file.filename;
    horoscopeInfo.filePath = `/uploads/horoscopes/${req.file.filename}`;
    horoscopeInfo.fileSize = req.file.size;
    horoscopeInfo.mimeType = req.file.mimetype;
    horoscopeInfo.uploadedAt = new Date().toISOString();
    horoscopeInfo.isVerified = false;

    user.horoscopeInfo = horoscopeInfo;
    user.calculateProfileCompletion();
    await user.save();

    res.json({
      success: true,
      message: 'Horoscope uploaded successfully',
      data: { horoscopeInfo }
    });

  } catch (error) {
    console.error('Horoscope upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload horoscope'
    });
  }
});

// Get user's horoscope
router.get('/', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { horoscopeInfo: user.horoscopeInfo || {} }
    });

  } catch (error) {
    console.error('Get horoscope error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get horoscope'
    });
  }
});

// Update horoscope details
router.put('/details', authenticateToken, [
  body('birthTime').optional().isLength({ min: 1, max: 10 }),
  body('birthPlace').optional().isLength({ min: 2, max: 100 }),
  body('rashi').optional().isLength({ min: 2, max: 50 }),
  body('nakshatra').optional().isLength({ min: 2, max: 50 }),
  body('gotra').optional().isLength({ min: 2, max: 50 }),
  body('manglikStatus').optional().isIn(['yes', 'no', 'anshik', 'dont_know'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update horoscope details
    const horoscopeInfo = { ...user.horoscopeInfo, ...req.body };
    user.horoscopeInfo = horoscopeInfo;
    
    // Update religious info if manglik status is provided
    if (req.body.manglikStatus) {
      const religiousInfo = { ...user.religiousInfo };
      religiousInfo.manglikStatus = req.body.manglikStatus;
      user.religiousInfo = religiousInfo;
    }
    
    user.calculateProfileCompletion();
    await user.save();

    res.json({
      success: true,
      message: 'Horoscope details updated successfully',
      data: { horoscopeInfo: user.horoscopeInfo }
    });

  } catch (error) {
    console.error('Update horoscope details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update horoscope details'
    });
  }
});

// Check horoscope compatibility
router.post('/compatibility', authenticateToken, [
  body('otherUserId').notEmpty().withMessage('Other user ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { otherUserId } = req.body;
    const currentUserId = req.user.id;

    if (currentUserId === otherUserId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot check compatibility with yourself'
      });
    }

    // Get both users
    const [currentUser, otherUser] = await Promise.all([
      User.findByPk(currentUserId),
      User.findByPk(otherUserId)
    ]);

    if (!currentUser || !otherUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const currentHoroscope = currentUser.horoscopeInfo || {};
    const otherHoroscope = otherUser.horoscopeInfo || {};
    const currentReligious = currentUser.religiousInfo || {};
    const otherReligious = otherUser.religiousInfo || {};

    // Basic compatibility calculation
    let compatibilityScore = 50; // Base score
    const compatibilityFactors = [];

    // Manglik compatibility
    if (currentReligious.manglikStatus && otherReligious.manglikStatus) {
      if (currentReligious.manglikStatus === otherReligious.manglikStatus) {
        compatibilityScore += 15;
        compatibilityFactors.push({
          factor: 'Manglik Status',
          score: 15,
          description: 'Both have same Manglik status'
        });
      } else if (
        (currentReligious.manglikStatus === 'anshik' && otherReligious.manglikStatus === 'yes') ||
        (currentReligious.manglikStatus === 'yes' && otherReligious.manglikStatus === 'anshik')
      ) {
        compatibilityScore += 8;
        compatibilityFactors.push({
          factor: 'Manglik Status',
          score: 8,
          description: 'Partial Manglik compatibility'
        });
      } else {
        compatibilityScore -= 10;
        compatibilityFactors.push({
          factor: 'Manglik Status',
          score: -10,
          description: 'Manglik mismatch'
        });
      }
    }

    // Rashi compatibility (simplified)
    if (currentHoroscope.rashi && otherHoroscope.rashi) {
      if (currentHoroscope.rashi === otherHoroscope.rashi) {
        compatibilityScore += 10;
        compatibilityFactors.push({
          factor: 'Rashi',
          score: 10,
          description: 'Same Rashi'
        });
      }
    }

    // Nakshatra compatibility (simplified)
    if (currentHoroscope.nakshatra && otherHoroscope.nakshatra) {
      if (currentHoroscope.nakshatra === otherHoroscope.nakshatra) {
        compatibilityScore += 8;
        compatibilityFactors.push({
          factor: 'Nakshatra',
          score: 8,
          description: 'Same Nakshatra'
        });
      }
    }

    // Gotra compatibility
    if (currentHoroscope.gotra && otherHoroscope.gotra) {
      if (currentHoroscope.gotra === otherHoroscope.gotra) {
        compatibilityScore -= 20; // Same gotra is not preferred
        compatibilityFactors.push({
          factor: 'Gotra',
          score: -20,
          description: 'Same Gotra (not recommended)'
        });
      } else {
        compatibilityScore += 5;
        compatibilityFactors.push({
          factor: 'Gotra',
          score: 5,
          description: 'Different Gotra (good)'
        });
      }
    }

    // Ensure score is within bounds
    compatibilityScore = Math.max(0, Math.min(100, compatibilityScore));

    // Determine compatibility level
    let compatibilityLevel = 'Poor';
    if (compatibilityScore >= 80) compatibilityLevel = 'Excellent';
    else if (compatibilityScore >= 65) compatibilityLevel = 'Very Good';
    else if (compatibilityScore >= 50) compatibilityLevel = 'Good';
    else if (compatibilityScore >= 35) compatibilityLevel = 'Average';

    const compatibility = {
      score: compatibilityScore,
      level: compatibilityLevel,
      factors: compatibilityFactors,
      recommendation: compatibilityScore >= 65 ? 
        'Highly compatible match. Consider proceeding.' :
        compatibilityScore >= 50 ?
        'Moderately compatible. Consult astrologer for detailed analysis.' :
        'Low compatibility. Detailed horoscope matching recommended.',
      checkedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: { compatibility }
    });

  } catch (error) {
    console.error('Horoscope compatibility error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check horoscope compatibility'
    });
  }
});

// Get horoscope matching guidelines
router.get('/guidelines', async (req, res) => {
  try {
    const guidelines = {
      manglikMatching: {
        title: 'Manglik Matching',
        description: 'Manglik individuals should ideally marry other Mangliks',
        rules: [
          'Manglik + Manglik = Excellent match',
          'Manglik + Anshik Manglik = Good match',
          'Manglik + Non-Manglik = Requires remedies'
        ]
      },
      gunaMatching: {
        title: 'Guna Matching (Ashtakoot)',
        description: 'Traditional 36-point compatibility system',
        points: [
          'Varna (1 point) - Caste compatibility',
          'Vashya (2 points) - Mutual attraction',
          'Tara (3 points) - Health and well-being',
          'Yoni (4 points) - Sexual compatibility',
          'Graha Maitri (5 points) - Mental compatibility',
          'Gana (6 points) - Temperament',
          'Bhakoot (7 points) - Love and affection',
          'Nadi (8 points) - Health of progeny'
        ],
        scoring: [
          '32-36 points: Excellent match',
          '25-31 points: Very good match',
          '18-24 points: Average match',
          'Below 18 points: Not recommended'
        ]
      },
      doshaAnalysis: {
        title: 'Dosha Analysis',
        description: 'Important doshas to consider',
        doshas: [
          'Manglik Dosha - Mars placement',
          'Kaal Sarp Dosha - Rahu-Ketu axis',
          'Shani Dosha - Saturn effects',
          'Pitra Dosha - Ancestral effects'
        ]
      }
    };

    res.json({
      success: true,
      data: { guidelines }
    });

  } catch (error) {
    console.error('Get horoscope guidelines error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get horoscope guidelines'
    });
  }
});

module.exports = router;
