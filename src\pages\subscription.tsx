// This file is missing, creating it now
import React from 'react';
import { 
  <PERSON>, 
  CardBody, 
  CardHeader, 
  Card<PERSON>ooter,
  <PERSON><PERSON>, 
  Divider,
  Chip,
  RadioGroup,
  Radio
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const SubscriptionPage: React.FC = () => {
  const { user } = useAuth();
  const [selectedPlan, setSelectedPlan] = React.useState('gold');
  const [duration, setDuration] = React.useState('3');
  
  // Mock subscription plans
  const plans = [
    {
      id: 'silver',
      name: 'Silver',
      price: 999,
      features: [
        'View contact details',
        'Send unlimited interests',
        'Advanced search filters',
        'View who visited your profile',
      ],
      popular: false,
    },
    {
      id: 'gold',
      name: 'Gold',
      price: 1999,
      features: [
        'All Silver features',
        'Priority listing in search results',
        'Send direct messages',
        'View horoscope details',
        'See when your messages are read',
      ],
      popular: true,
    },
    {
      id: 'platinum',
      name: 'Platinum',
      price: 3999,
      features: [
        'All Gold features',
        'Dedicated relationship manager',
        'Profile highlighted in search results',
        'Video calling feature',
        'Background verification of matches',
        'Personalized matchmaking',
      ],
      popular: false,
    },
  ];
  
  const durations = [
    { id: '1', name: '1 Month', multiplier: 1 },
    { id: '3', name: '3 Months', multiplier: 2.5, discount: '17%' },
    { id: '6', name: '6 Months', multiplier: 4.5, discount: '25%' },
    { id: '12', name: '12 Months', multiplier: 8, discount: '33%' },
  ];
  
  const selectedPlanData = plans.find(plan => plan.id === selectedPlan);
  const selectedDurationData = durations.find(d => d.id === duration);
  
  const calculatePrice = () => {
    if (!selectedPlanData || !selectedDurationData) return 0;
    return selectedPlanData.price * selectedDurationData.multiplier;
  };
  
  const handleSubscribe = () => {
    console.log(`Subscribing to ${selectedPlan} plan for ${duration} months`);
    // Implement subscription logic
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Upgrade Membership</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Current Plan */}
          <div className="lg:col-span-1">
            <Card className="mb-6">
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:info" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Current Plan</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody>
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold capitalize">{user?.membershipType}</h3>
                    <p className="text-default-500 text-sm">
                      {user?.membershipType === 'free' ? 'Basic membership' : 'Premium membership'}
                    </p>
                  </div>
                  <Chip 
                    color={user?.membershipType === 'free' ? 'default' : 'primary'} 
                    variant="flat"
                  >
                    {user?.membershipType === 'free' ? 'Free' : 'Active'}
                  </Chip>
                </div>
                
                {user?.membershipType !== 'free' && (
                  <div className="mb-4">
                    <p className="text-sm text-default-500">Valid till</p>
                    <p className="font-medium">June 30, 2023</p>
                  </div>
                )}
                
                <div>
                  <p className="text-sm font-medium mb-2">Features included:</p>
                  <ul className="space-y-2">
                    <li className="flex items-center gap-2 text-sm">
                      <Icon icon="lucide:check" className="text-success" />
                      <span>Create your profile</span>
                    </li>
                    <li className="flex items-center gap-2 text-sm">
                      <Icon icon="lucide:check" className="text-success" />
                      <span>Browse profiles</span>
                    </li>
                    <li className="flex items-center gap-2 text-sm">
                      <Icon icon="lucide:check" className="text-success" />
                      <span>Receive interests</span>
                    </li>
                    {user?.membershipType === 'free' ? (
                      <li className="flex items-center gap-2 text-sm text-default-400">
                        <Icon icon="lucide:x" className="text-danger" />
                        <span>Send interests (Premium only)</span>
                      </li>
                    ) : (
                      <li className="flex items-center gap-2 text-sm">
                        <Icon icon="lucide:check" className="text-success" />
                        <span>Send unlimited interests</span>
                      </li>
                    )}
                  </ul>
                </div>
              </CardBody>
              {user?.membershipType !== 'free' && (
                <CardFooter>
                  <Button 
                    color="danger" 
                    variant="flat" 
                    className="w-full"
                  >
                    Cancel Subscription
                  </Button>
                </CardFooter>
              )}
            </Card>
            
            <Card>
              <CardHeader className="flex gap-3">
                <Icon icon="lucide:help-circle" className="text-primary text-xl" />
                <div className="flex flex-col">
                  <p className="text-md font-semibold">Need Help?</p>
                </div>
              </CardHeader>
              <Divider />
              <CardBody>
                <p className="text-sm mb-4">
                  If you have any questions about our subscription plans or need assistance, our customer support team is here to help.
                </p>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:phone" className="text-primary" />
                    <span className="text-sm">+91 1800-123-4567</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:mail" className="text-primary" />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Icon icon="lucide:message-circle" className="text-primary" />
                    <span className="text-sm">Live Chat (9 AM - 9 PM)</span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>
          
          {/* Right Column - Subscription Plans */}
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardHeader>
                <h2 className="text-lg font-semibold">Choose a Plan</h2>
              </CardHeader>
              <Divider />
              <CardBody>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {plans.map((plan) => (
                    <Card 
                      key={plan.id} 
                      isPressable 
                      isHoverable
                      className={`border-2 ${selectedPlan === plan.id ? 'border-primary' : 'border-transparent'}`}
                      onPress={() => setSelectedPlan(plan.id)}
                    >
                      <CardBody className="relative">
                        {plan.popular && (
                          <div className="absolute top-0 right-0">
                            <Chip color="warning" size="sm" variant="solid">
                              Popular
                            </Chip>
                          </div>
                        )}
                        <h3 className="text-xl font-semibold text-center mb-2">{plan.name}</h3>
                        <div className="text-center mb-4">
                          <p className="text-2xl font-bold">₹{plan.price}</p>
                          <p className="text-xs text-default-500">per month</p>
                        </div>
                        <Divider className="my-3" />
                        <ul className="space-y-2">
                          {plan.features.map((feature, index) => (
                            <li key={index} className="flex items-center gap-2 text-sm">
                              <Icon icon="lucide:check" className="text-success" />
                              <span>{feature}</span>
                            </li>
                          ))}
                        </ul>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              </CardBody>
            </Card>
            
            <Card className="mb-6">
              <CardHeader>
                <h2 className="text-lg font-semibold">Choose Duration</h2>
              </CardHeader>
              <Divider />
              <CardBody>
                <RadioGroup 
                  orientation="horizontal" 
                  value={duration}
                  onValueChange={setDuration}
                >
                  {durations.map((d) => (
                    <div key={d.id} className="flex-1">
                      <Radio 
                        value={d.id}
                        description={d.discount ? `Save ${d.discount}` : null}
                        classNames={{
                          base: "max-w-full",
                        }}
                      >
                        {d.name}
                      </Radio>
                    </div>
                  ))}
                </RadioGroup>
              </CardBody>
            </Card>
            
            <Card>
              <CardHeader>
                <h2 className="text-lg font-semibold">Order Summary</h2>
              </CardHeader>
              <Divider />
              <CardBody>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>{selectedPlanData?.name} Plan ({selectedDurationData?.name})</span>
                    <span>₹{calculatePrice()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>GST (18%)</span>
                    <span>₹{Math.round(calculatePrice() * 0.18)}</span>
                  </div>
                  <Divider />
                  <div className="flex justify-between font-semibold">
                    <span>Total</span>
                    <span>₹{calculatePrice() + Math.round(calculatePrice() * 0.18)}</span>
                  </div>
                </div>
              </CardBody>
              <Divider />
              <CardFooter>
                <Button 
                  color="primary" 
                  className="w-full"
                  onPress={handleSubscribe}
                >
                  Proceed to Payment
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};