import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Progress,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Image,
  Chip,
  Badge,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  Dropdown<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ert
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { userAPI } from '../services/api';
import { Photo } from '../types/matrimony';

export const PhotoManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [photos, setPhotos] = React.useState<Photo[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [uploading, setUploading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [selectedFiles, setSelectedFiles] = React.useState<FileList | null>(null);
  const [previewUrls, setPreviewUrls] = React.useState<string[]>([]);
  const [deletingPhoto, setDeletingPhoto] = React.useState<string | null>(null);

  const fileInputRef = React.useRef<HTMLInputElement>(null);

  // Load user photos
  React.useEffect(() => {
    const loadPhotos = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const response = await userAPI.getPhotos();
        
        if (response.success) {
          setPhotos(response.data.photos);
        } else {
          setError(response.message || 'Failed to load photos');
        }
      } catch (err: any) {
        console.error('Load photos error:', err);
        setError(err.response?.data?.message || 'Failed to load photos');
      } finally {
        setLoading(false);
      }
    };
    
    loadPhotos();
  }, [user]);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    // Validate files
    const validFiles: File[] = [];
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!allowedTypes.includes(file.type)) {
        setError(`${file.name} is not a supported image format`);
        continue;
      }
      
      if (file.size > maxSize) {
        setError(`${file.name} is too large. Maximum size is 5MB`);
        continue;
      }
      
      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    // Create file list
    const dt = new DataTransfer();
    validFiles.forEach(file => dt.items.add(file));
    setSelectedFiles(dt.files);

    // Create preview URLs
    const urls = validFiles.map(file => URL.createObjectURL(file));
    setPreviewUrls(urls);
    
    onOpen();
  };

  // Upload photos
  const handleUpload = async () => {
    if (!selectedFiles) return;

    setUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      for (let i = 0; i < selectedFiles.length; i++) {
        formData.append('photos', selectedFiles[i]);
      }

      const response = await userAPI.uploadPhotos(formData);
      
      if (response.success) {
        setPhotos(prev => [...prev, ...response.data.photos]);
        setSelectedFiles(null);
        setPreviewUrls([]);
        onClose();
        
        // Update user profile completion
        if (user) {
          updateUser({ ...user, profileCompletionPercentage: user.profileCompletionPercentage + 10 });
        }
      } else {
        setError(response.message || 'Upload failed');
      }
    } catch (err: any) {
      console.error('Upload error:', err);
      setError(err.response?.data?.message || 'Upload failed');
    } finally {
      setUploading(false);
    }
  };

  // Delete photo
  const handleDeletePhoto = async (photoId: string) => {
    try {
      setDeletingPhoto(photoId);
      
      const response = await userAPI.deletePhoto(photoId);
      
      if (response.success) {
        setPhotos(prev => prev.filter(p => p.id !== photoId));
      } else {
        setError(response.message || 'Failed to delete photo');
      }
    } catch (err: any) {
      console.error('Delete photo error:', err);
      setError(err.response?.data?.message || 'Failed to delete photo');
    } finally {
      setDeletingPhoto(null);
    }
  };

  // Set as profile picture
  const handleSetProfilePicture = async (photoId: string) => {
    try {
      const response = await userAPI.setProfilePicture(photoId);
      
      if (response.success) {
        setPhotos(prev => prev.map(p => ({
          ...p,
          isProfilePicture: p.id === photoId
        })));
      } else {
        setError(response.message || 'Failed to set profile picture');
      }
    } catch (err: any) {
      console.error('Set profile picture error:', err);
      setError(err.response?.data?.message || 'Failed to set profile picture');
    }
  };

  // Change photo visibility
  const handleChangeVisibility = async (photoId: string, visibility: string) => {
    try {
      const response = await userAPI.updatePhotoVisibility(photoId, visibility);
      
      if (response.success) {
        setPhotos(prev => prev.map(p => 
          p.id === photoId ? { ...p, visibility: visibility as any } : p
        ));
      } else {
        setError(response.message || 'Failed to update visibility');
      }
    } catch (err: any) {
      console.error('Update visibility error:', err);
      setError(err.response?.data?.message || 'Failed to update visibility');
    }
  };

  const getVisibilityColor = (visibility: string) => {
    switch (visibility) {
      case 'public': return 'success';
      case 'premium_only': return 'warning';
      case 'mutual_interest_only': return 'primary';
      case 'private': return 'danger';
      default: return 'default';
    }
  };

  const getVisibilityIcon = (visibility: string) => {
    switch (visibility) {
      case 'public': return 'lucide:globe';
      case 'premium_only': return 'lucide:crown';
      case 'mutual_interest_only': return 'lucide:heart';
      case 'private': return 'lucide:lock';
      default: return 'lucide:eye';
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to manage your photos.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Photo Management</h1>
            <p className="text-default-500">
              Upload and manage your profile photos. First photo will be your profile picture.
            </p>
          </div>
          
          <Button
            color="primary"
            startContent={<Icon icon="lucide:upload" />}
            onPress={() => fileInputRef.current?.click()}
            isDisabled={photos.length >= 10}
          >
            Upload Photos
          </Button>
        </div>

        {/* Photo Guidelines */}
        <Card className="mb-6">
          <CardBody>
            <div className="flex items-start gap-3">
              <Icon icon="lucide:info" className="text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-2">Photo Guidelines</h3>
                <ul className="text-sm text-default-600 space-y-1">
                  <li>• Upload clear, recent photos of yourself</li>
                  <li>• Maximum 10 photos allowed</li>
                  <li>• Supported formats: JPEG, PNG, GIF, WebP</li>
                  <li>• Maximum file size: 5MB per photo</li>
                  <li>• First photo will be your main profile picture</li>
                  <li>• Avoid group photos, sunglasses, or inappropriate content</li>
                </ul>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Progress */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium">Photos uploaded</span>
            <span className="text-sm text-default-500">{photos.length}/10</span>
          </div>
          <Progress 
            value={(photos.length / 10) * 100} 
            color="primary" 
            className="h-2"
          />
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          color="danger"
          variant="flat"
          className="mb-6"
          startContent={<Icon icon="lucide:alert-circle" />}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <Spinner size="lg" color="primary" />
            <p className="mt-4 text-default-500">Loading photos...</p>
          </div>
        </div>
      )}

      {/* Photos Grid */}
      {!loading && (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {photos.map((photo, index) => (
            <Card key={photo.id} className="relative group">
              <CardBody className="p-0">
                <div className="relative">
                  <Image
                    src={photo.filePath}
                    alt={`Photo ${index + 1}`}
                    className="w-full h-48 object-cover"
                    fallback={
                      <div className="w-full h-48 bg-default-100 flex items-center justify-center">
                        <Icon icon="lucide:image" size={32} className="text-default-400" />
                      </div>
                    }
                  />
                  
                  {/* Profile Picture Badge */}
                  {photo.isProfilePicture && (
                    <Badge
                      content="Profile"
                      color="primary"
                      placement="top-left"
                      className="absolute top-2 left-2"
                    />
                  )}
                  
                  {/* Verification Badge */}
                  {photo.isVerified && (
                    <Badge
                      content={<Icon icon="lucide:check" className="text-white" size={12} />}
                      color="success"
                      placement="top-right"
                      className="absolute top-2 right-2"
                    />
                  )}
                  
                  {/* Visibility Chip */}
                  <Chip
                    size="sm"
                    color={getVisibilityColor(photo.visibility)}
                    variant="flat"
                    startContent={<Icon icon={getVisibilityIcon(photo.visibility)} size={12} />}
                    className="absolute bottom-2 left-2"
                  >
                    {photo.visibility.replace('_', ' ')}
                  </Chip>
                  
                  {/* Action Buttons */}
                  <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                    {!photo.isProfilePicture && (
                      <Button
                        size="sm"
                        color="primary"
                        variant="solid"
                        onPress={() => handleSetProfilePicture(photo.id)}
                      >
                        Set as Profile
                      </Button>
                    )}
                    
                    <Dropdown>
                      <DropdownTrigger>
                        <Button size="sm" variant="solid">
                          <Icon icon="lucide:settings" />
                        </Button>
                      </DropdownTrigger>
                      <DropdownMenu onAction={(key) => handleChangeVisibility(photo.id, key as string)}>
                        <DropdownItem key="public" startContent={<Icon icon="lucide:globe" />}>
                          Public
                        </DropdownItem>
                        <DropdownItem key="premium_only" startContent={<Icon icon="lucide:crown" />}>
                          Premium Only
                        </DropdownItem>
                        <DropdownItem key="mutual_interest_only" startContent={<Icon icon="lucide:heart" />}>
                          Mutual Interest Only
                        </DropdownItem>
                        <DropdownItem key="private" startContent={<Icon icon="lucide:lock" />}>
                          Private
                        </DropdownItem>
                      </DropdownMenu>
                    </Dropdown>
                    
                    <Button
                      size="sm"
                      color="danger"
                      variant="solid"
                      onPress={() => handleDeletePhoto(photo.id)}
                      isLoading={deletingPhoto === photo.id}
                    >
                      <Icon icon="lucide:trash-2" />
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>
          ))}
          
          {/* Upload Placeholder */}
          {photos.length < 10 && (
            <Card 
              className="border-2 border-dashed border-default-300 hover:border-primary cursor-pointer transition-colors"
              isPressable
              onPress={() => fileInputRef.current?.click()}
            >
              <CardBody className="flex items-center justify-center h-48">
                <div className="text-center">
                  <Icon icon="lucide:plus" size={32} className="text-default-400 mb-2" />
                  <p className="text-sm text-default-500">Add Photo</p>
                </div>
              </CardBody>
            </Card>
          )}
        </div>
      )}

      {/* Empty State */}
      {!loading && photos.length === 0 && (
        <div className="text-center py-12">
          <Icon icon="lucide:camera" size={64} className="mx-auto mb-4 text-default-400" />
          <h3 className="text-xl font-semibold mb-2">No Photos Yet</h3>
          <p className="text-default-500 mb-6">
            Upload your first photo to get started. A good profile picture increases your chances of finding matches.
          </p>
          <Button
            color="primary"
            size="lg"
            startContent={<Icon icon="lucide:upload" />}
            onPress={() => fileInputRef.current?.click()}
          >
            Upload Your First Photo
          </Button>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        className="hidden"
        onChange={handleFileSelect}
      />

      {/* Upload Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalContent>
          <ModalHeader>Upload Photos</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <p className="text-default-600">
                Selected {selectedFiles?.length || 0} photo(s) for upload
              </p>
              
              {previewUrls.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {previewUrls.map((url, index) => (
                    <div key={index} className="relative">
                      <Image
                        src={url}
                        alt={`Preview ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      {index === 0 && photos.length === 0 && (
                        <Badge
                          content="Profile"
                          color="primary"
                          placement="top-left"
                          className="absolute top-1 left-1"
                        />
                      )}
                    </div>
                  ))}
                </div>
              )}
              
              {error && (
                <Alert color="danger" variant="flat">
                  {error}
                </Alert>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleUpload}
              isLoading={uploading}
              isDisabled={!selectedFiles}
            >
              Upload Photos
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
