const express = require("express");
const cors = require("cors");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const path = require("path");
const fs = require("fs");
require("dotenv").config();

// Import security middleware
const {
  rateLimits,
  securityHeaders,
  sanitizeRequest,
  detectSuspiciousActivity,
  corsOptions,
  securityLogger,
} = require("./src/middleware/security");

// Import SQLite database
const SQLiteDatabase = require("./src/database/sqlite-setup");

const app = express();

// Initialize database
const db = new SQLiteDatabase();

// Security middleware
app.use(securityHeaders);
app.use(securityLogger);
app.use(detectSuspiciousActivity);
app.use(sanitizeRequest);

// CORS
app.use(cors(corsOptions));

// Rate limiting
app.use(rateLimits.general);

// Body parsing
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true, limit: "10mb" }));

// Utility functions
const generateId = () =>
  "user-" + Date.now() + "-" + Math.random().toString(36).substr(2, 9);

const findUserByEmail = async (email) => {
  return await db.get("SELECT * FROM users WHERE email = ? AND is_active = 1", [
    email,
  ]);
};

const findUserById = async (id) => {
  return await db.get("SELECT * FROM users WHERE id = ? AND is_active = 1", [
    id,
  ]);
};

const createUser = async (userData) => {
  const id = generateId();
  const now = new Date().toISOString();

  await db.run(
    `
    INSERT INTO users (
      id, name, email, password, phone, profile_type, membership_type,
      is_active, profile_completed, profile_completion_percentage,
      verification_status, personal_info, family_info, education_career,
      lifestyle, religious_info, partner_preferences, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `,
    [
      id,
      userData.name,
      userData.email,
      userData.password,
      userData.phone || "",
      userData.profileType || "self",
      userData.membershipType || "free",
      1,
      0,
      20,
      JSON.stringify({
        email: false,
        phone: false,
        photo: false,
        document: false,
      }),
      JSON.stringify({}),
      JSON.stringify({}),
      JSON.stringify({}),
      JSON.stringify({}),
      JSON.stringify({}),
      JSON.stringify({}),
      now,
      now,
    ]
  );

  return await findUserById(id);
};

// Authentication middleware
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers["authorization"];
  const token = authHeader && authHeader.split(" ")[1];

  if (!token) {
    return res.status(401).json({
      success: false,
      message: "Access token required",
    });
  }

  try {
    const decoded = jwt.verify(
      token,
      process.env.JWT_SECRET || "matrimony-secret-key"
    );
    const user = await findUserById(decoded.userId);

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid token",
      });
    }

    // Parse JSON fields
    if (user.personal_info) user.personalInfo = JSON.parse(user.personal_info);
    if (user.family_info) user.familyInfo = JSON.parse(user.family_info);
    if (user.education_career)
      user.educationCareer = JSON.parse(user.education_career);
    if (user.lifestyle) user.lifestyle = JSON.parse(user.lifestyle);
    if (user.religious_info)
      user.religiousInfo = JSON.parse(user.religious_info);
    if (user.partner_preferences)
      user.partnerPreferences = JSON.parse(user.partner_preferences);
    if (user.verification_status)
      user.verificationStatus = JSON.parse(user.verification_status);

    req.user = user;
    next();
  } catch (error) {
    return res.status(403).json({
      success: false,
      message: "Invalid or expired token",
    });
  }
};

// Health check
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    message: "Matrimony API is running",
    timestamp: new Date().toISOString(),
    version: "2.0.0",
  });
});

// Register
app.post("/api/auth/register", rateLimits.auth, async (req, res) => {
  try {
    const { name, email, password, phone, profileType } = req.body;

    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: "Name, email, and password are required",
      });
    }

    // Check if user already exists
    const existingUser = await findUserByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "User with this email already exists",
      });
    }

    const hashedPassword = await bcrypt.hash(password, 12);

    const user = await createUser({
      name,
      email,
      password: hashedPassword,
      phone,
      profileType,
    });

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || "matrimony-secret-key",
      { expiresIn: "7d" }
    );

    // Remove password from response
    const { password: _, ...userResponse } = user;

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user: userResponse,
        token,
        expiresIn: "7d",
      },
    });
  } catch (error) {
    console.error("Registration error:", error);
    res.status(500).json({
      success: false,
      message: "Registration failed",
    });
  }
});

// Login
app.post("/api/auth/login", rateLimits.auth, async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: "Email and password are required",
      });
    }

    const user = await findUserByEmail(email);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);

    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: "Invalid email or password",
      });
    }

    // Update last active
    await db.run("UPDATE users SET last_active = ? WHERE id = ?", [
      new Date().toISOString(),
      user.id,
    ]);

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || "matrimony-secret-key",
      { expiresIn: "7d" }
    );

    const { password: _, ...userResponse } = user;

    res.json({
      success: true,
      message: "Login successful",
      data: {
        user: userResponse,
        token,
        expiresIn: "7d",
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Login failed",
    });
  }
});

// Verify token
app.get("/api/auth/verify", authenticateToken, (req, res) => {
  const { password: _, ...userResponse } = req.user;
  res.json({
    success: true,
    data: {
      user: userResponse,
      valid: true,
    },
  });
});

// Get user profile
app.get("/api/users/profile", authenticateToken, (req, res) => {
  const { password: _, ...userResponse } = req.user;
  res.json({
    success: true,
    data: { user: userResponse },
  });
});

// Update profile
app.put("/api/users/profile", authenticateToken, (req, res) => {
  try {
    const updates = req.body;
    const user = req.user;

    // Update allowed fields
    const allowedFields = [
      "name",
      "phone",
      "personalInfo",
      "familyInfo",
      "educationCareer",
      "lifestyle",
      "religiousInfo",
      "partnerPreferences",
    ];

    allowedFields.forEach((field) => {
      if (updates[field] !== undefined) {
        user[field] = updates[field];
      }
    });

    // Calculate profile completion
    let completionScore = 20; // Base score for registration

    if (user.personalInfo && Object.keys(user.personalInfo).length > 0)
      completionScore += 20;
    if (user.educationCareer && Object.keys(user.educationCareer).length > 0)
      completionScore += 20;
    if (user.familyInfo && Object.keys(user.familyInfo).length > 0)
      completionScore += 15;
    if (user.religiousInfo && Object.keys(user.religiousInfo).length > 0)
      completionScore += 15;
    if (user.lifestyle && Object.keys(user.lifestyle).length > 0)
      completionScore += 10;

    user.profileCompletionPercentage = Math.min(completionScore, 100);
    user.profileCompleted = user.profileCompletionPercentage >= 80;

    const { password: _, ...userResponse } = user;

    res.json({
      success: true,
      message: "Profile updated successfully",
      data: { user: userResponse },
    });
  } catch (error) {
    console.error("Profile update error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update profile",
    });
  }
});

// Search profiles
app.get("/api/search", authenticateToken, (req, res) => {
  try {
    const { page = 1, limit = 20, ageMin, ageMax, city, religion } = req.query;
    const currentUserId = req.user.id;

    let filteredUsers = users.filter(
      (user) =>
        user.id !== currentUserId && user.isActive && user.profileCompleted
    );

    // Apply filters
    if (ageMin || ageMax) {
      filteredUsers = filteredUsers.filter((user) => {
        const age = user.personalInfo?.age;
        if (!age) return false;
        if (ageMin && age < parseInt(ageMin)) return false;
        if (ageMax && age > parseInt(ageMax)) return false;
        return true;
      });
    }

    if (city) {
      filteredUsers = filteredUsers.filter((user) =>
        user.personalInfo?.city?.toLowerCase().includes(city.toLowerCase())
      );
    }

    if (religion) {
      filteredUsers = filteredUsers.filter(
        (user) =>
          user.religiousInfo?.religion?.toLowerCase() === religion.toLowerCase()
      );
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    const profiles = paginatedUsers.map((user) => {
      const { password: _, ...profile } = user;
      return profile;
    });

    res.json({
      success: true,
      data: {
        profiles,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(filteredUsers.length / limit),
          totalResults: filteredUsers.length,
          hasNext: endIndex < filteredUsers.length,
          hasPrev: page > 1,
        },
      },
    });
  } catch (error) {
    console.error("Search error:", error);
    res.status(500).json({
      success: false,
      message: "Search failed",
    });
  }
});

// Error handling
app.use((error, req, res, next) => {
  console.error("Error:", error);
  res.status(500).json({
    success: false,
    message: "Internal server error",
  });
});

// 404 handler
app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Route not found",
  });
});

// Initialize and start server
const startServer = async () => {
  try {
    // Initialize database
    await db.initialize();

    const PORT = process.env.PORT || 5000;
    const server = app.listen(PORT, () => {
      console.log(`🚀 Secure Matrimony server running on port ${PORT}`);
      console.log(`🔒 Security: Enabled (Helmet, CORS, Rate Limiting)`);
      console.log(`🗄️  Database: SQLite`);
      console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
      console.log(`🔗 Frontend: http://localhost:5173`);
    });

    // Graceful shutdown
    process.on("SIGTERM", async () => {
      console.log("🛑 SIGTERM received, shutting down gracefully");

      server.close(async () => {
        console.log("✅ HTTP server closed");
        await db.close();
        process.exit(0);
      });
    });

    process.on("SIGINT", async () => {
      console.log("🛑 SIGINT received, shutting down gracefully");

      server.close(async () => {
        console.log("✅ HTTP server closed");
        await db.close();
        process.exit(0);
      });
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

// Start the server
startServer();

module.exports = app;
