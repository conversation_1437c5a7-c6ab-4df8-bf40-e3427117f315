const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs').promises;
const sharp = require('sharp');
const { authenticateToken } = require('../../middleware/auth');
const { validateInput } = require('../../middleware/validation');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    const uploadDir = path.join(__dirname, '../../../uploads/photos');
    try {
      await fs.mkdir(uploadDir, { recursive: true });
      cb(null, uploadDir);
    } catch (error) {
      cb(error);
    }
  },
  filename: (req, file, cb) => {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, `photo-${uniqueSuffix}${path.extname(file.originalname)}`);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
    files: 5
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.'));
    }
  }
});

// Get current user profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: { exclude: ['password', 'passwordResetToken', 'emailVerificationToken'] }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { user }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
});

// Update personal details
router.put('/profile/personal', authenticateToken, [
  body('firstName').optional().isLength({ min: 2, max: 50 }).trim(),
  body('lastName').optional().isLength({ min: 2, max: 50 }).trim(),
  body('dateOfBirth').optional().isISO8601(),
  body('age').optional().isInt({ min: 18, max: 100 }),
  body('height').optional().isInt({ min: 100, max: 250 }),
  body('weight').optional().isInt({ min: 30, max: 200 }),
  body('maritalStatus').optional().isIn(['never_married', 'divorced', 'widowed', 'separated', 'awaiting_divorce']),
  body('motherTongue').optional().isLength({ min: 2, max: 50 }),
  body('country').optional().isLength({ min: 2, max: 100 }),
  body('state').optional().isLength({ min: 2, max: 100 }),
  body('city').optional().isLength({ min: 2, max: 100 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update personal info
    const personalInfo = { ...user.personalInfo, ...req.body };
    user.personalInfo = personalInfo;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Personal details updated successfully',
      data: { 
        user: {
          id: user.id,
          personalInfo: user.personalInfo,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update personal details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update personal details'
    });
  }
});

// Update family details
router.put('/profile/family', authenticateToken, [
  body('familyType').optional().isIn(['nuclear', 'joint', 'others']),
  body('familyStatus').optional().isIn(['middle_class', 'upper_middle_class', 'rich', 'affluent']),
  body('familyValues').optional().isIn(['orthodox', 'traditional', 'moderate', 'liberal']),
  body('totalBrothers').optional().isInt({ min: 0, max: 20 }),
  body('marriedBrothers').optional().isInt({ min: 0, max: 20 }),
  body('totalSisters').optional().isInt({ min: 0, max: 20 }),
  body('marriedSisters').optional().isInt({ min: 0, max: 20 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update family info
    const familyInfo = { ...user.familyInfo, ...req.body };
    user.familyInfo = familyInfo;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Family details updated successfully',
      data: { 
        user: {
          id: user.id,
          familyInfo: user.familyInfo,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update family details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update family details'
    });
  }
});

// Update education details
router.put('/profile/education', authenticateToken, [
  body('highestEducation').optional().isIn(['high_school', 'diploma', 'bachelors', 'masters', 'doctorate', 'professional']),
  body('educationField').optional().isLength({ min: 2, max: 100 }),
  body('institutionName').optional().isLength({ min: 2, max: 200 }),
  body('graduationYear').optional().isInt({ min: 1950, max: new Date().getFullYear() })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update education info
    const educationCareer = { ...user.educationCareer, ...req.body };
    user.educationCareer = educationCareer;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Education details updated successfully',
      data: { 
        user: {
          id: user.id,
          educationCareer: user.educationCareer,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update education details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update education details'
    });
  }
});

// Update professional details
router.put('/profile/professional', authenticateToken, [
  body('occupation').optional().isLength({ min: 2, max: 100 }),
  body('designation').optional().isLength({ min: 2, max: 100 }),
  body('companyName').optional().isLength({ min: 2, max: 200 }),
  body('companyType').optional().isIn(['government', 'private', 'business', 'self_employed', 'not_working']),
  body('workExperienceYears').optional().isInt({ min: 0, max: 50 }),
  body('annualIncomeRange').optional().isIn(['below_2_lakhs', '2_3_lakhs', '3_5_lakhs', '5_7_lakhs', '7_10_lakhs', '10_15_lakhs', '15_20_lakhs', '20_30_lakhs', '30_50_lakhs', 'above_50_lakhs'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update professional info
    const educationCareer = { ...user.educationCareer, ...req.body };
    user.educationCareer = educationCareer;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Professional details updated successfully',
      data: { 
        user: {
          id: user.id,
          educationCareer: user.educationCareer,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update professional details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update professional details'
    });
  }
});

// Update lifestyle details
router.put('/profile/lifestyle', authenticateToken, [
  body('diet').optional().isIn(['vegetarian', 'non_vegetarian', 'vegan', 'jain_food', 'occasionally_non_veg']),
  body('smoking').optional().isIn(['never', 'occasionally', 'regularly', 'trying_to_quit']),
  body('drinking').optional().isIn(['never', 'occasionally', 'socially', 'regularly']),
  body('fitnessLevel').optional().isIn(['low', 'moderate', 'high', 'fitness_freak'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update lifestyle info
    const lifestyle = { ...user.lifestyle, ...req.body };
    user.lifestyle = lifestyle;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Lifestyle details updated successfully',
      data: { 
        user: {
          id: user.id,
          lifestyle: user.lifestyle,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update lifestyle details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update lifestyle details'
    });
  }
});

// Update religious details
router.put('/profile/religious', authenticateToken, [
  body('religion').optional().isIn(['hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain', 'parsi', 'other']),
  body('caste').optional().isLength({ min: 2, max: 100 }),
  body('subCaste').optional().isLength({ min: 2, max: 100 }),
  body('manglikStatus').optional().isIn(['yes', 'no', 'anshik', 'dont_know'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Update religious info
    const religiousInfo = { ...user.religiousInfo, ...req.body };
    user.religiousInfo = religiousInfo;
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Religious details updated successfully',
      data: { 
        user: {
          id: user.id,
          religiousInfo: user.religiousInfo,
          profileCompletionPercentage: user.profileCompletionPercentage
        }
      }
    });
  } catch (error) {
    console.error('Update religious details error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update religious details'
    });
  }
});

// Upload photos
router.post('/profile/photos', authenticateToken, upload.array('photos', 5), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No files uploaded'
      });
    }

    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const processedPhotos = [];
    const currentPhotos = user.profilePhotos || [];

    for (const file of req.files) {
      try {
        // Process image with Sharp
        const processedFileName = `processed-${file.filename}`;
        const processedPath = path.join(path.dirname(file.path), processedFileName);

        await sharp(file.path)
          .resize(800, 800, { fit: 'inside', withoutEnlargement: true })
          .jpeg({ quality: 85 })
          .toFile(processedPath);

        // Create photo record
        const photo = {
          id: Date.now() + Math.random(),
          fileName: processedFileName,
          filePath: `/uploads/photos/${processedFileName}`,
          fileSize: (await fs.stat(processedPath)).size,
          mimeType: 'image/jpeg',
          isProfilePicture: currentPhotos.length === 0 && processedPhotos.length === 0,
          isVerified: false,
          displayOrder: currentPhotos.length + processedPhotos.length,
          visibility: 'public',
          uploadedAt: new Date().toISOString()
        };

        processedPhotos.push(photo);

        // Remove original file
        await fs.unlink(file.path);
      } catch (processError) {
        console.error('Photo processing error:', processError);
        // Continue with other photos
      }
    }

    // Update user photos
    user.profilePhotos = [...currentPhotos, ...processedPhotos];
    
    // Set profile picture if this is the first photo
    if (!user.profilePicture && processedPhotos.length > 0) {
      user.profilePicture = processedPhotos[0].filePath;
    }
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Photos uploaded successfully',
      data: { 
        photos: processedPhotos,
        profileCompletionPercentage: user.profileCompletionPercentage
      }
    });
  } catch (error) {
    console.error('Photo upload error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload photos'
    });
  }
});

// Get user photos
router.get('/profile/photos', authenticateToken, async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: { photos: user.profilePhotos || [] }
    });
  } catch (error) {
    console.error('Get photos error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get photos'
    });
  }
});

module.exports = router;
