import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Select,
  SelectItem,
  Checkbox,
  RadioGroup,
  Radio,
  Divider,
  Avatar,
  Chip,
  Pagination,
  Tabs,
  Tab,
  Tooltip,
  Spinner,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { ProfileMatch } from "../types/user";
import { useAuth } from "../contexts/auth-context";
import { searchAPI, interestAPI } from "../services/api";

// Mock data for search results
const searchResults: ProfileMatch[] = [
  {
    id: "1",
    userId: "101",
    name: "<PERSON><PERSON><PERSON>",
    age: 28,
    location: "Mumbai, Maharashtra",
    profession: "Software Engineer",
    education: "B.Tech, Computer Science",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=2",
    compatibilityScore: 85,
    isPremium: true,
    isVerified: true,
    lastActive: "2023-06-15T10:30:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: true,
    viewed: true,
  },
  {
    id: "2",
    userId: "102",
    name: "<PERSON><PERSON><PERSON>",
    age: 26,
    location: "Bangalore, Karnataka",
    profession: "Data Scientist",
    education: "M.Sc, Statistics",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=3",
    compatibilityScore: 78,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-14T14:20:00Z",
    shortlisted: true,
    interestSent: false,
    interestReceived: false,
    viewed: true,
  },
  {
    id: "3",
    userId: "103",
    name: "Pooja Verma",
    age: 27,
    location: "Delhi, NCR",
    profession: "Marketing Manager",
    education: "MBA, Marketing",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=4",
    compatibilityScore: 72,
    isPremium: true,
    isVerified: true,
    lastActive: "2023-06-15T09:15:00Z",
    shortlisted: false,
    interestSent: true,
    interestReceived: false,
    viewed: true,
  },
  {
    id: "4",
    userId: "104",
    name: "Kavita Singh",
    age: 29,
    location: "Pune, Maharashtra",
    profession: "HR Manager",
    education: "MBA, Human Resources",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=5",
    compatibilityScore: 68,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-15T11:45:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
  {
    id: "5",
    userId: "105",
    name: "Meera Reddy",
    age: 25,
    location: "Hyderabad, Telangana",
    profession: "Architect",
    education: "B.Arch",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=6",
    compatibilityScore: 75,
    isPremium: true,
    isVerified: true,
    lastActive: "2023-06-14T16:30:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
  {
    id: "6",
    userId: "106",
    name: "Priya Malhotra",
    age: 27,
    location: "Chennai, Tamil Nadu",
    profession: "Doctor",
    education: "MBBS, MD",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=7",
    compatibilityScore: 82,
    isPremium: true,
    isVerified: true,
    lastActive: "2023-06-15T08:20:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
  {
    id: "7",
    userId: "107",
    name: "Divya Gupta",
    age: 26,
    location: "Kolkata, West Bengal",
    profession: "Chartered Accountant",
    education: "B.Com, CA",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=8",
    compatibilityScore: 70,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-14T19:10:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
  {
    id: "8",
    userId: "108",
    name: "Ritu Desai",
    age: 28,
    location: "Ahmedabad, Gujarat",
    profession: "Teacher",
    education: "B.Ed, M.Ed",
    photo: "https://img.heroui.chat/image/avatar?w=200&h=200&u=9",
    compatibilityScore: 65,
    isPremium: false,
    isVerified: true,
    lastActive: "2023-06-15T07:45:00Z",
    shortlisted: false,
    interestSent: false,
    interestReceived: false,
    viewed: false,
  },
];

export const SearchPage: React.FC = () => {
  const { user } = useAuth();
  const [ageRange, setAgeRange] = React.useState<[number, number]>([24, 32]);
  const [heightRange, setHeightRange] = React.useState<[number, number]>([
    150, 180,
  ]);

  // API state
  const [profiles, setProfiles] = React.useState<ProfileMatch[]>([]);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [pagination, setPagination] = React.useState({
    currentPage: 1,
    totalPages: 1,
    totalResults: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [maritalStatus, setMaritalStatus] = React.useState<string[]>([
    "never_married",
  ]);
  const [religion, setReligion] = React.useState<string>("");
  const [caste, setCaste] = React.useState<string>("");
  const [motherTongue, setMotherTongue] = React.useState<string>("");
  const [education, setEducation] = React.useState<string>("");
  const [occupation, setOccupation] = React.useState<string>("");
  const [location, setLocation] = React.useState<string>("");
  const [diet, setDiet] = React.useState<string>("");
  const [manglikStatus, setManglikStatus] = React.useState<string>("");
  const [photoOnly, setPhotoOnly] = React.useState<boolean>(false);
  const [verifiedOnly, setVerifiedOnly] = React.useState<boolean>(false);
  const [onlineOnly, setOnlineOnly] = React.useState<boolean>(false);
  const [premiumOnly, setPremiumOnly] = React.useState<boolean>(false);
  const [activeOnly, setActiveOnly] = React.useState<boolean>(false);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [sortBy, setSortBy] = React.useState<string>("relevance");
  const [isLoading, setIsLoading] = React.useState<boolean>(false);
  const [isAdvancedFilterOpen, setIsAdvancedFilterOpen] =
    React.useState<boolean>(false);
  const [viewMode, setViewMode] = React.useState<"list" | "grid">("list");

  // Search function
  const searchProfiles = React.useCallback(
    async (page = 1) => {
      if (!user) return;

      setLoading(true);
      setError(null);

      try {
        const searchCriteria = {
          page,
          limit: 20,
          ageMin: ageRange[0],
          ageMax: ageRange[1],
          religion: religion || undefined,
          caste: caste || undefined,
          maritalStatus:
            maritalStatus.length > 0 ? maritalStatus[0] : undefined,
          education: education || undefined,
          occupation: occupation || undefined,
          city: location || undefined,
          diet: diet || undefined,
          manglik: manglikStatus || undefined,
          withPhoto: photoOnly,
          verified: verifiedOnly,
          sortBy,
        };

        const response = await searchAPI.searchProfiles(searchCriteria);

        if (response.success) {
          setProfiles(response.data.profiles);
          setPagination(response.data.pagination);
          setCurrentPage(page);
        } else {
          setError(response.message || "Search failed");
        }
      } catch (err: any) {
        console.error("Search error:", err);
        setError(
          err.response?.data?.message || "Search failed. Please try again."
        );
        // Fallback to mock data if API fails
        setProfiles(searchResults);
        setPagination({
          currentPage: 1,
          totalPages: 1,
          totalResults: searchResults.length,
          hasNext: false,
          hasPrev: false,
        });
      } finally {
        setLoading(false);
      }
    },
    [
      user,
      ageRange,
      religion,
      caste,
      maritalStatus,
      education,
      occupation,
      location,
      diet,
      manglikStatus,
      photoOnly,
      verifiedOnly,
      sortBy,
    ]
  );

  // Send interest function
  const sendInterest = async (profileId: string) => {
    try {
      const response = await interestAPI.sendInterest(
        profileId,
        "Hi! I found your profile interesting. Would love to connect."
      );

      if (response.success) {
        // Update the profile to show interest sent
        setProfiles((prev) =>
          prev.map((profile) =>
            profile.id === profileId
              ? { ...profile, interestSent: true }
              : profile
          )
        );
      }
    } catch (error: any) {
      console.error("Send interest error:", error);
      // For demo, still update UI
      setProfiles((prev) =>
        prev.map((profile) =>
          profile.id === profileId
            ? { ...profile, interestSent: true }
            : profile
        )
      );
    }
  };

  // Initial search on component mount
  React.useEffect(() => {
    if (profiles.length === 0) {
      searchProfiles(1);
    }
  }, []);

  const handleSearch = () => {
    searchProfiles(1);
  };

  const handleReset = () => {
    setAgeRange([24, 32]);
    setHeightRange([150, 180]);
    setMaritalStatus(["never_married"]);
    setReligion("");
    setCaste("");
    setMotherTongue("");
    setEducation("");
    setOccupation("");
    setLocation("");
    setDiet("");
    setManglikStatus("");
    setPhotoOnly(false);
    setVerifiedOnly(false);
    setOnlineOnly(false);
    setPremiumOnly(false);
    setActiveOnly(false);
    setSortBy("relevance");
  };

  const handleSendInterest = (profileId: string) => {
    sendInterest(profileId);
  };

  const handleShortlist = (profileId: string) => {
    console.log(`Shortlisting profile ${profileId}`);
    // Implement shortlisting logic
  };

  const religions = [
    { key: "hindu", label: "Hindu" },
    { key: "muslim", label: "Muslim" },
    { key: "christian", label: "Christian" },
    { key: "sikh", label: "Sikh" },
    { key: "buddhist", label: "Buddhist" },
    { key: "jain", label: "Jain" },
    { key: "parsi", label: "Parsi" },
    { key: "jewish", label: "Jewish" },
    { key: "other", label: "Other" },
  ];

  const languages = [
    { key: "hindi", label: "Hindi" },
    { key: "tamil", label: "Tamil" },
    { key: "telugu", label: "Telugu" },
    { key: "kannada", label: "Kannada" },
    { key: "malayalam", label: "Malayalam" },
    { key: "marathi", label: "Marathi" },
    { key: "bengali", label: "Bengali" },
    { key: "gujarati", label: "Gujarati" },
    { key: "punjabi", label: "Punjabi" },
    { key: "urdu", label: "Urdu" },
    { key: "english", label: "English" },
    { key: "other", label: "Other" },
  ];

  const educationOptions = [
    { key: "high_school", label: "High School" },
    { key: "bachelor", label: "Bachelor's Degree" },
    { key: "master", label: "Master's Degree" },
    { key: "doctorate", label: "Doctorate" },
    { key: "diploma", label: "Diploma" },
    { key: "other", label: "Other" },
  ];

  const occupationOptions = [
    { key: "software", label: "Software Professional" },
    { key: "doctor", label: "Doctor" },
    { key: "engineer", label: "Engineer" },
    { key: "teacher", label: "Teacher" },
    { key: "business", label: "Business Owner" },
    { key: "government", label: "Government Employee" },
    { key: "other", label: "Other" },
  ];

  const locationOptions = [
    { key: "mumbai", label: "Mumbai" },
    { key: "delhi", label: "Delhi" },
    { key: "bangalore", label: "Bangalore" },
    { key: "hyderabad", label: "Hyderabad" },
    { key: "chennai", label: "Chennai" },
    { key: "kolkata", label: "Kolkata" },
    { key: "pune", label: "Pune" },
    { key: "ahmedabad", label: "Ahmedabad" },
    { key: "jaipur", label: "Jaipur" },
    { key: "lucknow", label: "Lucknow" },
    { key: "other", label: "Other" },
  ];

  const dietOptions = [
    { key: "vegetarian", label: "Vegetarian" },
    { key: "non_vegetarian", label: "Non-Vegetarian" },
    { key: "eggetarian", label: "Eggetarian" },
    { key: "vegan", label: "Vegan" },
    { key: "jain", label: "Jain" },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Search Profiles</h1>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Left Column - Search Filters */}
          <div className="lg:col-span-1">
            <Card className="sticky top-20">
              <CardHeader className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">Search Filters</h2>
                <Button
                  variant="light"
                  color="primary"
                  size="sm"
                  onPress={handleReset}
                >
                  Reset
                </Button>
              </CardHeader>
              <CardBody className="space-y-6">
                <div>
                  <p className="text-sm font-medium mb-2">Age Range</p>
                  <div className="flex gap-2 items-center">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={ageRange[0].toString()}
                      onChange={(e) =>
                        setAgeRange([parseInt(e.target.value), ageRange[1]])
                      }
                      className="w-24"
                      min={18}
                      max={70}
                    />
                    <span>to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={ageRange[1].toString()}
                      onChange={(e) =>
                        setAgeRange([ageRange[0], parseInt(e.target.value)])
                      }
                      className="w-24"
                      min={18}
                      max={70}
                    />
                    <span>yrs</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Height Range</p>
                  <div className="flex gap-2 items-center">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={heightRange[0].toString()}
                      onChange={(e) =>
                        setHeightRange([
                          parseInt(e.target.value),
                          heightRange[1],
                        ])
                      }
                      className="w-24"
                      min={140}
                      max={200}
                    />
                    <span>to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={heightRange[1].toString()}
                      onChange={(e) =>
                        setHeightRange([
                          heightRange[0],
                          parseInt(e.target.value),
                        ])
                      }
                      className="w-24"
                      min={140}
                      max={200}
                    />
                    <span>cm</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm font-medium mb-2">Marital Status</p>
                  <div className="flex flex-wrap gap-2">
                    <Checkbox
                      isSelected={maritalStatus.includes("never_married")}
                      onValueChange={(isSelected) => {
                        if (isSelected) {
                          setMaritalStatus([...maritalStatus, "never_married"]);
                        } else {
                          setMaritalStatus(
                            maritalStatus.filter((s) => s !== "never_married")
                          );
                        }
                      }}
                      size="sm"
                    >
                      Never Married
                    </Checkbox>
                    <Checkbox
                      isSelected={maritalStatus.includes("divorced")}
                      onValueChange={(isSelected) => {
                        if (isSelected) {
                          setMaritalStatus([...maritalStatus, "divorced"]);
                        } else {
                          setMaritalStatus(
                            maritalStatus.filter((s) => s !== "divorced")
                          );
                        }
                      }}
                      size="sm"
                    >
                      Divorced
                    </Checkbox>
                    <Checkbox
                      isSelected={maritalStatus.includes("widowed")}
                      onValueChange={(isSelected) => {
                        if (isSelected) {
                          setMaritalStatus([...maritalStatus, "widowed"]);
                        } else {
                          setMaritalStatus(
                            maritalStatus.filter((s) => s !== "widowed")
                          );
                        }
                      }}
                      size="sm"
                    >
                      Widowed
                    </Checkbox>
                  </div>
                </div>

                <Select
                  label="Religion"
                  placeholder="Select religion"
                  selectedKeys={religion ? [religion] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setReligion(selected);
                  }}
                >
                  {religions.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>

                <Select
                  label="Mother Tongue"
                  placeholder="Select language"
                  selectedKeys={motherTongue ? [motherTongue] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setMotherTongue(selected);
                  }}
                >
                  {languages.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>

                <Select
                  label="Location"
                  placeholder="Select location"
                  selectedKeys={location ? [location] : []}
                  onSelectionChange={(keys) => {
                    const selected = Array.from(keys)[0] as string;
                    setLocation(selected);
                  }}
                >
                  {locationOptions.map((item) => (
                    <SelectItem key={item.key} value={item.key}>
                      {item.label}
                    </SelectItem>
                  ))}
                </Select>

                <Button
                  variant="light"
                  color="primary"
                  className="w-full"
                  endContent={
                    isAdvancedFilterOpen ? (
                      <Icon icon="lucide:chevron-up" />
                    ) : (
                      <Icon icon="lucide:chevron-down" />
                    )
                  }
                  onPress={() => setIsAdvancedFilterOpen(!isAdvancedFilterOpen)}
                >
                  {isAdvancedFilterOpen
                    ? "Hide Advanced Filters"
                    : "Show Advanced Filters"}
                </Button>

                {isAdvancedFilterOpen && (
                  <div className="space-y-4">
                    <Divider />

                    <Select
                      label="Education"
                      placeholder="Select education"
                      selectedKeys={education ? [education] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setEducation(selected);
                      }}
                    >
                      {educationOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>

                    <Select
                      label="Occupation"
                      placeholder="Select occupation"
                      selectedKeys={occupation ? [occupation] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setOccupation(selected);
                      }}
                    >
                      {occupationOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>

                    <Select
                      label="Diet"
                      placeholder="Select diet preference"
                      selectedKeys={diet ? [diet] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setDiet(selected);
                      }}
                    >
                      {dietOptions.map((item) => (
                        <SelectItem key={item.key} value={item.key}>
                          {item.label}
                        </SelectItem>
                      ))}
                    </Select>

                    <div>
                      <p className="text-sm font-medium mb-2">Manglik Status</p>
                      <RadioGroup
                        orientation="horizontal"
                        value={manglikStatus}
                        onValueChange={setManglikStatus}
                      >
                        <Radio value="yes">Yes</Radio>
                        <Radio value="no">No</Radio>
                        <Radio value="doesnt_matter">Doesn't Matter</Radio>
                      </RadioGroup>
                    </div>

                    <Divider />

                    <div className="space-y-2">
                      <Checkbox
                        isSelected={photoOnly}
                        onValueChange={setPhotoOnly}
                        size="sm"
                      >
                        Profiles with Photo only
                      </Checkbox>

                      <Checkbox
                        isSelected={verifiedOnly}
                        onValueChange={setVerifiedOnly}
                        size="sm"
                      >
                        Verified Profiles only
                      </Checkbox>

                      <Checkbox
                        isSelected={onlineOnly}
                        onValueChange={setOnlineOnly}
                        size="sm"
                      >
                        Online now
                      </Checkbox>

                      <Checkbox
                        isSelected={premiumOnly}
                        onValueChange={setPremiumOnly}
                        size="sm"
                      >
                        Premium Members only
                      </Checkbox>

                      <Checkbox
                        isSelected={activeOnly}
                        onValueChange={setActiveOnly}
                        size="sm"
                      >
                        Active in last 7 days
                      </Checkbox>
                    </div>
                  </div>
                )}

                <Button
                  color="primary"
                  className="w-full"
                  onPress={handleSearch}
                  isLoading={isLoading}
                >
                  Search
                </Button>
              </CardBody>
            </Card>
          </div>

          {/* Right Column - Search Results */}
          <div className="lg:col-span-3">
            <Card className="mb-6">
              <CardBody>
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                  <div className="flex items-center gap-2">
                    <p className="text-default-500 text-sm">
                      <span className="font-semibold text-default-700">
                        {searchResults.length}
                      </span>{" "}
                      profiles found
                    </p>

                    <Select
                      label="Sort By"
                      placeholder="Sort by"
                      selectedKeys={sortBy ? [sortBy] : []}
                      onSelectionChange={(keys) => {
                        const selected = Array.from(keys)[0] as string;
                        setSortBy(selected);
                      }}
                      className="w-40"
                      size="sm"
                    >
                      <SelectItem key="relevance" value="relevance">
                        Relevance
                      </SelectItem>
                      <SelectItem key="newest" value="newest">
                        Newest First
                      </SelectItem>
                      <SelectItem key="oldest" value="oldest">
                        Oldest First
                      </SelectItem>
                      <SelectItem key="ageAsc" value="ageAsc">
                        Age: Low to High
                      </SelectItem>
                      <SelectItem key="ageDesc" value="ageDesc">
                        Age: High to Low
                      </SelectItem>
                    </Select>
                  </div>

                  <div className="flex gap-2 items-center">
                    {/* Add view mode toggle */}
                    <div className="border border-default-200 rounded-md flex mr-2">
                      <Tooltip content="List View">
                        <Button
                          size="sm"
                          variant={viewMode === "list" ? "solid" : "light"}
                          color={viewMode === "list" ? "primary" : "default"}
                          onPress={() => setViewMode("list")}
                          isIconOnly
                        >
                          <Icon icon="lucide:list" />
                        </Button>
                      </Tooltip>
                      <Tooltip content="Grid View">
                        <Button
                          size="sm"
                          variant={viewMode === "grid" ? "solid" : "light"}
                          color={viewMode === "grid" ? "primary" : "default"}
                          onPress={() => setViewMode("grid")}
                          isIconOnly
                        >
                          <Icon icon="lucide:grid" />
                        </Button>
                      </Tooltip>
                    </div>

                    <Button
                      variant="flat"
                      color="default"
                      size="sm"
                      startContent={<Icon icon="lucide:save" />}
                    >
                      Save Search
                    </Button>

                    <Button
                      variant="flat"
                      color="primary"
                      size="sm"
                      startContent={<Icon icon="lucide:filter" />}
                    >
                      Applied Filters
                    </Button>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Loading state */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <Spinner size="lg" color="primary" />
                <span className="ml-3 text-lg">Searching profiles...</span>
              </div>
            )}

            {/* Error state */}
            {error && !loading && (
              <div className="text-center py-12">
                <div className="text-red-500 mb-4">
                  <Icon icon="lucide:alert-circle" size={48} />
                </div>
                <h3 className="text-lg font-semibold mb-2">Search Failed</h3>
                <p className="text-default-500 mb-4">{error}</p>
                <Button color="primary" onPress={() => searchProfiles(1)}>
                  Try Again
                </Button>
              </div>
            )}

            {/* Results */}
            {!loading && !error && profiles.length === 0 && (
              <div className="text-center py-12">
                <div className="text-default-400 mb-4">
                  <Icon icon="lucide:search-x" size={48} />
                </div>
                <h3 className="text-lg font-semibold mb-2">
                  No Profiles Found
                </h3>
                <p className="text-default-500 mb-4">
                  Try adjusting your search criteria to find more matches.
                </p>
                <Button
                  color="primary"
                  variant="flat"
                  onPress={() => {
                    // Reset filters
                    setReligion("");
                    setCaste("");
                    setEducation("");
                    setOccupation("");
                    setLocation("");
                    searchProfiles(1);
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            )}

            {/* Grid view */}
            {!loading &&
              !error &&
              profiles.length > 0 &&
              viewMode === "grid" && (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {profiles.map((profile) => (
                    <Card key={profile.id} className="overflow-hidden">
                      <CardBody className="p-0">
                        <div className="relative">
                          <img
                            src={profile.photo}
                            alt={profile.name}
                            className="w-full h-48 object-cover"
                          />
                          {profile.isPremium && (
                            <div className="absolute top-2 left-2">
                              <Chip
                                color="warning"
                                variant="flat"
                                size="sm"
                                startContent={
                                  <Icon
                                    icon="lucide:crown"
                                    className="text-warning"
                                  />
                                }
                              >
                                Premium
                              </Chip>
                            </div>
                          )}
                          {profile.isVerified && (
                            <div className="absolute top-2 right-2">
                              <Chip
                                color="success"
                                variant="flat"
                                size="sm"
                                startContent={
                                  <Icon
                                    icon="lucide:check-circle"
                                    className="text-success"
                                  />
                                }
                              >
                                Verified
                              </Chip>
                            </div>
                          )}
                          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-3">
                            <Chip
                              color="primary"
                              size="sm"
                              variant="flat"
                              className="mb-1"
                            >
                              {profile.compatibilityScore}% Match
                            </Chip>
                            <h3 className="text-white font-semibold">
                              {profile.name}
                            </h3>
                            <p className="text-white/80 text-xs">
                              {profile.age} yrs, {profile.location}
                            </p>
                          </div>
                        </div>

                        <div className="p-3">
                          <div className="flex flex-col gap-1 mb-3">
                            <div className="flex items-center gap-2">
                              <Icon
                                icon="lucide:briefcase"
                                className="text-default-500 text-sm"
                              />
                              <span className="text-xs">
                                {profile.profession}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Icon
                                icon="lucide:graduation-cap"
                                className="text-default-500 text-sm"
                              />
                              <span className="text-xs">
                                {profile.education}
                              </span>
                            </div>
                          </div>

                          <div className="flex flex-wrap gap-2">
                            <Link
                              to={`/profile/${profile.userId}`}
                              className="flex-1"
                            >
                              <Button
                                variant="flat"
                                color="default"
                                size="sm"
                                className="w-full"
                              >
                                View Profile
                              </Button>
                            </Link>

                            {profile.interestReceived ? (
                              <Button
                                color="success"
                                variant="flat"
                                size="sm"
                                isIconOnly
                              >
                                <Icon icon="lucide:check" />
                              </Button>
                            ) : profile.interestSent ? (
                              <Button
                                color="primary"
                                variant="flat"
                                size="sm"
                                isDisabled
                                isIconOnly
                              >
                                <Icon icon="lucide:heart" />
                              </Button>
                            ) : (
                              <Button
                                color="primary"
                                size="sm"
                                isIconOnly
                                onPress={() => handleSendInterest(profile.id)}
                              >
                                <Icon icon="lucide:heart" />
                              </Button>
                            )}

                            <Button
                              variant="light"
                              color={
                                profile.shortlisted ? "warning" : "default"
                              }
                              size="sm"
                              isIconOnly
                              onPress={() => handleShortlist(profile.id)}
                            >
                              <Icon
                                icon={
                                  profile.shortlisted
                                    ? "lucide:star"
                                    : "lucide:star"
                                }
                              />
                            </Button>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              )}

            {/* List view */}
            {!loading &&
              !error &&
              profiles.length > 0 &&
              viewMode === "list" && (
                <div className="space-y-6">
                  {profiles.map((profile) => (
                    <Card key={profile.id} className="overflow-hidden">
                      <CardBody className="p-0">
                        <div className="flex flex-col md:flex-row">
                          <div className="md:w-1/3 relative">
                            <img
                              src={profile.photo}
                              alt={profile.name}
                              className="w-full h-full object-cover aspect-[3/4] md:aspect-auto"
                            />
                            {profile.isPremium && (
                              <div className="absolute top-2 left-2">
                                <Chip
                                  color="warning"
                                  variant="flat"
                                  size="sm"
                                  startContent={
                                    <Icon
                                      icon="lucide:crown"
                                      className="text-warning"
                                    />
                                  }
                                >
                                  Premium
                                </Chip>
                              </div>
                            )}
                            {profile.isVerified && (
                              <div className="absolute top-2 right-2">
                                <Chip
                                  color="success"
                                  variant="flat"
                                  size="sm"
                                  startContent={
                                    <Icon
                                      icon="lucide:check-circle"
                                      className="text-success"
                                    />
                                  }
                                >
                                  Verified
                                </Chip>
                              </div>
                            )}

                            {/* Add activity status indicator */}
                            <div className="absolute bottom-2 left-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                              <span
                                className={`inline-block w-2 h-2 rounded-full ${
                                  new Date(profile.lastActive) >
                                  new Date(Date.now() - 24 * 60 * 60 * 1000)
                                    ? "bg-success"
                                    : "bg-default-300"
                                }`}
                              ></span>
                              {new Date(profile.lastActive) >
                              new Date(Date.now() - 24 * 60 * 60 * 1000)
                                ? "Active Today"
                                : `Active ${new Date(
                                    profile.lastActive
                                  ).toLocaleDateString()}`}
                            </div>
                          </div>

                          <div className="md:w-2/3 p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <h3 className="text-lg font-semibold flex items-center gap-1">
                                  {profile.name}
                                  {profile.isPremium && (
                                    <Icon
                                      icon="lucide:crown"
                                      className="text-warning text-sm"
                                    />
                                  )}
                                </h3>
                                <p className="text-default-500 text-sm">
                                  {profile.age} yrs, {profile.location}
                                </p>
                              </div>
                              <Chip color="primary" size="sm" variant="flat">
                                {profile.compatibilityScore}% Match
                              </Chip>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-4">
                              <div className="flex items-center gap-2">
                                <Icon
                                  icon="lucide:briefcase"
                                  className="text-default-500"
                                />
                                <span className="text-sm">
                                  {profile.profession}
                                </span>
                              </div>

                              <div className="flex items-center gap-2">
                                <Icon
                                  icon="lucide:graduation-cap"
                                  className="text-default-500"
                                />
                                <span className="text-sm">
                                  {profile.education}
                                </span>
                              </div>

                              <div className="flex items-center gap-2">
                                <Icon
                                  icon="lucide:clock"
                                  className="text-default-500"
                                />
                                <span className="text-sm">
                                  Last active:{" "}
                                  {new Date(
                                    profile.lastActive
                                  ).toLocaleDateString()}
                                </span>
                              </div>

                              <div className="flex items-center gap-2">
                                <Icon
                                  icon="lucide:eye"
                                  className="text-default-500"
                                />
                                <span className="text-sm">
                                  {profile.viewed
                                    ? "Profile viewed"
                                    : "Not viewed yet"}
                                </span>
                              </div>
                            </div>

                            {/* Add quick info tabs */}
                            <div className="mt-4">
                              <Tabs
                                aria-label="Profile quick info"
                                size="sm"
                                variant="underlined"
                                color="primary"
                              >
                                <Tab
                                  key="basic"
                                  title={
                                    <div className="flex items-center gap-1">
                                      <Icon icon="lucide:info" size={14} />
                                      <span>Basic</span>
                                    </div>
                                  }
                                >
                                  <div className="py-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Height
                                      </span>
                                      <span>5'6" (168 cm)</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Religion
                                      </span>
                                      <span>Hindu</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Mother Tongue
                                      </span>
                                      <span>Hindi</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Marital Status
                                      </span>
                                      <span>Never Married</span>
                                    </div>
                                  </div>
                                </Tab>
                                <Tab
                                  key="lifestyle"
                                  title={
                                    <div className="flex items-center gap-1">
                                      <Icon icon="lucide:heart" size={14} />
                                      <span>Lifestyle</span>
                                    </div>
                                  }
                                >
                                  <div className="py-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Diet
                                      </span>
                                      <span>Vegetarian</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Smoking
                                      </span>
                                      <span>No</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Drinking
                                      </span>
                                      <span>No</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Hobbies
                                      </span>
                                      <span>Reading, Music</span>
                                    </div>
                                  </div>
                                </Tab>
                                <Tab
                                  key="family"
                                  title={
                                    <div className="flex items-center gap-1">
                                      <Icon icon="lucide:users" size={14} />
                                      <span>Family</span>
                                    </div>
                                  }
                                >
                                  <div className="py-2 grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Family Type
                                      </span>
                                      <span>Nuclear</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Family Status
                                      </span>
                                      <span>Middle Class</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Family Values
                                      </span>
                                      <span>Moderate</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span className="text-default-500">
                                        Location
                                      </span>
                                      <span>{profile.location}</span>
                                    </div>
                                  </div>
                                </Tab>
                              </Tabs>
                            </div>

                            <Divider className="my-4" />

                            <div className="flex flex-wrap gap-3">
                              <Link to={`/profile/${profile.userId}`}>
                                <Button
                                  variant="flat"
                                  color="default"
                                  startContent={<Icon icon="lucide:user" />}
                                >
                                  View Profile
                                </Button>
                              </Link>

                              {profile.interestReceived ? (
                                <Button
                                  color="success"
                                  variant="flat"
                                  startContent={<Icon icon="lucide:check" />}
                                >
                                  Accept Interest
                                </Button>
                              ) : profile.interestSent ? (
                                <Button
                                  color="primary"
                                  variant="flat"
                                  isDisabled
                                  startContent={<Icon icon="lucide:heart" />}
                                >
                                  Interest Sent
                                </Button>
                              ) : (
                                <Button
                                  color="primary"
                                  startContent={<Icon icon="lucide:heart" />}
                                  onPress={() => handleSendInterest(profile.id)}
                                >
                                  Send Interest
                                </Button>
                              )}

                              <Button
                                variant="light"
                                color={
                                  profile.shortlisted ? "warning" : "default"
                                }
                                startContent={
                                  <Icon
                                    icon={
                                      profile.shortlisted
                                        ? "lucide:star"
                                        : "lucide:star"
                                    }
                                  />
                                }
                                onPress={() => handleShortlist(profile.id)}
                              >
                                {profile.shortlisted
                                  ? "Shortlisted"
                                  : "Shortlist"}
                              </Button>

                              {/* Add chat button for premium users */}
                              {profile.isPremium && (
                                <Tooltip
                                  content={
                                    user?.membershipType === "free"
                                      ? "Upgrade to chat"
                                      : "Start conversation"
                                  }
                                >
                                  <Button
                                    variant="light"
                                    color="primary"
                                    isIconOnly
                                    isDisabled={user?.membershipType === "free"}
                                  >
                                    <Icon icon="lucide:message-circle" />
                                  </Button>
                                </Tooltip>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardBody>
                    </Card>
                  ))}
                </div>
              )}

            {/* Pagination */}
            {!loading &&
              !error &&
              profiles.length > 0 &&
              pagination.totalPages > 1 && (
                <div className="flex justify-center mt-8">
                  <Pagination
                    total={pagination.totalPages}
                    initialPage={1}
                    page={pagination.currentPage}
                    onChange={(page) => {
                      searchProfiles(page);
                      window.scrollTo({ top: 0, behavior: "smooth" });
                    }}
                    showControls
                    showShadow
                    color="primary"
                  />
                </div>
              )}

            {/* Results summary */}
            {!loading && !error && profiles.length > 0 && (
              <div className="text-center mt-6 text-sm text-default-500">
                Showing {(pagination.currentPage - 1) * 20 + 1} to{" "}
                {Math.min(pagination.currentPage * 20, pagination.totalResults)}{" "}
                of {pagination.totalResults} profiles
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
