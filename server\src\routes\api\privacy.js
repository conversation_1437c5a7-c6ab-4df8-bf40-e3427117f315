const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');

// In-memory storage for privacy settings and blocked users
let privacySettings = new Map();
let blockedUsers = new Map();
let reportedUsers = new Map();

// Get privacy settings
router.get('/settings', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const defaultSettings = {
      profileVisibility: 'public', // public, members_only, premium_only
      photoVisibility: 'members_only', // public, members_only, premium_only, private
      contactInfoVisibility: 'premium_only', // members_only, premium_only, private
      lastSeenVisibility: 'members_only', // public, members_only, private
      showOnlineStatus: true,
      allowMessages: 'all', // all, premium_only, mutual_interests_only
      allowInterests: 'all', // all, premium_only, verified_only
      allowPhotoRequests: true,
      allowContactRequests: 'premium_only', // all, premium_only, mutual_interests_only
      showInSearch: true,
      showProfileViews: true,
      emailNotifications: true,
      smsNotifications: false,
      pushNotifications: true
    };

    const userSettings = privacySettings.get(userId) || defaultSettings;

    res.json({
      success: true,
      data: { settings: userSettings }
    });

  } catch (error) {
    console.error('Get privacy settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get privacy settings'
    });
  }
});

// Update privacy settings
router.put('/settings', authenticateToken, [
  body('profileVisibility').optional().isIn(['public', 'members_only', 'premium_only']),
  body('photoVisibility').optional().isIn(['public', 'members_only', 'premium_only', 'private']),
  body('contactInfoVisibility').optional().isIn(['members_only', 'premium_only', 'private']),
  body('lastSeenVisibility').optional().isIn(['public', 'members_only', 'private']),
  body('showOnlineStatus').optional().isBoolean(),
  body('allowMessages').optional().isIn(['all', 'premium_only', 'mutual_interests_only']),
  body('allowInterests').optional().isIn(['all', 'premium_only', 'verified_only']),
  body('allowPhotoRequests').optional().isBoolean(),
  body('allowContactRequests').optional().isIn(['all', 'premium_only', 'mutual_interests_only']),
  body('showInSearch').optional().isBoolean(),
  body('showProfileViews').optional().isBoolean(),
  body('emailNotifications').optional().isBoolean(),
  body('smsNotifications').optional().isBoolean(),
  body('pushNotifications').optional().isBoolean()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const currentSettings = privacySettings.get(userId) || {};
    const updatedSettings = { ...currentSettings, ...req.body };

    privacySettings.set(userId, updatedSettings);

    res.json({
      success: true,
      message: 'Privacy settings updated successfully',
      data: { settings: updatedSettings }
    });

  } catch (error) {
    console.error('Update privacy settings error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update privacy settings'
    });
  }
});

// Block user
router.post('/block', authenticateToken, [
  body('userId').notEmpty().withMessage('User ID is required'),
  body('reason').optional().isLength({ max: 500 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const currentUserId = req.user.id;
    const { userId, reason } = req.body;

    if (currentUserId === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot block yourself'
      });
    }

    // Check if user exists
    const userToBlock = await User.findByPk(userId);
    if (!userToBlock) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get current blocked users list
    const currentBlocked = blockedUsers.get(currentUserId) || [];
    
    // Check if already blocked
    if (currentBlocked.some(blocked => blocked.userId === userId)) {
      return res.status(400).json({
        success: false,
        message: 'User is already blocked'
      });
    }

    // Add to blocked list
    currentBlocked.push({
      userId,
      reason: reason || 'No reason provided',
      blockedAt: new Date().toISOString(),
      userName: userToBlock.name
    });

    blockedUsers.set(currentUserId, currentBlocked);

    res.json({
      success: true,
      message: 'User blocked successfully',
      data: { blockedUserId: userId }
    });

  } catch (error) {
    console.error('Block user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to block user'
    });
  }
});

// Unblock user
router.post('/unblock', authenticateToken, [
  body('userId').notEmpty().withMessage('User ID is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const currentUserId = req.user.id;
    const { userId } = req.body;

    // Get current blocked users list
    const currentBlocked = blockedUsers.get(currentUserId) || [];
    
    // Check if user is blocked
    const blockedIndex = currentBlocked.findIndex(blocked => blocked.userId === userId);
    if (blockedIndex === -1) {
      return res.status(400).json({
        success: false,
        message: 'User is not blocked'
      });
    }

    // Remove from blocked list
    currentBlocked.splice(blockedIndex, 1);
    blockedUsers.set(currentUserId, currentBlocked);

    res.json({
      success: true,
      message: 'User unblocked successfully',
      data: { unblockedUserId: userId }
    });

  } catch (error) {
    console.error('Unblock user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unblock user'
    });
  }
});

// Get blocked users
router.get('/blocked', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const blocked = blockedUsers.get(userId) || [];

    res.json({
      success: true,
      data: { blockedUsers: blocked }
    });

  } catch (error) {
    console.error('Get blocked users error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get blocked users'
    });
  }
});

// Report user
router.post('/report', authenticateToken, [
  body('userId').notEmpty().withMessage('User ID is required'),
  body('reason').notEmpty().withMessage('Reason is required'),
  body('category').isIn(['fake_profile', 'inappropriate_behavior', 'spam', 'harassment', 'other']),
  body('description').optional().isLength({ max: 1000 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const reporterId = req.user.id;
    const { userId, reason, category, description } = req.body;

    if (reporterId === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot report yourself'
      });
    }

    // Check if user exists
    const userToReport = await User.findByPk(userId);
    if (!userToReport) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get current reports
    const currentReports = reportedUsers.get(userId) || [];
    
    // Check if already reported by this user
    if (currentReports.some(report => report.reporterId === reporterId)) {
      return res.status(400).json({
        success: false,
        message: 'You have already reported this user'
      });
    }

    // Add report
    const report = {
      reporterId,
      reason,
      category,
      description: description || '',
      reportedAt: new Date().toISOString(),
      status: 'pending' // pending, reviewed, resolved
    };

    currentReports.push(report);
    reportedUsers.set(userId, currentReports);

    res.json({
      success: true,
      message: 'User reported successfully. We will review this report within 24 hours.',
      data: { reportId: `report_${Date.now()}` }
    });

  } catch (error) {
    console.error('Report user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to report user'
    });
  }
});

// Get account deletion options
router.get('/delete-account/options', authenticateToken, async (req, res) => {
  try {
    const options = {
      reasons: [
        'Found my life partner',
        'Not satisfied with matches',
        'Privacy concerns',
        'Too many unwanted contacts',
        'Technical issues',
        'Cost concerns',
        'Other'
      ],
      consequences: [
        'Your profile will be permanently deleted',
        'All your photos will be removed',
        'Your conversations will be deleted',
        'Your interests and matches will be lost',
        'You will lose access to premium features',
        'This action cannot be undone'
      ],
      alternatives: [
        {
          title: 'Hide Profile',
          description: 'Make your profile invisible without deleting it',
          action: 'hide_profile'
        },
        {
          title: 'Pause Account',
          description: 'Temporarily deactivate your account',
          action: 'pause_account'
        },
        {
          title: 'Change Privacy Settings',
          description: 'Limit who can contact you',
          action: 'privacy_settings'
        }
      ]
    };

    res.json({
      success: true,
      data: options
    });

  } catch (error) {
    console.error('Get delete account options error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get delete account options'
    });
  }
});

// Request account deletion
router.post('/delete-account/request', authenticateToken, [
  body('reason').notEmpty().withMessage('Reason is required'),
  body('feedback').optional().isLength({ max: 1000 }),
  body('confirmPassword').notEmpty().withMessage('Password confirmation is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { reason, feedback, confirmPassword } = req.body;

    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // In production, verify password
    // const isPasswordValid = await bcrypt.compare(confirmPassword, user.password);
    // if (!isPasswordValid) {
    //   return res.status(400).json({
    //     success: false,
    //     message: 'Invalid password'
    //   });
    // }

    // Mark account for deletion (in production, implement proper deletion process)
    user.deletionRequested = true;
    user.deletionRequestedAt = new Date();
    user.deletionReason = reason;
    user.deletionFeedback = feedback || '';
    await user.save();

    res.json({
      success: true,
      message: 'Account deletion request submitted. Your account will be deleted within 7 days. You can cancel this request by logging in.',
      data: { 
        deletionRequestedAt: user.deletionRequestedAt,
        cancellationDeadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      }
    });

  } catch (error) {
    console.error('Request account deletion error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to request account deletion'
    });
  }
});

module.exports = router;
