const express = require('express');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { authenticateToken } = require('../middleware/auth');
const { sendEmail } = require('../services/emailService');
const { sendSMS } = require('../services/smsService');

const router = express.Router();

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Register
router.post('/register', [
  body('name').trim().isLength({ min: 2 }).withMessage('Name must be at least 2 characters'),
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
  body('phone').isMobilePhone('en-IN').withMessage('Please provide a valid Indian phone number'),
  body('profileType').isIn(['self', 'son', 'daughter', 'brother', 'sister', 'relative', 'friend']).withMessage('Invalid profile type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { name, email, password, phone, profileType } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { phone }]
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: existingUser.email === email ? 'Email already registered' : 'Phone number already registered'
      });
    }

    // Create user
    const user = new User({
      name,
      email,
      password,
      phone,
      profileType
    });

    // Generate email verification token
    user.emailVerificationToken = crypto.randomBytes(32).toString('hex');

    await user.save();

    // Send verification email
    try {
      await sendEmail({
        to: email,
        subject: 'Verify Your Email - Matrimony Platform',
        template: 'emailVerification',
        data: {
          name,
          verificationLink: `${process.env.CLIENT_URL}/verify-email?token=${user.emailVerificationToken}`
        }
      });
    } catch (emailError) {
      console.error('Email sending failed:', emailError);
    }

    // Generate token
    const token = generateToken(user._id);

    res.status(201).json({
      success: true,
      message: 'Registration successful. Please verify your email.',
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          profileType: user.profileType,
          membershipType: user.membershipType,
          verificationStatus: user.verificationStatus,
          profileCompleted: user.profileCompleted,
          profileCompletionPercentage: user.profileCompletionPercentage
        },
        token
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Registration failed'
    });
  }
});

// Login
router.post('/login', [
  body('email').isEmail().normalizeEmail().withMessage('Please provide a valid email'),
  body('password').notEmpty().withMessage('Password is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Find user and include password for comparison
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    if (user.isBlocked) {
      return res.status(403).json({
        success: false,
        message: 'Your account has been blocked. Please contact support.'
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Update last active
    user.updateLastActive();

    // Generate token
    const token = generateToken(user._id);

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          profileType: user.profileType,
          membershipType: user.membershipType,
          profilePicture: user.profilePicture,
          verificationStatus: user.verificationStatus,
          profileCompleted: user.profileCompleted,
          profileCompletionPercentage: user.profileCompletionPercentage,
          lastActive: user.lastActive
        },
        token
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Login failed'
    });
  }
});

// Send phone verification OTP
router.post('/send-phone-otp', authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    if (user.verificationStatus.phone) {
      return res.status(400).json({
        success: false,
        message: 'Phone number already verified'
      });
    }

    // Generate OTP
    const otp = generateOTP();
    user.phoneVerificationOTP = otp;
    user.phoneVerificationExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await user.save();

    // Send SMS
    try {
      await sendSMS({
        to: user.phone,
        message: `Your matrimony verification OTP is: ${otp}. Valid for 10 minutes.`
      });

      res.json({
        success: true,
        message: 'OTP sent successfully'
      });
    } catch (smsError) {
      console.error('SMS sending failed:', smsError);
      res.status(500).json({
        success: false,
        message: 'Failed to send OTP. Please try again.'
      });
    }
  } catch (error) {
    console.error('Send OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send OTP'
    });
  }
});

// Verify phone OTP
router.post('/verify-phone-otp', [
  authenticateToken,
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP format',
        errors: errors.array()
      });
    }

    const { otp } = req.body;
    const user = req.user;

    if (!user.phoneVerificationOTP || !user.phoneVerificationExpires) {
      return res.status(400).json({
        success: false,
        message: 'No OTP found. Please request a new one.'
      });
    }

    if (new Date() > user.phoneVerificationExpires) {
      return res.status(400).json({
        success: false,
        message: 'OTP has expired. Please request a new one.'
      });
    }

    if (user.phoneVerificationOTP !== otp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP'
      });
    }

    // Mark phone as verified
    user.verificationStatus.phone = true;
    user.phoneVerificationOTP = undefined;
    user.phoneVerificationExpires = undefined;

    await user.save();

    res.json({
      success: true,
      message: 'Phone number verified successfully',
      data: {
        verificationStatus: user.verificationStatus
      }
    });
  } catch (error) {
    console.error('Verify OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'OTP verification failed'
    });
  }
});

// Verify email
router.get('/verify-email/:token', async (req, res) => {
  try {
    const { token } = req.params;

    const user = await User.findOne({ emailVerificationToken: token });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }

    user.verificationStatus.email = true;
    user.emailVerificationToken = undefined;

    await user.save();

    res.json({
      success: true,
      message: 'Email verified successfully'
    });
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Email verification failed'
    });
  }
});

// Get current user
router.get('/me', authenticateToken, async (req, res) => {
  try {
    const user = req.user;

    res.json({
      success: true,
      data: {
        user: {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          profileType: user.profileType,
          membershipType: user.membershipType,
          profilePicture: user.profilePicture,
          verificationStatus: user.verificationStatus,
          profileCompleted: user.profileCompleted,
          profileCompletionPercentage: user.profileCompletionPercentage,
          personalInfo: user.personalInfo,
          familyInfo: user.familyInfo,
          educationCareer: user.educationCareer,
          lifestyle: user.lifestyle,
          religiousInfo: user.religiousInfo,
          partnerPreferences: user.partnerPreferences,
          profileViews: user.profileViews,
          lastActive: user.lastActive,
          createdAt: user.createdAt
        }
      }
    });
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get user data'
    });
  }
});

// Logout (client-side token removal)
router.post('/logout', authenticateToken, (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

module.exports = router;
