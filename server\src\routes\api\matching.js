const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { query, validationResult } = require('express-validator');

// In-memory storage for matching data
let matchingPreferences = new Map();
let compatibilityScores = new Map();
let matchingHistory = new Map();

// Advanced matching algorithm
const calculateCompatibilityScore = (user1, user2) => {
  let score = 0;
  let factors = [];

  // Age compatibility (20 points)
  const ageDiff = Math.abs(user1.age - user2.age);
  if (ageDiff <= 2) {
    score += 20;
    factors.push({ factor: 'Age', score: 20, match: 'Perfect' });
  } else if (ageDiff <= 5) {
    score += 15;
    factors.push({ factor: 'Age', score: 15, match: 'Good' });
  } else if (ageDiff <= 8) {
    score += 10;
    factors.push({ factor: 'Age', score: 10, match: 'Fair' });
  } else {
    factors.push({ factor: 'Age', score: 0, match: 'Poor' });
  }

  // Location compatibility (15 points)
  if (user1.city === user2.city) {
    score += 15;
    factors.push({ factor: 'Location', score: 15, match: 'Same City' });
  } else if (user1.state === user2.state) {
    score += 10;
    factors.push({ factor: 'Location', score: 10, match: 'Same State' });
  } else {
    score += 5;
    factors.push({ factor: 'Location', score: 5, match: 'Different State' });
  }

  // Education compatibility (15 points)
  const educationLevels = {
    'high_school': 1,
    'diploma': 2,
    'bachelors': 3,
    'masters': 4,
    'phd': 5
  };
  
  const user1EduLevel = educationLevels[user1.education] || 3;
  const user2EduLevel = educationLevels[user2.education] || 3;
  const eduDiff = Math.abs(user1EduLevel - user2EduLevel);
  
  if (eduDiff === 0) {
    score += 15;
    factors.push({ factor: 'Education', score: 15, match: 'Same Level' });
  } else if (eduDiff === 1) {
    score += 12;
    factors.push({ factor: 'Education', score: 12, match: 'Similar Level' });
  } else {
    score += 8;
    factors.push({ factor: 'Education', score: 8, match: 'Different Level' });
  }

  // Religion compatibility (15 points)
  if (user1.religion === user2.religion) {
    score += 15;
    factors.push({ factor: 'Religion', score: 15, match: 'Same Religion' });
    
    // Caste compatibility (within same religion)
    if (user1.caste === user2.caste) {
      score += 5;
      factors.push({ factor: 'Caste', score: 5, match: 'Same Caste' });
    }
  } else {
    factors.push({ factor: 'Religion', score: 0, match: 'Different Religion' });
  }

  // Income compatibility (10 points)
  const incomeRatio = Math.min(user1.income, user2.income) / Math.max(user1.income, user2.income);
  if (incomeRatio >= 0.8) {
    score += 10;
    factors.push({ factor: 'Income', score: 10, match: 'Similar Income' });
  } else if (incomeRatio >= 0.5) {
    score += 7;
    factors.push({ factor: 'Income', score: 7, match: 'Moderate Difference' });
  } else {
    score += 3;
    factors.push({ factor: 'Income', score: 3, match: 'Significant Difference' });
  }

  // Lifestyle compatibility (10 points)
  let lifestyleScore = 0;
  if (user1.smoking === user2.smoking) lifestyleScore += 3;
  if (user1.drinking === user2.drinking) lifestyleScore += 3;
  if (user1.diet === user2.diet) lifestyleScore += 4;
  
  score += lifestyleScore;
  factors.push({ factor: 'Lifestyle', score: lifestyleScore, match: `${lifestyleScore}/10 factors match` });

  // Family values compatibility (10 points)
  if (user1.familyType === user2.familyType) {
    score += 5;
  }
  if (user1.familyValues === user2.familyValues) {
    score += 5;
  }
  factors.push({ factor: 'Family Values', score: Math.min(10, score - (score - 10)), match: 'Calculated' });

  // Language compatibility (5 points)
  const commonLanguages = user1.languages?.filter(lang => user2.languages?.includes(lang)) || [];
  const languageScore = Math.min(5, commonLanguages.length * 2);
  score += languageScore;
  factors.push({ factor: 'Languages', score: languageScore, match: `${commonLanguages.length} common` });

  return {
    totalScore: Math.min(100, score),
    factors,
    recommendation: score >= 80 ? 'Excellent Match' : 
                   score >= 65 ? 'Good Match' : 
                   score >= 50 ? 'Fair Match' : 'Poor Match'
  };
};

// Get AI-powered recommendations
router.get('/recommendations', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 50 }).toInt(),
  query('minScore').optional().isInt({ min: 0, max: 100 }).toInt()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { limit = 20, minScore = 50 } = req.query;

    const currentUser = await User.findByPk(userId);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Get all potential matches (excluding current user)
    const allUsers = await User.findAll({
      where: {
        id: { [require('sequelize').Op.ne]: userId },
        isActive: true
      }
    });

    // Calculate compatibility scores
    const recommendations = [];
    
    for (const user of allUsers) {
      const compatibility = calculateCompatibilityScore(currentUser, user);
      
      if (compatibility.totalScore >= minScore) {
        recommendations.push({
          id: user.id,
          name: user.name,
          age: user.age,
          location: `${user.city}, ${user.state}`,
          education: user.education,
          profession: user.profession,
          religion: user.religion,
          profilePicture: user.profilePicture,
          compatibilityScore: compatibility.totalScore,
          compatibilityFactors: compatibility.factors,
          recommendation: compatibility.recommendation,
          isOnline: user.lastActiveAt && (Date.now() - new Date(user.lastActiveAt).getTime()) < 15 * 60 * 1000,
          isPremium: user.membershipType !== 'free',
          isVerified: user.isVerified || false
        });
      }
    }

    // Sort by compatibility score
    recommendations.sort((a, b) => b.compatibilityScore - a.compatibilityScore);

    // Limit results
    const limitedRecommendations = recommendations.slice(0, limit);

    res.json({
      success: true,
      data: {
        recommendations: limitedRecommendations,
        totalFound: recommendations.length,
        averageScore: recommendations.length > 0 ? 
          Math.round(recommendations.reduce((sum, r) => sum + r.compatibilityScore, 0) / recommendations.length) : 0
      }
    });

  } catch (error) {
    console.error('Get recommendations error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recommendations'
    });
  }
});

// Get detailed compatibility analysis
router.get('/compatibility/:userId', authenticateToken, async (req, res) => {
  try {
    const currentUserId = req.user.id;
    const { userId } = req.params;

    if (currentUserId === userId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot analyze compatibility with yourself'
      });
    }

    const currentUser = await User.findByPk(currentUserId);
    const otherUser = await User.findByPk(userId);

    if (!currentUser || !otherUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    const compatibility = calculateCompatibilityScore(currentUser, otherUser);

    // Add detailed analysis
    const analysis = {
      overallScore: compatibility.totalScore,
      recommendation: compatibility.recommendation,
      factors: compatibility.factors,
      strengths: compatibility.factors.filter(f => f.score >= 10).map(f => f.factor),
      concerns: compatibility.factors.filter(f => f.score < 5).map(f => f.factor),
      suggestions: []
    };

    // Add suggestions based on compatibility
    if (compatibility.totalScore >= 80) {
      analysis.suggestions.push('This is an excellent match! Consider sending an interest.');
    } else if (compatibility.totalScore >= 65) {
      analysis.suggestions.push('Good compatibility. You might want to learn more about each other.');
    } else if (compatibility.totalScore >= 50) {
      analysis.suggestions.push('Fair match. Consider if the differences are acceptable to you.');
    } else {
      analysis.suggestions.push('Low compatibility. You might want to look for better matches.');
    }

    res.json({
      success: true,
      data: {
        compatibility: analysis,
        otherUser: {
          id: otherUser.id,
          name: otherUser.name,
          age: otherUser.age,
          location: `${otherUser.city}, ${otherUser.state}`,
          profilePicture: otherUser.profilePicture
        }
      }
    });

  } catch (error) {
    console.error('Get compatibility analysis error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get compatibility analysis'
    });
  }
});

// Get trending profiles
router.get('/trending', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 20 }).toInt(),
  query('timeframe').optional().isIn(['today', 'week', 'month'])
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { limit = 10, timeframe = 'week' } = req.query;

    // Get all users except current user
    const allUsers = await User.findAll({
      where: {
        id: { [require('sequelize').Op.ne]: userId },
        isActive: true
      }
    });

    // Calculate trending score based on various factors
    const trendingProfiles = allUsers.map(user => {
      let trendingScore = 0;

      // Recent activity (40 points)
      if (user.lastActiveAt) {
        const hoursSinceActive = (Date.now() - new Date(user.lastActiveAt).getTime()) / (1000 * 60 * 60);
        if (hoursSinceActive < 24) trendingScore += 40;
        else if (hoursSinceActive < 72) trendingScore += 30;
        else if (hoursSinceActive < 168) trendingScore += 20;
      }

      // Profile completeness (20 points)
      trendingScore += (user.profileCompletionPercentage || 0) * 0.2;

      // Premium membership (15 points)
      if (user.membershipType !== 'free') trendingScore += 15;

      // Verification status (15 points)
      if (user.isVerified) trendingScore += 15;

      // Recent profile updates (10 points)
      if (user.updatedAt && (Date.now() - new Date(user.updatedAt).getTime()) < 7 * 24 * 60 * 60 * 1000) {
        trendingScore += 10;
      }

      return {
        id: user.id,
        name: user.name,
        age: user.age,
        location: `${user.city}, ${user.state}`,
        education: user.education,
        profession: user.profession,
        profilePicture: user.profilePicture,
        trendingScore,
        isOnline: user.lastActiveAt && (Date.now() - new Date(user.lastActiveAt).getTime()) < 15 * 60 * 1000,
        isPremium: user.membershipType !== 'free',
        isVerified: user.isVerified || false,
        profileCompletionPercentage: user.profileCompletionPercentage || 0
      };
    });

    // Sort by trending score
    trendingProfiles.sort((a, b) => b.trendingScore - a.trendingScore);

    // Limit results
    const limitedProfiles = trendingProfiles.slice(0, limit);

    res.json({
      success: true,
      data: {
        trendingProfiles: limitedProfiles,
        timeframe,
        totalProfiles: trendingProfiles.length
      }
    });

  } catch (error) {
    console.error('Get trending profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get trending profiles'
    });
  }
});

// Get recently viewed profiles
router.get('/recently-viewed', authenticateToken, [
  query('limit').optional().isInt({ min: 1, max: 20 }).toInt()
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { limit = 10 } = req.query;

    // Get user's viewing history
    const viewingHistory = matchingHistory.get(userId) || [];
    
    // Sort by most recent first
    viewingHistory.sort((a, b) => new Date(b.viewedAt) - new Date(a.viewedAt));
    
    // Get unique profiles (remove duplicates)
    const uniqueViews = [];
    const seenIds = new Set();
    
    for (const view of viewingHistory) {
      if (!seenIds.has(view.profileId)) {
        seenIds.add(view.profileId);
        uniqueViews.push(view);
      }
    }

    // Limit results
    const limitedViews = uniqueViews.slice(0, limit);

    // Get full user details
    const recentlyViewed = [];
    for (const view of limitedViews) {
      const user = await User.findByPk(view.profileId);
      if (user) {
        recentlyViewed.push({
          id: user.id,
          name: user.name,
          age: user.age,
          location: `${user.city}, ${user.state}`,
          profilePicture: user.profilePicture,
          viewedAt: view.viewedAt,
          viewCount: viewingHistory.filter(v => v.profileId === user.id).length
        });
      }
    }

    res.json({
      success: true,
      data: {
        recentlyViewed,
        totalViewed: uniqueViews.length
      }
    });

  } catch (error) {
    console.error('Get recently viewed profiles error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recently viewed profiles'
    });
  }
});

// Record profile view
router.post('/view/:profileId', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const { profileId } = req.params;

    if (userId === profileId) {
      return res.status(400).json({
        success: false,
        message: 'Cannot view your own profile'
      });
    }

    // Check if profile exists
    const profile = await User.findByPk(profileId);
    if (!profile) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    // Record the view
    const userHistory = matchingHistory.get(userId) || [];
    userHistory.push({
      profileId,
      viewedAt: new Date().toISOString()
    });
    matchingHistory.set(userId, userHistory);

    res.json({
      success: true,
      message: 'Profile view recorded',
      data: { profileId, viewedAt: new Date().toISOString() }
    });

  } catch (error) {
    console.error('Record profile view error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to record profile view'
    });
  }
});

module.exports = router;
