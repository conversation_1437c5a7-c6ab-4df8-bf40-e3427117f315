const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../../middleware/auth');
const User = require('../../models/sequelize/User');
const { body, validationResult } = require('express-validator');
const crypto = require('crypto');

// In-memory storage for OTPs (in production, use Redis)
const otpStorage = new Map();

// Generate OTP
const generateOTP = () => {
  return Math.floor(100000 + Math.random() * 900000).toString();
};

// Send email OTP
router.post('/email/send-otp', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.emailVerifiedAt) {
      return res.status(400).json({
        success: false,
        message: 'Email already verified'
      });
    }

    const otp = generateOTP();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

    // Store OTP
    otpStorage.set(`email_${userId}`, { otp, expiresAt });

    // In production, send actual email
    console.log(`Email OTP for ${user.email}: ${otp}`);

    res.json({
      success: true,
      message: 'OTP sent to your email address',
      data: { 
        email: user.email.replace(/(.{2})(.*)(@.*)/, '$1***$3'),
        expiresIn: 600 // 10 minutes in seconds
      }
    });

  } catch (error) {
    console.error('Send email OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send email OTP'
    });
  }
});

// Verify email OTP
router.post('/email/verify-otp', authenticateToken, [
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { otp } = req.body;

    const storedOTP = otpStorage.get(`email_${userId}`);
    if (!storedOTP) {
      return res.status(400).json({
        success: false,
        message: 'No OTP found. Please request a new one.'
      });
    }

    if (Date.now() > storedOTP.expiresAt) {
      otpStorage.delete(`email_${userId}`);
      return res.status(400).json({
        success: false,
        message: 'OTP has expired. Please request a new one.'
      });
    }

    if (storedOTP.otp !== otp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP. Please try again.'
      });
    }

    // Verify email
    const user = await User.findByPk(userId);
    user.emailVerifiedAt = new Date();
    user.calculateProfileCompletion();
    await user.save();

    // Clean up OTP
    otpStorage.delete(`email_${userId}`);

    res.json({
      success: true,
      message: 'Email verified successfully',
      data: { emailVerified: true }
    });

  } catch (error) {
    console.error('Verify email OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify email OTP'
    });
  }
});

// Send phone OTP
router.post('/phone/send-otp', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.phoneVerifiedAt) {
      return res.status(400).json({
        success: false,
        message: 'Phone already verified'
      });
    }

    const otp = generateOTP();
    const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

    // Store OTP
    otpStorage.set(`phone_${userId}`, { otp, expiresAt });

    // In production, send actual SMS
    console.log(`Phone OTP for ${user.phone}: ${otp}`);

    res.json({
      success: true,
      message: 'OTP sent to your phone number',
      data: { 
        phone: user.phone.replace(/(\+\d{2})(\d{3})(\d{3})(\d{4})/, '$1***$4'),
        expiresIn: 600 // 10 minutes in seconds
      }
    });

  } catch (error) {
    console.error('Send phone OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send phone OTP'
    });
  }
});

// Verify phone OTP
router.post('/phone/verify-otp', authenticateToken, [
  body('otp').isLength({ min: 6, max: 6 }).withMessage('OTP must be 6 digits')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { otp } = req.body;

    const storedOTP = otpStorage.get(`phone_${userId}`);
    if (!storedOTP) {
      return res.status(400).json({
        success: false,
        message: 'No OTP found. Please request a new one.'
      });
    }

    if (Date.now() > storedOTP.expiresAt) {
      otpStorage.delete(`phone_${userId}`);
      return res.status(400).json({
        success: false,
        message: 'OTP has expired. Please request a new one.'
      });
    }

    if (storedOTP.otp !== otp) {
      return res.status(400).json({
        success: false,
        message: 'Invalid OTP. Please try again.'
      });
    }

    // Verify phone
    const user = await User.findByPk(userId);
    user.phoneVerifiedAt = new Date();
    user.calculateProfileCompletion();
    await user.save();

    // Clean up OTP
    otpStorage.delete(`phone_${userId}`);

    res.json({
      success: true,
      message: 'Phone verified successfully',
      data: { phoneVerified: true }
    });

  } catch (error) {
    console.error('Verify phone OTP error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to verify phone OTP'
    });
  }
});

// Get verification status
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: {
        emailVerified: !!user.emailVerifiedAt,
        phoneVerified: !!user.phoneVerifiedAt,
        profileVerified: user.isVerified || false,
        documentVerified: false, // Implement document verification
        photoVerified: false // Implement photo verification
      }
    });

  } catch (error) {
    console.error('Get verification status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get verification status'
    });
  }
});

// Request profile verification
router.post('/profile/request', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (user.isVerified) {
      return res.status(400).json({
        success: false,
        message: 'Profile already verified'
      });
    }

    // Check if profile is complete enough for verification
    if (user.profileCompletionPercentage < 80) {
      return res.status(400).json({
        success: false,
        message: 'Profile must be at least 80% complete for verification'
      });
    }

    // In production, this would create a verification request for admin review
    user.verificationRequested = true;
    user.verificationRequestedAt = new Date();
    await user.save();

    res.json({
      success: true,
      message: 'Profile verification request submitted. We will review and update within 24-48 hours.',
      data: { verificationRequested: true }
    });

  } catch (error) {
    console.error('Request profile verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to request profile verification'
    });
  }
});

module.exports = router;
