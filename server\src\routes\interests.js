const express = require('express');
const { body, query, validationResult } = require('express-validator');
const Interest = require('../models/Interest');
const User = require('../models/User');
const { authenticateToken, requireCompleteProfile, checkUsageLimit } = require('../middleware/auth');
const { sendEmail } = require('../services/emailService');
const { sendSMS } = require('../services/smsService');

const router = express.Router();

// Send interest
router.post('/send', [
  authenticateToken,
  requireCompleteProfile,
  checkUsageLimit('interestsPerDay'),
  body('to').isMongoId().withMessage('Invalid user ID'),
  body('message').optional().isLength({ max: 500 }).withMessage('Message too long'),
  body('interestType').optional().isIn(['express_interest', 'send_message', 'request_contact', 'request_photo']).withMessage('Invalid interest type')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { to, message, interestType = 'express_interest' } = req.body;
    const from = req.user._id;

    // Check if target user exists and is active
    const targetUser = await User.findById(to);
    if (!targetUser || !targetUser.isActive || targetUser.isBlocked) {
      return res.status(404).json({
        success: false,
        message: 'User not found or not available'
      });
    }

    // Check if current user is blocked by target user
    if (targetUser.blockedUsers.includes(from)) {
      return res.status(403).json({
        success: false,
        message: 'You cannot send interest to this user'
      });
    }

    // Check if interest already exists
    const existingInterest = await Interest.findOne({
      from,
      to,
      status: { $in: ['pending', 'accepted'] }
    });

    if (existingInterest) {
      return res.status(400).json({
        success: false,
        message: 'Interest already sent to this user'
      });
    }

    // Check if user is sending interest to themselves
    if (from.toString() === to.toString()) {
      return res.status(400).json({
        success: false,
        message: 'You cannot send interest to yourself'
      });
    }

    // Create interest
    const interest = new Interest({
      from,
      to,
      message,
      interestType,
      isPremium: req.user.membershipType !== 'free'
    });

    await interest.save();

    // Increment usage count
    if (req.subscription) {
      await req.subscription.incrementUsage('interestsPerDay');
    }

    // Send notification to target user
    try {
      await sendEmail({
        to: targetUser.email,
        subject: 'New Interest Received - Matrimony Platform',
        template: 'newInterest',
        data: {
          receiverName: targetUser.name,
          senderName: req.user.name,
          message: message || 'Someone has expressed interest in your profile',
          profileLink: `${process.env.CLIENT_URL}/profile/${req.user._id}`
        }
      });
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
    }

    // Populate sender info for response
    await interest.populate('from', 'name profilePicture membershipType verificationStatus');

    res.status(201).json({
      success: true,
      message: 'Interest sent successfully',
      data: {
        interest
      }
    });
  } catch (error) {
    console.error('Send interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send interest'
    });
  }
});

// Get received interests
router.get('/received', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('status').optional().isIn(['pending', 'accepted', 'declined']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { page = 1, limit = 20, status } = req.query;
    const userId = req.user._id;

    const query = { to: userId };
    if (status) {
      query.status = status;
    }

    const interests = await Interest.find(query)
      .populate('from', 'name profilePicture membershipType verificationStatus personalInfo.age personalInfo.city educationCareer.occupation')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalCount = await Interest.countDocuments(query);

    // Mark interests as viewed
    await Interest.updateMany(
      { to: userId, viewedAt: { $exists: false } },
      { viewedAt: new Date() }
    );

    res.json({
      success: true,
      data: {
        interests,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get received interests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get received interests'
    });
  }
});

// Get sent interests
router.get('/sent', [
  authenticateToken,
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('Limit must be between 1 and 50'),
  query('status').optional().isIn(['pending', 'accepted', 'declined']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { page = 1, limit = 20, status } = req.query;
    const userId = req.user._id;

    const query = { from: userId };
    if (status) {
      query.status = status;
    }

    const interests = await Interest.find(query)
      .populate('to', 'name profilePicture membershipType verificationStatus personalInfo.age personalInfo.city educationCareer.occupation')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));

    const totalCount = await Interest.countDocuments(query);

    res.json({
      success: true,
      data: {
        interests,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(totalCount / limit),
          totalResults: totalCount,
          hasNext: page * limit < totalCount,
          hasPrev: page > 1
        }
      }
    });
  } catch (error) {
    console.error('Get sent interests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get sent interests'
    });
  }
});

// Respond to interest
router.put('/:interestId/respond', [
  authenticateToken,
  body('status').isIn(['accepted', 'declined']).withMessage('Status must be accepted or declined'),
  body('responseMessage').optional().isLength({ max: 500 }).withMessage('Response message too long')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { interestId } = req.params;
    const { status, responseMessage } = req.body;
    const userId = req.user._id;

    const interest = await Interest.findById(interestId).populate('from', 'name email');

    if (!interest) {
      return res.status(404).json({
        success: false,
        message: 'Interest not found'
      });
    }

    // Check if current user is the recipient
    if (interest.to.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only respond to interests sent to you'
      });
    }

    // Check if interest is still pending
    if (interest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Interest has already been responded to'
      });
    }

    // Check if interest has expired
    if (interest.isExpired()) {
      return res.status(400).json({
        success: false,
        message: 'Interest has expired'
      });
    }

    // Update interest
    interest.status = status;
    interest.responseMessage = responseMessage;
    interest.respondedAt = new Date();

    await interest.save();

    // Send notification to sender
    try {
      const notificationMessage = status === 'accepted' 
        ? 'Your interest has been accepted!' 
        : 'Your interest has been declined.';

      await sendEmail({
        to: interest.from.email,
        subject: `Interest ${status.charAt(0).toUpperCase() + status.slice(1)} - Matrimony Platform`,
        template: 'interestResponse',
        data: {
          senderName: interest.from.name,
          receiverName: req.user.name,
          status,
          message: responseMessage || notificationMessage,
          profileLink: `${process.env.CLIENT_URL}/profile/${req.user._id}`
        }
      });
    } catch (emailError) {
      console.error('Email notification failed:', emailError);
    }

    res.json({
      success: true,
      message: `Interest ${status} successfully`,
      data: {
        interest
      }
    });
  } catch (error) {
    console.error('Respond to interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to respond to interest'
    });
  }
});

// Withdraw interest
router.delete('/:interestId', authenticateToken, async (req, res) => {
  try {
    const { interestId } = req.params;
    const userId = req.user._id;

    const interest = await Interest.findById(interestId);

    if (!interest) {
      return res.status(404).json({
        success: false,
        message: 'Interest not found'
      });
    }

    // Check if current user is the sender
    if (interest.from.toString() !== userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'You can only withdraw interests you sent'
      });
    }

    // Check if interest can be withdrawn
    if (!interest.canWithdraw()) {
      return res.status(400).json({
        success: false,
        message: 'Interest cannot be withdrawn'
      });
    }

    interest.status = 'withdrawn';
    await interest.save();

    res.json({
      success: true,
      message: 'Interest withdrawn successfully'
    });
  } catch (error) {
    console.error('Withdraw interest error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to withdraw interest'
    });
  }
});

// Get interest statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    const stats = await Interest.getInterestStats(userId);

    res.json({
      success: true,
      data: {
        stats
      }
    });
  } catch (error) {
    console.error('Get interest stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get interest statistics'
    });
  }
});

// Get mutual interests
router.get('/mutual', authenticateToken, async (req, res) => {
  try {
    const userId = req.user._id;

    // Find users who sent interest to current user and current user also sent interest to them
    const mutualInterests = await Interest.aggregate([
      {
        $match: {
          $or: [
            { from: userId, status: 'accepted' },
            { to: userId, status: 'accepted' }
          ]
        }
      },
      {
        $group: {
          _id: {
            $cond: [
              { $eq: ['$from', userId] },
              '$to',
              '$from'
            ]
          },
          interests: { $push: '$$ROOT' }
        }
      },
      {
        $match: {
          'interests.1': { $exists: true } // At least 2 interests (mutual)
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: '_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          user: {
            _id: 1,
            name: 1,
            profilePicture: 1,
            membershipType: 1,
            'personalInfo.age': 1,
            'personalInfo.city': 1,
            'educationCareer.occupation': 1
          },
          interests: 1
        }
      }
    ]);

    res.json({
      success: true,
      data: {
        mutualInterests
      }
    });
  } catch (error) {
    console.error('Get mutual interests error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get mutual interests'
    });
  }
});

module.exports = router;
