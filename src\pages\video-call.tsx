import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  CardBody,
  Button,
  Avatar,
  Chip,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Spinner
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { useToast } from '../contexts/toast-context';

interface CallState {
  status: 'connecting' | 'ringing' | 'connected' | 'ended';
  duration: number;
  isMuted: boolean;
  isVideoOff: boolean;
  isScreenSharing: boolean;
}

interface CallParticipant {
  id: string;
  name: string;
  profilePicture?: string;
  isOnline: boolean;
}

export const VideoCallPage: React.FC = () => {
  const { callId } = useParams<{ callId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showToast } = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();

  const [callState, setCallState] = React.useState<CallState>({
    status: 'connecting',
    duration: 0,
    isMuted: false,
    isVideoOff: false,
    isScreenSharing: false
  });

  const [otherParticipant, setOtherParticipant] = React.useState<CallParticipant | null>(null);
  const [localStream, setLocalStream] = React.useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = React.useState<MediaStream | null>(null);
  const [connectionQuality, setConnectionQuality] = React.useState<'excellent' | 'good' | 'poor'>('good');

  const localVideoRef = React.useRef<HTMLVideoElement>(null);
  const remoteVideoRef = React.useRef<HTMLVideoElement>(null);
  const durationInterval = React.useRef<NodeJS.Timeout>();

  // Initialize call
  React.useEffect(() => {
    initializeCall();
    return () => {
      cleanup();
    };
  }, [callId]);

  // Duration timer
  React.useEffect(() => {
    if (callState.status === 'connected') {
      durationInterval.current = setInterval(() => {
        setCallState(prev => ({ ...prev, duration: prev.duration + 1 }));
      }, 1000);
    } else {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    }

    return () => {
      if (durationInterval.current) {
        clearInterval(durationInterval.current);
      }
    };
  }, [callState.status]);

  const initializeCall = async () => {
    try {
      // Mock call initialization
      setOtherParticipant({
        id: 'user_2',
        name: 'Priya Gupta',
        profilePicture: 'https://img.heroui.chat/image/avatar?w=400&h=400&u=2',
        isOnline: true
      });

      // Get user media
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      setLocalStream(stream);
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }

      // Simulate call progression
      setTimeout(() => {
        setCallState(prev => ({ ...prev, status: 'ringing' }));
      }, 1000);

      setTimeout(() => {
        setCallState(prev => ({ ...prev, status: 'connected' }));
        // Mock remote stream
        setRemoteStream(stream); // In real app, this would be the remote stream
        if (remoteVideoRef.current) {
          remoteVideoRef.current.srcObject = stream;
        }
      }, 3000);

    } catch (error) {
      console.error('Failed to initialize call:', error);
      showToast('Failed to access camera/microphone', 'error');
      navigate('/dashboard');
    }
  };

  const cleanup = () => {
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
    }
    if (durationInterval.current) {
      clearInterval(durationInterval.current);
    }
  };

  const toggleMute = () => {
    if (localStream) {
      const audioTrack = localStream.getAudioTracks()[0];
      if (audioTrack) {
        audioTrack.enabled = callState.isMuted;
        setCallState(prev => ({ ...prev, isMuted: !prev.isMuted }));
      }
    }
  };

  const toggleVideo = () => {
    if (localStream) {
      const videoTrack = localStream.getVideoTracks()[0];
      if (videoTrack) {
        videoTrack.enabled = callState.isVideoOff;
        setCallState(prev => ({ ...prev, isVideoOff: !prev.isVideoOff }));
      }
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (!callState.isScreenSharing) {
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true
        });
        
        // Replace video track with screen share
        if (localVideoRef.current) {
          localVideoRef.current.srcObject = screenStream;
        }
        
        setCallState(prev => ({ ...prev, isScreenSharing: true }));
        
        // Listen for screen share end
        screenStream.getVideoTracks()[0].onended = () => {
          setCallState(prev => ({ ...prev, isScreenSharing: false }));
          if (localVideoRef.current && localStream) {
            localVideoRef.current.srcObject = localStream;
          }
        };
      } else {
        // Stop screen sharing
        if (localVideoRef.current && localStream) {
          localVideoRef.current.srcObject = localStream;
        }
        setCallState(prev => ({ ...prev, isScreenSharing: false }));
      }
    } catch (error) {
      console.error('Screen share error:', error);
      showToast('Failed to share screen', 'error');
    }
  };

  const endCall = () => {
    setCallState(prev => ({ ...prev, status: 'ended' }));
    cleanup();
    navigate('/dashboard');
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const getConnectionQualityColor = () => {
    switch (connectionQuality) {
      case 'excellent': return 'success';
      case 'good': return 'primary';
      case 'poor': return 'danger';
      default: return 'default';
    }
  };

  if (callState.status === 'connecting') {
    return (
      <div className="fixed inset-0 bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <Spinner size="lg" color="primary" className="mb-4" />
          <h2 className="text-xl font-semibold mb-2">Connecting...</h2>
          <p className="text-gray-300">Setting up your call</p>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-black/50 backdrop-blur-sm">
        <div className="flex items-center gap-3">
          {otherParticipant && (
            <>
              <Avatar
                src={otherParticipant.profilePicture}
                size="sm"
                fallback={<Icon icon="lucide:user" />}
              />
              <div>
                <h3 className="text-white font-semibold">{otherParticipant.name}</h3>
                <div className="flex items-center gap-2">
                  <Chip
                    size="sm"
                    color={getConnectionQualityColor()}
                    variant="flat"
                  >
                    {connectionQuality}
                  </Chip>
                  {callState.status === 'connected' && (
                    <span className="text-sm text-gray-300">
                      {formatDuration(callState.duration)}
                    </span>
                  )}
                </div>
              </div>
            </>
          )}
        </div>

        <Button
          isIconOnly
          variant="light"
          className="text-white"
          onPress={() => navigate('/dashboard')}
        >
          <Icon icon="lucide:minimize-2" size={20} />
        </Button>
      </div>

      {/* Video Area */}
      <div className="flex-1 relative">
        {/* Remote Video */}
        <div className="absolute inset-0">
          {callState.status === 'connected' && remoteStream ? (
            <video
              ref={remoteVideoRef}
              autoPlay
              playsInline
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gray-900 flex items-center justify-center">
              {callState.status === 'ringing' ? (
                <div className="text-center text-white">
                  <Avatar
                    src={otherParticipant?.profilePicture}
                    size="lg"
                    className="mb-4 mx-auto"
                    fallback={<Icon icon="lucide:user" size={32} />}
                  />
                  <h2 className="text-xl font-semibold mb-2">Calling {otherParticipant?.name}...</h2>
                  <div className="flex justify-center">
                    <div className="animate-pulse flex space-x-1">
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                      <div className="w-2 h-2 bg-primary rounded-full animation-delay-200"></div>
                      <div className="w-2 h-2 bg-primary rounded-full animation-delay-400"></div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center text-white">
                  <Icon icon="lucide:video-off" size={48} className="mb-4 mx-auto text-gray-500" />
                  <p className="text-gray-400">Video unavailable</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Local Video (Picture-in-Picture) */}
        <div className="absolute top-4 right-4 w-32 h-24 bg-gray-800 rounded-lg overflow-hidden border-2 border-white/20">
          {localStream && !callState.isVideoOff ? (
            <video
              ref={localVideoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover scale-x-[-1]"
            />
          ) : (
            <div className="w-full h-full bg-gray-700 flex items-center justify-center">
              <Icon icon="lucide:video-off" className="text-gray-400" size={20} />
            </div>
          )}
        </div>

        {/* Call Status Overlay */}
        {callState.status !== 'connected' && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="text-center text-white">
              {callState.status === 'ringing' && (
                <>
                  <Icon icon="lucide:phone" size={48} className="mb-4 mx-auto animate-pulse" />
                  <h2 className="text-xl font-semibold">Ringing...</h2>
                </>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Controls */}
      <div className="p-6 bg-black/50 backdrop-blur-sm">
        <div className="flex items-center justify-center gap-4">
          {/* Mute */}
          <Button
            isIconOnly
            size="lg"
            color={callState.isMuted ? 'danger' : 'default'}
            variant={callState.isMuted ? 'solid' : 'flat'}
            onPress={toggleMute}
            className="text-white"
          >
            <Icon icon={callState.isMuted ? "lucide:mic-off" : "lucide:mic"} size={24} />
          </Button>

          {/* Video */}
          <Button
            isIconOnly
            size="lg"
            color={callState.isVideoOff ? 'danger' : 'default'}
            variant={callState.isVideoOff ? 'solid' : 'flat'}
            onPress={toggleVideo}
            className="text-white"
          >
            <Icon icon={callState.isVideoOff ? "lucide:video-off" : "lucide:video"} size={24} />
          </Button>

          {/* Screen Share */}
          <Button
            isIconOnly
            size="lg"
            color={callState.isScreenSharing ? 'primary' : 'default'}
            variant={callState.isScreenSharing ? 'solid' : 'flat'}
            onPress={toggleScreenShare}
            className="text-white"
          >
            <Icon icon="lucide:screen-share" size={24} />
          </Button>

          {/* End Call */}
          <Button
            isIconOnly
            size="lg"
            color="danger"
            onPress={endCall}
          >
            <Icon icon="lucide:phone-off" size={24} />
          </Button>

          {/* More Options */}
          <Button
            isIconOnly
            size="lg"
            variant="flat"
            onPress={onOpen}
            className="text-white"
          >
            <Icon icon="lucide:more-horizontal" size={24} />
          </Button>
        </div>
      </div>

      {/* More Options Modal */}
      <Modal isOpen={isOpen} onClose={onClose} className="dark">
        <ModalContent>
          <ModalHeader>Call Options</ModalHeader>
          <ModalBody>
            <div className="space-y-3">
              <Button
                variant="flat"
                startContent={<Icon icon="lucide:message-circle" />}
                className="w-full justify-start"
                onPress={() => {
                  onClose();
                  // Open chat
                }}
              >
                Send Message
              </Button>
              
              <Button
                variant="flat"
                startContent={<Icon icon="lucide:user-plus" />}
                className="w-full justify-start"
                onPress={() => {
                  onClose();
                  // Add to contacts
                }}
              >
                Add to Contacts
              </Button>
              
              <Button
                variant="flat"
                startContent={<Icon icon="lucide:flag" />}
                className="w-full justify-start"
                onPress={() => {
                  onClose();
                  // Report user
                }}
              >
                Report User
              </Button>
              
              <Button
                variant="flat"
                startContent={<Icon icon="lucide:settings" />}
                className="w-full justify-start"
                onPress={() => {
                  onClose();
                  // Open settings
                }}
              >
                Call Settings
              </Button>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
