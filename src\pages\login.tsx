import React from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { Card, CardBody, CardHeader, Input, Button, Checkbox, Divider, Tabs, Tab } from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

export const LoginPage: React.FC = () => {
  const [email, setEmail] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [phone, setPhone] = React.useState('');
  const [otp, setOtp] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  const [rememberMe, setRememberMe] = React.useState(false);
  const [otpSent, setOtpSent] = React.useState(false);
  const [selectedTab, setSelectedTab] = React.useState('email');

  const { login, verifyOtp } = useAuth();
  const history = useHistory();
  const location = useLocation();
  const { from } = location.state || { from: { pathname: '/dashboard' } };

  const handleEmailLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      await login(email, password);
      history.replace(from);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendOtp = async () => {
    if (!phone || phone.length < 10) {
      setError('Please enter a valid phone number');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      // Simulate OTP sending
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setOtpSent(true);
    } catch (err) {
      setError('Failed to send OTP. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      const isValid = await verifyOtp(otp);
      
      if (isValid) {
        history.replace(from);
      } else {
        setError('Invalid OTP. Please try again.');
      }
    } catch (err) {
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="flex flex-col gap-1 items-center">
          <h1 className="text-2xl font-bold">Welcome Back</h1>
          <p className="text-default-500 text-sm">Login to continue your search for a perfect match</p>
        </CardHeader>
        <CardBody>
          <Tabs 
            aria-label="Login Options" 
            selectedKey={selectedTab} 
            onSelectionChange={setSelectedTab as any}
            className="mb-4"
          >
            <Tab key="email" title="Email Login" />
            <Tab key="phone" title="Phone Login" />
          </Tabs>

          {error && (
            <div className="bg-danger-50 text-danger p-3 rounded-md mb-4 text-sm">
              {error}
            </div>
          )}

          {selectedTab === 'email' ? (
            <form onSubmit={handleEmailLogin} className="space-y-4">
              <Input
                type="email"
                label="Email"
                placeholder="Enter your email"
                value={email}
                onValueChange={setEmail}
                startContent={<Icon icon="lucide:mail" className="text-default-400" />}
                isRequired
              />
              
              <Input
                type="password"
                label="Password"
                placeholder="Enter your password"
                value={password}
                onValueChange={setPassword}
                startContent={<Icon icon="lucide:lock" className="text-default-400" />}
                isRequired
              />
              
              <div className="flex justify-between items-center">
                <Checkbox 
                  size="sm" 
                  isSelected={rememberMe}
                  onValueChange={setRememberMe}
                >
                  <span className="text-sm">Remember me</span>
                </Checkbox>
                <Link to="/forgot-password" className="text-primary text-sm">
                  Forgot password?
                </Link>
              </div>
              
              <Button 
                type="submit" 
                color="primary" 
                className="w-full"
                isLoading={isLoading}
              >
                Login
              </Button>
              
              <div className="relative flex items-center my-4">
                <div className="flex-grow border-t border-gray-300"></div>
                <span className="flex-shrink mx-4 text-gray-400 text-sm">or continue with</span>
                <div className="flex-grow border-t border-gray-300"></div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <Button 
                  variant="bordered" 
                  startContent={<Icon icon="logos:google-icon" />}
                  className="w-full"
                >
                  Google
                </Button>
                <Button 
                  variant="bordered" 
                  startContent={<Icon icon="logos:facebook" />}
                  className="w-full"
                >
                  Facebook
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-4">
              {!otpSent ? (
                <>
                  <Input
                    type="tel"
                    label="Phone Number"
                    placeholder="Enter your phone number"
                    value={phone}
                    onValueChange={setPhone}
                    startContent={<span className="text-default-400">+91</span>}
                    isRequired
                  />
                  
                  <Button 
                    color="primary" 
                    className="w-full"
                    isLoading={isLoading}
                    onPress={handleSendOtp}
                  >
                    Send OTP
                  </Button>
                </>
              ) : (
                <form onSubmit={handleVerifyOtp} className="space-y-4">
                  <Input
                    type="text"
                    label="OTP"
                    placeholder="Enter 6-digit OTP"
                    value={otp}
                    onValueChange={setOtp}
                    maxLength={6}
                    isRequired
                  />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-default-500">
                      Didn't receive OTP?
                    </span>
                    <Button 
                      variant="light" 
                      color="primary" 
                      size="sm"
                      onPress={() => {
                        setOtpSent(false);
                        setOtp('');
                      }}
                    >
                      Resend
                    </Button>
                  </div>
                  
                  <Button 
                    type="submit" 
                    color="primary" 
                    className="w-full"
                    isLoading={isLoading}
                  >
                    Verify & Login
                  </Button>
                </form>
              )}
            </div>
          )}
          
          <div className="mt-6 text-center">
            <p className="text-sm text-default-500">
              Don't have an account?{' '}
              <Link to="/register" className="text-primary font-medium">
                Register Free
              </Link>
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  );
};