import React from 'react';
import { Switch, Route } from 'react-router-dom';
import { Navbar } from './components/navbar';
import { Footer } from './components/footer';
import { HomePage } from './pages/home';
import { LoginPage } from './pages/login';
import { RegisterPage } from './pages/register';
import { ProfileCreationPage } from './pages/profile-creation';
import { Dashboard } from './pages/dashboard';
import { SearchPage } from './pages/search';
import { ProfilePage } from './pages/profile';
import { MatchesPage } from './pages/matches';
import { MessagesPage } from './pages/messages';
import { SubscriptionPage } from './pages/subscription';
import { SettingsPage } from './pages/settings';
import { HoroscopePage } from './pages/horoscope';
import { SuccessStoriesPage } from './pages/success-stories';
import { HelpPage } from './pages/help';
import { AuthProvider } from './contexts/auth-context';
import { PrivateRoute } from './components/private-route';

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen flex flex-col">
        <Navbar />
        <main className="flex-grow">
          <Switch>
            <Route exact path="/" component={HomePage} />
            <Route path="/login" component={LoginPage} />
            <Route path="/register" component={RegisterPage} />
            <Route path="/profile-creation" component={ProfileCreationPage} />
            <PrivateRoute path="/dashboard" component={Dashboard} />
            <PrivateRoute path="/search" component={SearchPage} />
            <PrivateRoute path="/profile/:id" component={ProfilePage} />
            <PrivateRoute path="/matches" component={MatchesPage} />
            <PrivateRoute path="/messages" component={MessagesPage} />
            <PrivateRoute path="/subscription" component={SubscriptionPage} />
            <PrivateRoute path="/settings" component={SettingsPage} />
            <PrivateRoute path="/horoscope" component={HoroscopePage} />
            <Route path="/success-stories" component={SuccessStoriesPage} />
            <Route path="/help" component={HelpPage} />
          </Switch>
        </main>
        <Footer />
      </div>
    </AuthProvider>
  );
}

export default App;