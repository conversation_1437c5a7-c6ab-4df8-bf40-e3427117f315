import React from "react";
import { Switch, Route } from "react-router-dom";
import { Navbar } from "./components/navbar";
import { Footer } from "./components/footer";
import { HomePage } from "./pages/home";
import { LoginPage } from "./pages/login";
import { RegisterPage } from "./pages/register";
import { ProfileCreationPage } from "./pages/profile-creation";
import { Dashboard } from "./pages/dashboard";
import { SearchPage } from "./pages/search";
import { ProfilePage } from "./pages/profile";
import { MatchesPage } from "./pages/matches";
import { MessagesPage } from "./pages/messages";
import { SubscriptionPage } from "./pages/subscription";
import { SettingsPage } from "./pages/settings";
import { HoroscopePage } from "./pages/horoscope";
import { SuccessStoriesPage } from "./pages/success-stories";
import { HelpPage } from "./pages/help";
import { InterestsPage } from "./pages/interests";
import { ProfileEditPage } from "./pages/profile-edit";
import { ComprehensiveDashboardPage } from "./pages/comprehensive-dashboard";
import { AdvancedSearchPage } from "./pages/advanced-search";
import { InterestsManagementPage } from "./pages/interests-management";
import { MessagingPage } from "./pages/messaging";
import { ComprehensiveRegisterPage } from "./pages/comprehensive-register";
import { ProfileSetupPage } from "./pages/profile-setup";
import { PhotoManagementPage } from "./pages/photo-management";
import { SubscriptionPlansPage } from "./pages/subscription-plans";
import { VerificationPage } from "./pages/verification";
import { VideoCallPage } from "./pages/video-call";
import { PrivacySettingsPage } from "./pages/privacy-settings";
import { AuthProvider } from "./contexts/auth-context";
import { PrivateRoute } from "./components/private-route";
import ErrorBoundary from "./components/common/error-boundary";

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <div className="min-h-screen flex flex-col">
          <Navbar />
          <main className="flex-grow">
            <Switch>
              <Route exact path="/" component={HomePage} />
              <Route path="/login" component={LoginPage} />
              <Route path="/register" component={RegisterPage} />
              <Route
                path="/register-comprehensive"
                component={ComprehensiveRegisterPage}
              />
              <Route path="/profile-creation" component={ProfileCreationPage} />
              <PrivateRoute
                path="/dashboard-new"
                component={ComprehensiveDashboardPage}
              />
              <PrivateRoute path="/dashboard" component={Dashboard} />
              <PrivateRoute
                path="/search-advanced"
                component={AdvancedSearchPage}
              />
              <PrivateRoute path="/search" component={SearchPage} />
              <PrivateRoute path="/profile/:id" component={ProfilePage} />
              <PrivateRoute path="/profile-edit" component={ProfileEditPage} />
              <PrivateRoute
                path="/profile-setup"
                component={ProfileSetupPage}
              />
              <PrivateRoute path="/photos" component={PhotoManagementPage} />
              <PrivateRoute
                path="/interests-new"
                component={InterestsManagementPage}
              />
              <PrivateRoute path="/interests" component={InterestsPage} />
              <PrivateRoute path="/matches" component={MatchesPage} />
              <PrivateRoute
                path="/messages/:userId"
                component={MessagingPage}
              />
              <PrivateRoute path="/messages" component={MessagesPage} />
              <PrivateRoute
                path="/subscription-plans"
                component={SubscriptionPlansPage}
              />
              <PrivateRoute path="/subscription" component={SubscriptionPage} />
              <PrivateRoute path="/settings" component={SettingsPage} />
              <PrivateRoute path="/horoscope" component={HoroscopePage} />
              <PrivateRoute path="/verification" component={VerificationPage} />
              <PrivateRoute
                path="/video-call/:callId"
                component={VideoCallPage}
              />
              <PrivateRoute
                path="/privacy-settings"
                component={PrivacySettingsPage}
              />
              <Route path="/success-stories" component={SuccessStoriesPage} />
              <Route path="/help" component={HelpPage} />
            </Switch>
          </main>
          <Footer />
        </div>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
