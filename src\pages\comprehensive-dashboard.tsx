import React from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Progress,
  Avatar,
  Badge,
  Chip,
  Di<PERSON>r,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ner,
  <PERSON><PERSON>,
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useAuth } from "../contexts/auth-context";
import { userAPI, searchAPI, interestAPI, analyticsAPI } from "../services/api";
import { ProfileMatch, Interest } from "../types/matrimony";

interface DashboardStats {
  profileViews: number;
  interestsSent: number;
  interestsReceived: number;
  mutualInterests: number;
  messages: number;
  profileCompletionPercentage: number;
}

export const ComprehensiveDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();

  const [stats, setStats] = React.useState<DashboardStats>({
    profileViews: 0,
    interestsSent: 0,
    interestsReceived: 0,
    mutualInterests: 0,
    messages: 0,
    profileCompletionPercentage: 0,
  });

  const [recommendations, setRecommendations] = React.useState<ProfileMatch[]>(
    []
  );
  const [recentInterests, setRecentInterests] = React.useState<Interest[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Load dashboard data
  React.useEffect(() => {
    const loadDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);

        // Load dashboard stats with error handling
        const [statsResponse, recommendationsResponse, interestsResponse] =
          await Promise.allSettled([
            analyticsAPI
              .getDashboard()
              .catch(() => ({ success: false, data: stats })),
            searchAPI
              .getRecommendations(6)
              .catch(() => ({ success: false, data: { profiles: [] } })),
            interestAPI
              .getInterests("received")
              .catch(() => ({ success: false, data: { received: [] } })),
          ]);

        // Handle Promise.allSettled results
        if (
          statsResponse.status === "fulfilled" &&
          statsResponse.value.success
        ) {
          setStats(statsResponse.value.data);
        }

        if (
          recommendationsResponse.status === "fulfilled" &&
          recommendationsResponse.value.success
        ) {
          setRecommendations(recommendationsResponse.value.data.profiles || []);
        }

        if (
          interestsResponse.status === "fulfilled" &&
          interestsResponse.value.success
        ) {
          setRecentInterests(
            interestsResponse.value.data.received?.slice(0, 5) || []
          );
        }
      } catch (err: any) {
        console.error("Dashboard load error:", err);
        setError("Failed to load dashboard data");
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [user]);

  const getProfileCompletionTasks = () => {
    const tasks = [];

    if (!user?.personalDetails) {
      tasks.push({
        title: "Complete Personal Details",
        link: "/profile-setup?step=personal",
        icon: "lucide:user",
      });
    }

    if (!user?.photos || user.photos.length === 0) {
      tasks.push({
        title: "Upload Photos",
        link: "/photos",
        icon: "lucide:camera",
      });
    }

    if (!user?.familyDetails) {
      tasks.push({
        title: "Add Family Information",
        link: "/profile-setup?step=family",
        icon: "lucide:users",
      });
    }

    if (!user?.partnerPreferences) {
      tasks.push({
        title: "Set Partner Preferences",
        link: "/profile-setup?step=preferences",
        icon: "lucide:heart",
      });
    }

    if (!user?.emailVerified) {
      tasks.push({
        title: "Verify Email",
        link: "/verification",
        icon: "lucide:mail",
      });
    }

    if (!user?.phoneVerified) {
      tasks.push({
        title: "Verify Phone",
        link: "/verification",
        icon: "lucide:phone",
      });
    }

    return tasks;
  };

  const formatLastActive = (lastActive: string) => {
    const date = new Date(lastActive);
    const now = new Date();
    const diffInHours = Math.floor(
      (now.getTime() - date.getTime()) / (1000 * 60 * 60)
    );

    if (diffInHours < 1) return "Online now";
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString();
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon
            icon="lucide:user-x"
            size={48}
            className="mx-auto mb-4 text-default-400"
          />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">
            You need to log in to access your dashboard.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Welcome Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Welcome back, {user.personalDetails?.firstName || "User"}!
            </h1>
            <p className="text-default-600">
              Find your perfect life partner with our advanced matching system
            </p>
          </div>

          <div className="flex items-center gap-4">
            {user.membershipType !== "free" && (
              <Chip
                color={
                  user.membershipType === "platinum" ? "warning" : "primary"
                }
                variant="flat"
                startContent={<Icon icon="lucide:crown" />}
              >
                {user.membershipType?.toUpperCase()} Member
              </Chip>
            )}

            <Avatar
              src={user.photos?.[0]?.filePath}
              size="lg"
              fallback={<Icon icon="lucide:user" size={32} />}
            />
          </div>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert
          color="danger"
          variant="flat"
          className="mb-6"
          startContent={<Icon icon="lucide:alert-circle" />}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Profile Completion */}
      {user.profileCompletionPercentage < 100 && (
        <Card className="mb-6 border-l-4 border-l-warning">
          <CardBody>
            <div className="flex items-start gap-4">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-warning-100 rounded-full flex items-center justify-center">
                  <Icon
                    icon="lucide:user-check"
                    className="text-warning-600"
                    size={24}
                  />
                </div>
              </div>

              <div className="flex-1">
                <h3 className="font-semibold mb-2">Complete Your Profile</h3>
                <p className="text-sm text-default-600 mb-3">
                  A complete profile gets 5x more matches. Complete the missing
                  sections to improve your visibility.
                </p>

                <Progress
                  value={user.profileCompletionPercentage}
                  color="warning"
                  className="mb-3"
                  label={`${user.profileCompletionPercentage}% Complete`}
                />

                <div className="flex flex-wrap gap-2">
                  {getProfileCompletionTasks()
                    .slice(0, 3)
                    .map((task, index) => (
                      <Link key={index} to={task.link}>
                        <Button
                          size="sm"
                          variant="flat"
                          startContent={<Icon icon={task.icon} />}
                        >
                          {task.title}
                        </Button>
                      </Link>
                    ))}
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon icon="lucide:eye" className="text-blue-600" size={24} />
            </div>
            <p className="text-2xl font-bold">{stats.profileViews}</p>
            <p className="text-sm text-default-500">Profile Views</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon icon="lucide:heart" className="text-green-600" size={24} />
            </div>
            <p className="text-2xl font-bold">{stats.interestsReceived}</p>
            <p className="text-sm text-default-500">Interests Received</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon icon="lucide:send" className="text-purple-600" size={24} />
            </div>
            <p className="text-2xl font-bold">{stats.interestsSent}</p>
            <p className="text-sm text-default-500">Interests Sent</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon icon="lucide:users" className="text-pink-600" size={24} />
            </div>
            <p className="text-2xl font-bold">{stats.mutualInterests}</p>
            <p className="text-sm text-default-500">Mutual Interests</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon
                icon="lucide:message-circle"
                className="text-orange-600"
                size={24}
              />
            </div>
            <p className="text-2xl font-bold">{stats.messages}</p>
            <p className="text-sm text-default-500">Messages</p>
          </CardBody>
        </Card>

        <Card className="hover:shadow-lg transition-shadow">
          <CardBody className="text-center">
            <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <Icon icon="lucide:star" className="text-indigo-600" size={24} />
            </div>
            <p className="text-2xl font-bold">
              {user.profileCompletionPercentage}%
            </p>
            <p className="text-sm text-default-500">Profile Complete</p>
          </CardBody>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Recommendations */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="flex justify-between items-center">
              <h2 className="text-xl font-semibold">Recommended Matches</h2>
              <Link to="/search">
                <Button variant="flat" size="sm">
                  View All
                </Button>
              </Link>
            </CardHeader>
            <CardBody>
              {loading && (
                <div className="flex justify-center py-8">
                  <Spinner color="primary" />
                </div>
              )}

              {!loading && recommendations.length === 0 && (
                <div className="text-center py-8">
                  <Icon
                    icon="lucide:search"
                    size={48}
                    className="mx-auto mb-4 text-default-400"
                  />
                  <h3 className="font-semibold mb-2">No Recommendations Yet</h3>
                  <p className="text-sm text-default-500 mb-4">
                    Complete your profile to get better recommendations
                  </p>
                  <Link to="/search">
                    <Button color="primary">Browse Profiles</Button>
                  </Link>
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {recommendations.map((profile) => (
                  <Card
                    key={profile.id}
                    className="hover:shadow-lg transition-shadow"
                  >
                    <CardBody>
                      <div className="flex gap-3">
                        <Avatar
                          src={profile.profilePicture}
                          size="lg"
                          fallback={<Icon icon="lucide:user" size={24} />}
                        />

                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-1">
                            <h3 className="font-semibold truncate">
                              {profile.name}
                            </h3>
                            {profile.compatibilityScore && (
                              <Chip size="sm" color="success" variant="flat">
                                {profile.compatibilityScore}%
                              </Chip>
                            )}
                          </div>

                          <p className="text-sm text-default-600 mb-1">
                            {profile.age} years • {profile.location}
                          </p>
                          <p className="text-sm text-default-600 mb-2">
                            {profile.occupation}
                          </p>

                          <div className="flex gap-2">
                            <Link to={`/profile/${profile.id}`}>
                              <Button size="sm" variant="flat">
                                View
                              </Button>
                            </Link>
                            <Button size="sm" color="primary">
                              <Icon icon="lucide:heart" size={14} />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))}
              </div>
            </CardBody>
          </Card>
        </div>

        {/* Right Column - Recent Activity */}
        <div className="space-y-6">
          {/* Recent Interests */}
          <Card>
            <CardHeader className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Recent Interests</h3>
              <Link to="/interests">
                <Button variant="flat" size="sm">
                  View All
                </Button>
              </Link>
            </CardHeader>
            <CardBody>
              {recentInterests.length === 0 ? (
                <div className="text-center py-6">
                  <Icon
                    icon="lucide:heart"
                    size={32}
                    className="mx-auto mb-2 text-default-400"
                  />
                  <p className="text-sm text-default-500">
                    No recent interests
                  </p>
                </div>
              ) : (
                <div className="space-y-3">
                  {recentInterests.map((interest) => (
                    <div key={interest.id} className="flex items-center gap-3">
                      <Avatar
                        src={interest.user?.profilePicture}
                        size="sm"
                        fallback={<Icon icon="lucide:user" size={16} />}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {interest.user?.name}
                        </p>
                        <p className="text-xs text-default-500">
                          {formatLastActive(interest.sentAt)}
                        </p>
                      </div>
                      <Chip
                        size="sm"
                        color={
                          interest.status === "pending" ? "warning" : "success"
                        }
                        variant="flat"
                      >
                        {interest.status}
                      </Chip>
                    </div>
                  ))}
                </div>
              )}
            </CardBody>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <h3 className="text-lg font-semibold">Quick Actions</h3>
            </CardHeader>
            <CardBody className="space-y-3">
              <Link to="/search">
                <Button className="w-full justify-start" variant="flat">
                  <Icon icon="lucide:search" />
                  Browse Profiles
                </Button>
              </Link>

              <Link to="/interests">
                <Button className="w-full justify-start" variant="flat">
                  <Icon icon="lucide:heart" />
                  Manage Interests
                </Button>
              </Link>

              <Link to="/messages">
                <Button className="w-full justify-start" variant="flat">
                  <Icon icon="lucide:message-circle" />
                  Messages
                </Button>
              </Link>

              <Link to="/photos">
                <Button className="w-full justify-start" variant="flat">
                  <Icon icon="lucide:camera" />
                  Manage Photos
                </Button>
              </Link>

              {user.membershipType === "free" && (
                <Link to="/subscription">
                  <Button className="w-full justify-start" color="primary">
                    <Icon icon="lucide:crown" />
                    Upgrade to Premium
                  </Button>
                </Link>
              )}
            </CardBody>
          </Card>
        </div>
      </div>
    </div>
  );
};
