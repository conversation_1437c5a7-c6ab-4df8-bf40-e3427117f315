const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const { authenticateToken, requireCompleteProfile, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Update personal information
router.put('/personal-info', [
  authenticateToken,
  body('dateOfBirth').optional().isISO8601().withMessage('Invalid date format'),
  body('height').optional().isLength({ min: 1 }).withMessage('Height is required'),
  body('weight').optional().isNumeric().withMessage('Weight must be a number'),
  body('maritalStatus').optional().isIn(['never_married', 'divorced', 'widowed', 'separated']).withMessage('Invalid marital status'),
  body('motherTongue').optional().isLength({ min: 1 }).withMessage('Mother tongue is required'),
  body('country').optional().isLength({ min: 1 }).withMessage('Country is required'),
  body('state').optional().isLength({ min: 1 }).withMessage('State is required'),
  body('city').optional().isLength({ min: 1 }).withMessage('City is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const personalInfo = req.body;

    // Calculate age from date of birth
    if (personalInfo.dateOfBirth) {
      const birthDate = new Date(personalInfo.dateOfBirth);
      const today = new Date();
      personalInfo.age = today.getFullYear() - birthDate.getFullYear();
      
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        personalInfo.age--;
      }
    }

    // Update personal information
    user.personalInfo = { ...user.personalInfo, ...personalInfo };
    
    // Recalculate profile completion
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Personal information updated successfully',
      data: {
        personalInfo: user.personalInfo,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update personal info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update personal information'
    });
  }
});

// Update family information
router.put('/family-info', [
  authenticateToken,
  body('familyType').optional().isIn(['nuclear', 'joint', 'others']).withMessage('Invalid family type'),
  body('familyStatus').optional().isIn(['middle_class', 'upper_middle_class', 'rich', 'affluent']).withMessage('Invalid family status'),
  body('familyValues').optional().isIn(['orthodox', 'traditional', 'moderate', 'liberal']).withMessage('Invalid family values'),
  body('brothers').optional().isInt({ min: 0 }).withMessage('Brothers must be a non-negative number'),
  body('sisters').optional().isInt({ min: 0 }).withMessage('Sisters must be a non-negative number')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const familyInfo = req.body;

    user.familyInfo = { ...user.familyInfo, ...familyInfo };
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Family information updated successfully',
      data: {
        familyInfo: user.familyInfo,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update family info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update family information'
    });
  }
});

// Update education and career information
router.put('/education-career', [
  authenticateToken,
  body('highestEducation').optional().isLength({ min: 1 }).withMessage('Education is required'),
  body('occupation').optional().isLength({ min: 1 }).withMessage('Occupation is required'),
  body('annualIncome').optional().isLength({ min: 1 }).withMessage('Annual income is required'),
  body('yearOfPassing').optional().isInt({ min: 1950, max: new Date().getFullYear() }).withMessage('Invalid year of passing')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const educationCareer = req.body;

    user.educationCareer = { ...user.educationCareer, ...educationCareer };
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Education and career information updated successfully',
      data: {
        educationCareer: user.educationCareer,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update education career error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update education and career information'
    });
  }
});

// Update lifestyle information
router.put('/lifestyle', [
  authenticateToken,
  body('diet').optional().isIn(['vegetarian', 'non_vegetarian', 'vegan', 'jain_food']).withMessage('Invalid diet preference'),
  body('smoking').optional().isIn(['never', 'occasionally', 'regularly', 'trying_to_quit']).withMessage('Invalid smoking preference'),
  body('drinking').optional().isIn(['never', 'occasionally', 'socially', 'regularly']).withMessage('Invalid drinking preference')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const lifestyle = req.body;

    user.lifestyle = { ...user.lifestyle, ...lifestyle };
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Lifestyle information updated successfully',
      data: {
        lifestyle: user.lifestyle,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update lifestyle error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update lifestyle information'
    });
  }
});

// Update religious information
router.put('/religious-info', [
  authenticateToken,
  body('religion').optional().isIn(['hindu', 'muslim', 'christian', 'sikh', 'buddhist', 'jain', 'parsi', 'jewish', 'other']).withMessage('Invalid religion'),
  body('manglik').optional().isIn(['yes', 'no', 'anshik']).withMessage('Invalid manglik status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const religiousInfo = req.body;

    user.religiousInfo = { ...user.religiousInfo, ...religiousInfo };
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Religious information updated successfully',
      data: {
        religiousInfo: user.religiousInfo,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update religious info error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update religious information'
    });
  }
});

// Update partner preferences
router.put('/partner-preferences', [
  authenticateToken,
  body('ageRange.min').optional().isInt({ min: 18, max: 80 }).withMessage('Invalid minimum age'),
  body('ageRange.max').optional().isInt({ min: 18, max: 80 }).withMessage('Invalid maximum age')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const user = req.user;
    const partnerPreferences = req.body;

    // Validate age range
    if (partnerPreferences.ageRange && 
        partnerPreferences.ageRange.min && 
        partnerPreferences.ageRange.max &&
        partnerPreferences.ageRange.min > partnerPreferences.ageRange.max) {
      return res.status(400).json({
        success: false,
        message: 'Minimum age cannot be greater than maximum age'
      });
    }

    user.partnerPreferences = { ...user.partnerPreferences, ...partnerPreferences };
    user.calculateProfileCompletion();
    
    await user.save();

    res.json({
      success: true,
      message: 'Partner preferences updated successfully',
      data: {
        partnerPreferences: user.partnerPreferences,
        profileCompletionPercentage: user.profileCompletionPercentage,
        profileCompleted: user.profileCompleted
      }
    });
  } catch (error) {
    console.error('Update partner preferences error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update partner preferences'
    });
  }
});

// Get user profile by ID
router.get('/:userId', optionalAuth, async (req, res) => {
  try {
    const { userId } = req.params;
    const currentUser = req.user;

    const user = await User.findById(userId).select('-password -emailVerificationToken -phoneVerificationOTP -phoneVerificationExpires -passwordResetToken -passwordResetExpires');

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Profile not found'
      });
    }

    if (!user.isActive) {
      return res.status(404).json({
        success: false,
        message: 'Profile not available'
      });
    }

    // Check if current user is blocked by the profile owner
    if (currentUser && user.blockedUsers.includes(currentUser._id)) {
      return res.status(403).json({
        success: false,
        message: 'You cannot view this profile'
      });
    }

    // Increment profile views (only if viewed by different user)
    if (currentUser && currentUser._id.toString() !== userId) {
      user.profileViews += 1;
      await user.save();
    }

    // Prepare response data based on user's membership and verification
    const profileData = {
      id: user._id,
      name: user.name,
      profileType: user.profileType,
      membershipType: user.membershipType,
      profilePicture: user.profilePicture,
      profilePhotos: user.profilePhotos,
      verificationStatus: user.verificationStatus,
      profileCompletionPercentage: user.profileCompletionPercentage,
      personalInfo: user.personalInfo,
      familyInfo: user.familyInfo,
      educationCareer: user.educationCareer,
      lifestyle: user.lifestyle,
      religiousInfo: user.religiousInfo,
      profileViews: user.profileViews,
      lastActive: user.lastActive,
      createdAt: user.createdAt
    };

    // Hide sensitive information for non-premium users
    if (!currentUser || currentUser.membershipType === 'free') {
      delete profileData.personalInfo?.phone;
      delete profileData.familyInfo?.familyIncome;
      delete profileData.educationCareer?.annualIncome;
    }

    res.json({
      success: true,
      data: {
        profile: profileData
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get profile'
    });
  }
});

// Get profile completion status
router.get('/completion/status', authenticateToken, (req, res) => {
  try {
    const user = req.user;
    
    const completionStatus = {
      overall: user.profileCompletionPercentage,
      sections: {
        personalInfo: user.personalInfo && Object.keys(user.personalInfo).length > 5,
        familyInfo: user.familyInfo && Object.keys(user.familyInfo).length > 3,
        educationCareer: user.educationCareer && Object.keys(user.educationCareer).length > 3,
        lifestyle: user.lifestyle && Object.keys(user.lifestyle).length > 2,
        religiousInfo: user.religiousInfo && Object.keys(user.religiousInfo).length > 2,
        partnerPreferences: user.partnerPreferences && Object.keys(user.partnerPreferences).length > 3,
        profilePicture: !!user.profilePicture,
        verification: user.verificationStatus.email && user.verificationStatus.phone
      }
    };

    res.json({
      success: true,
      data: completionStatus
    });
  } catch (error) {
    console.error('Get completion status error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get completion status'
    });
  }
});

module.exports = router;
