const mongoose = require('mongoose');

const videoCallSchema = new mongoose.Schema({
  caller: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  callee: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  roomId: {
    type: String,
    required: true,
    unique: true
  },
  status: {
    type: String,
    enum: ['initiated', 'ringing', 'accepted', 'declined', 'ended', 'missed', 'failed'],
    default: 'initiated'
  },
  callType: {
    type: String,
    enum: ['video', 'audio'],
    default: 'video'
  },
  duration: {
    type: Number, // in seconds
    default: 0
  },
  startTime: Date,
  endTime: Date,
  quality: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    feedback: String,
    technicalIssues: [String]
  },
  recording: {
    enabled: {
      type: Boolean,
      default: false
    },
    url: String,
    duration: Number,
    consentGiven: {
      caller: { type: Boolean, default: false },
      callee: { type: Boolean, default: false }
    }
  },
  metadata: {
    callerDevice: String,
    calleeDevice: String,
    networkQuality: {
      caller: String,
      callee: String
    },
    serverRegion: String,
    sdkVersion: String
  },
  scheduledFor: Date, // For scheduled calls
  isScheduled: {
    type: Boolean,
    default: false
  },
  reminderSent: {
    type: Boolean,
    default: false
  },
  privacy: {
    isPrivate: {
      type: Boolean,
      default: true
    },
    allowRecording: {
      type: Boolean,
      default: false
    },
    allowScreenShare: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true
});

// Indexes
videoCallSchema.index({ caller: 1, createdAt: -1 });
videoCallSchema.index({ callee: 1, createdAt: -1 });
videoCallSchema.index({ roomId: 1 });
videoCallSchema.index({ status: 1, createdAt: -1 });
videoCallSchema.index({ scheduledFor: 1 });

// Virtual for call duration in minutes
videoCallSchema.virtual('durationInMinutes').get(function() {
  return Math.round(this.duration / 60);
});

// Virtual for call success
videoCallSchema.virtual('isSuccessful').get(function() {
  return ['accepted', 'ended'].includes(this.status) && this.duration > 0;
});

// Instance method to start call
videoCallSchema.methods.startCall = async function() {
  this.status = 'accepted';
  this.startTime = new Date();
  await this.save();
  
  // Emit real-time event
  const io = global.io;
  if (io) {
    io.to(this.callee.toString()).emit('call-started', {
      callId: this._id,
      roomId: this.roomId,
      caller: this.caller
    });
  }
};

// Instance method to end call
videoCallSchema.methods.endCall = async function(endedBy) {
  this.status = 'ended';
  this.endTime = new Date();
  
  if (this.startTime) {
    this.duration = Math.floor((this.endTime - this.startTime) / 1000);
  }
  
  await this.save();
  
  // Emit real-time event
  const io = global.io;
  if (io) {
    const participants = [this.caller.toString(), this.callee.toString()];
    participants.forEach(participantId => {
      io.to(participantId).emit('call-ended', {
        callId: this._id,
        duration: this.duration,
        endedBy
      });
    });
  }
};

// Instance method to decline call
videoCallSchema.methods.declineCall = async function(reason) {
  this.status = 'declined';
  this.endTime = new Date();
  this.metadata.declineReason = reason;
  await this.save();
  
  // Emit real-time event
  const io = global.io;
  if (io) {
    io.to(this.caller.toString()).emit('call-declined', {
      callId: this._id,
      reason
    });
  }
};

// Instance method to mark as missed
videoCallSchema.methods.markAsMissed = async function() {
  this.status = 'missed';
  this.endTime = new Date();
  await this.save();
  
  // Send notification to caller
  const notificationService = require('../services/notificationService');
  await notificationService.createNotification('call_missed', this.caller, {
    calleeId: this.callee,
    callId: this._id
  });
};

// Static method to create call room
videoCallSchema.statics.createCallRoom = async function(callerId, calleeId, options = {}) {
  const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const call = new this({
    caller: callerId,
    callee: calleeId,
    roomId,
    callType: options.callType || 'video',
    isScheduled: options.isScheduled || false,
    scheduledFor: options.scheduledFor,
    privacy: options.privacy || {}
  });
  
  await call.save();
  return call;
};

// Static method to get call history
videoCallSchema.statics.getCallHistory = function(userId, options = {}) {
  const {
    page = 1,
    limit = 20,
    status,
    callType
  } = options;
  
  const query = {
    $or: [
      { caller: userId },
      { callee: userId }
    ]
  };
  
  if (status) query.status = status;
  if (callType) query.callType = callType;
  
  return this.find(query)
    .populate('caller callee', 'name profilePicture')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit);
};

// Static method to get call statistics
videoCallSchema.statics.getCallStats = async function(userId, timeframe = '30d') {
  const days = parseInt(timeframe.replace('d', ''));
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
  
  const stats = await this.aggregate([
    {
      $match: {
        $or: [{ caller: userId }, { callee: userId }],
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalDuration: { $sum: '$duration' }
      }
    }
  ]);
  
  const totalCalls = stats.reduce((sum, stat) => sum + stat.count, 0);
  const totalDuration = stats.reduce((sum, stat) => sum + stat.totalDuration, 0);
  const successfulCalls = stats.find(s => s._id === 'ended')?.count || 0;
  
  return {
    totalCalls,
    successfulCalls,
    successRate: totalCalls > 0 ? (successfulCalls / totalCalls) * 100 : 0,
    totalDuration,
    averageDuration: successfulCalls > 0 ? totalDuration / successfulCalls : 0,
    byStatus: stats
  };
};

// Pre-save middleware
videoCallSchema.pre('save', function(next) {
  // Auto-generate room ID if not provided
  if (this.isNew && !this.roomId) {
    this.roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  next();
});

module.exports = mongoose.model('VideoCall', videoCallSchema);
