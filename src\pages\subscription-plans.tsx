import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Badge,
  <PERSON>vider,
  <PERSON>dal,
  <PERSON>dalContent,
  <PERSON>dalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Tabs,
  Tab,
  <PERSON><PERSON>,
  Spinner
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { subscriptionAPI } from '../services/api';
import { SubscriptionPlan, UserSubscription } from '../types/matrimony';

const plans: SubscriptionPlan[] = [
  {
    id: 'silver',
    name: 'Silver',
    type: 'silver',
    monthlyPrice: 999,
    quarterlyPrice: 2499,
    yearlyPrice: 7999,
    features: {
      profileViews: 50,
      interests: 20,
      messages: 10,
      advancedSearch: true,
      contactDetails: false,
      horoscopeMatching: false,
      prioritySupport: false,
      profileBoost: false,
      dedicatedManager: false,
      videoCall: false
    },
    isActive: true,
    createdAt: '',
    updatedAt: ''
  },
  {
    id: 'gold',
    name: 'Gold',
    type: 'gold',
    monthlyPrice: 1999,
    quarterlyPrice: 4999,
    yearlyPrice: 15999,
    features: {
      profileViews: 200,
      interests: 100,
      messages: 50,
      advancedSearch: true,
      contactDetails: true,
      horoscopeMatching: true,
      prioritySupport: true,
      profileBoost: true,
      dedicatedManager: false,
      videoCall: false
    },
    isActive: true,
    createdAt: '',
    updatedAt: ''
  },
  {
    id: 'platinum',
    name: 'Platinum',
    type: 'platinum',
    monthlyPrice: 3999,
    quarterlyPrice: 9999,
    yearlyPrice: 31999,
    features: {
      profileViews: -1, // Unlimited
      interests: -1, // Unlimited
      messages: -1, // Unlimited
      advancedSearch: true,
      contactDetails: true,
      horoscopeMatching: true,
      prioritySupport: true,
      profileBoost: true,
      dedicatedManager: true,
      videoCall: true
    },
    isActive: true,
    createdAt: '',
    updatedAt: ''
  }
];

export const SubscriptionPlansPage: React.FC = () => {
  const navigate = useNavigate();
  const { user, updateUser } = useAuth();
  const { isOpen, onOpen, onClose } = useDisclosure();
  
  const [selectedPlan, setSelectedPlan] = React.useState<SubscriptionPlan | null>(null);
  const [selectedDuration, setSelectedDuration] = React.useState<'monthly' | 'quarterly' | 'yearly'>('yearly');
  const [currentSubscription, setCurrentSubscription] = React.useState<UserSubscription | null>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [processingPayment, setProcessingPayment] = React.useState(false);

  // Load current subscription
  React.useEffect(() => {
    const loadCurrentSubscription = async () => {
      if (!user) return;
      
      try {
        const response = await subscriptionAPI.getCurrentSubscription();
        
        if (response.success && response.data.subscription) {
          setCurrentSubscription(response.data.subscription);
        }
      } catch (err: any) {
        console.error('Load subscription error:', err);
      }
    };
    
    loadCurrentSubscription();
  }, [user]);

  const getPlanPrice = (plan: SubscriptionPlan, duration: string) => {
    switch (duration) {
      case 'monthly': return plan.monthlyPrice;
      case 'quarterly': return plan.quarterlyPrice;
      case 'yearly': return plan.yearlyPrice;
      default: return plan.yearlyPrice;
    }
  };

  const getDurationLabel = (duration: string) => {
    switch (duration) {
      case 'monthly': return 'Month';
      case 'quarterly': return '3 Months';
      case 'yearly': return 'Year';
      default: return 'Year';
    }
  };

  const getSavingsPercentage = (plan: SubscriptionPlan, duration: string) => {
    const monthlyTotal = plan.monthlyPrice * (duration === 'quarterly' ? 3 : 12);
    const actualPrice = getPlanPrice(plan, duration);
    return Math.round(((monthlyTotal - actualPrice) / monthlyTotal) * 100);
  };

  const handleSelectPlan = (plan: SubscriptionPlan) => {
    setSelectedPlan(plan);
    onOpen();
  };

  const handleSubscribe = async () => {
    if (!selectedPlan || !user) return;

    try {
      setProcessingPayment(true);
      setError(null);

      const subscriptionData = {
        planId: selectedPlan.id,
        duration: selectedDuration,
        amount: getPlanPrice(selectedPlan, selectedDuration)
      };

      const response = await subscriptionAPI.createSubscription(subscriptionData);

      if (response.success) {
        // Handle payment gateway integration here
        // For now, we'll simulate a successful payment
        
        // Update user subscription status
        updateUser({
          ...user,
          isPremium: true,
          membershipType: selectedPlan.type,
          subscriptionExpiresAt: response.data.subscription.endDate
        });

        setCurrentSubscription(response.data.subscription);
        onClose();
        
        // Show success message
        alert('Subscription activated successfully!');
      } else {
        setError(response.message || 'Failed to create subscription');
      }
    } catch (err: any) {
      console.error('Subscribe error:', err);
      setError(err.response?.data?.message || 'Failed to process subscription');
    } finally {
      setProcessingPayment(false);
    }
  };

  const isCurrentPlan = (planType: string) => {
    return currentSubscription?.status === 'active' && 
           user?.membershipType === planType;
  };

  const formatFeatureValue = (value: number | boolean | undefined) => {
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (value === -1) return 'Unlimited';
    if (typeof value === 'number') return value.toString();
    return 'No';
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to view subscription plans.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Choose Your Perfect Plan</h1>
        <p className="text-xl text-default-600 mb-6">
          Unlock premium features and find your life partner faster
        </p>
        
        {currentSubscription && currentSubscription.status === 'active' && (
          <Alert color="success" variant="flat" className="max-w-md mx-auto">
            <div className="flex items-center gap-2">
              <Icon icon="lucide:crown" />
              <span>
                You have an active {user.membershipType?.toUpperCase()} subscription
              </span>
            </div>
          </Alert>
        )}
      </div>

      {/* Duration Selector */}
      <div className="flex justify-center mb-8">
        <Tabs
          selectedKey={selectedDuration}
          onSelectionChange={(key) => setSelectedDuration(key as any)}
          color="primary"
          variant="bordered"
        >
          <Tab key="monthly" title="Monthly" />
          <Tab key="quarterly" title="Quarterly" />
          <Tab key="yearly" title="Yearly" />
        </Tabs>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        {plans.map((plan) => {
          const price = getPlanPrice(plan, selectedDuration);
          const savings = selectedDuration !== 'monthly' ? getSavingsPercentage(plan, selectedDuration) : 0;
          const isPopular = plan.type === 'gold';
          const isCurrent = isCurrentPlan(plan.type);

          return (
            <Card
              key={plan.id}
              className={`relative ${isPopular ? 'border-2 border-primary scale-105' : ''} ${
                isCurrent ? 'border-2 border-success' : ''
              }`}
            >
              {isPopular && (
                <Badge
                  content="Most Popular"
                  color="primary"
                  placement="top-right"
                  className="absolute -top-2 -right-2"
                />
              )}
              
              {isCurrent && (
                <Badge
                  content="Current Plan"
                  color="success"
                  placement="top-left"
                  className="absolute -top-2 -left-2"
                />
              )}

              <CardHeader className="text-center pb-2">
                <div className="w-full">
                  <div className="flex justify-center mb-4">
                    <div className={`
                      w-16 h-16 rounded-full flex items-center justify-center
                      ${plan.type === 'silver' ? 'bg-gray-100' : ''}
                      ${plan.type === 'gold' ? 'bg-yellow-100' : ''}
                      ${plan.type === 'platinum' ? 'bg-purple-100' : ''}
                    `}>
                      <Icon
                        icon={
                          plan.type === 'silver' ? 'lucide:star' :
                          plan.type === 'gold' ? 'lucide:crown' :
                          'lucide:gem'
                        }
                        size={32}
                        className={`
                          ${plan.type === 'silver' ? 'text-gray-600' : ''}
                          ${plan.type === 'gold' ? 'text-yellow-600' : ''}
                          ${plan.type === 'platinum' ? 'text-purple-600' : ''}
                        `}
                      />
                    </div>
                  </div>
                  
                  <h3 className="text-2xl font-bold mb-2">{plan.name}</h3>
                  
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-3xl font-bold">₹{price.toLocaleString()}</span>
                    <span className="text-default-500">/{getDurationLabel(selectedDuration)}</span>
                  </div>
                  
                  {savings > 0 && (
                    <Chip size="sm" color="success" variant="flat" className="mt-2">
                      Save {savings}%
                    </Chip>
                  )}
                </div>
              </CardHeader>

              <CardBody className="pt-0">
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Profile Views</span>
                    <span className="font-medium">
                      {formatFeatureValue(plan.features.profileViews)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Send Interests</span>
                    <span className="font-medium">
                      {formatFeatureValue(plan.features.interests)}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Messages</span>
                    <span className="font-medium">
                      {formatFeatureValue(plan.features.messages)}
                    </span>
                  </div>
                  
                  <Divider />
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Advanced Search</span>
                    <Icon
                      icon={plan.features.advancedSearch ? 'lucide:check' : 'lucide:x'}
                      className={plan.features.advancedSearch ? 'text-success' : 'text-danger'}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Contact Details</span>
                    <Icon
                      icon={plan.features.contactDetails ? 'lucide:check' : 'lucide:x'}
                      className={plan.features.contactDetails ? 'text-success' : 'text-danger'}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Horoscope Matching</span>
                    <Icon
                      icon={plan.features.horoscopeMatching ? 'lucide:check' : 'lucide:x'}
                      className={plan.features.horoscopeMatching ? 'text-success' : 'text-danger'}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Priority Support</span>
                    <Icon
                      icon={plan.features.prioritySupport ? 'lucide:check' : 'lucide:x'}
                      className={plan.features.prioritySupport ? 'text-success' : 'text-danger'}
                    />
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Profile Boost</span>
                    <Icon
                      icon={plan.features.profileBoost ? 'lucide:check' : 'lucide:x'}
                      className={plan.features.profileBoost ? 'text-success' : 'text-danger'}
                    />
                  </div>
                  
                  {plan.type === 'platinum' && (
                    <>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Dedicated Manager</span>
                        <Icon icon="lucide:check" className="text-success" />
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Video Calling</span>
                        <Icon icon="lucide:check" className="text-success" />
                      </div>
                    </>
                  )}
                </div>

                <Button
                  color={isPopular ? 'primary' : 'default'}
                  variant={isCurrent ? 'flat' : 'solid'}
                  className="w-full"
                  size="lg"
                  onPress={() => handleSelectPlan(plan)}
                  isDisabled={isCurrent}
                >
                  {isCurrent ? 'Current Plan' : `Choose ${plan.name}`}
                </Button>
              </CardBody>
            </Card>
          );
        })}
      </div>

      {/* Features Comparison */}
      <Card className="mb-8">
        <CardHeader>
          <h2 className="text-2xl font-bold">Feature Comparison</h2>
        </CardHeader>
        <CardBody>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3">Features</th>
                  <th className="text-center py-3">Silver</th>
                  <th className="text-center py-3">Gold</th>
                  <th className="text-center py-3">Platinum</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                <tr>
                  <td className="py-3">Profile Views per month</td>
                  <td className="text-center py-3">50</td>
                  <td className="text-center py-3">200</td>
                  <td className="text-center py-3">Unlimited</td>
                </tr>
                <tr>
                  <td className="py-3">Send Interests per month</td>
                  <td className="text-center py-3">20</td>
                  <td className="text-center py-3">100</td>
                  <td className="text-center py-3">Unlimited</td>
                </tr>
                <tr>
                  <td className="py-3">Messages per month</td>
                  <td className="text-center py-3">10</td>
                  <td className="text-center py-3">50</td>
                  <td className="text-center py-3">Unlimited</td>
                </tr>
                <tr>
                  <td className="py-3">Advanced Search Filters</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">View Contact Details</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">Horoscope Matching</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">Priority Customer Support</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">Profile Boost</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">Dedicated Relationship Manager</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                </tr>
                <tr>
                  <td className="py-3">Video Calling</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✗</td>
                  <td className="text-center py-3">✓</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardBody>
      </Card>

      {/* Subscription Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalContent>
          <ModalHeader>
            Subscribe to {selectedPlan?.name} Plan
          </ModalHeader>
          <ModalBody>
            {selectedPlan && (
              <div className="space-y-4">
                <div className="text-center">
                  <h3 className="text-2xl font-bold mb-2">{selectedPlan.name} Plan</h3>
                  <div className="flex items-baseline justify-center gap-1">
                    <span className="text-3xl font-bold">
                      ₹{getPlanPrice(selectedPlan, selectedDuration).toLocaleString()}
                    </span>
                    <span className="text-default-500">/{getDurationLabel(selectedDuration)}</span>
                  </div>
                </div>

                <Divider />

                <div className="space-y-2">
                  <h4 className="font-semibold">What you'll get:</h4>
                  <ul className="space-y-1 text-sm">
                    <li>• {formatFeatureValue(selectedPlan.features.profileViews)} profile views</li>
                    <li>• {formatFeatureValue(selectedPlan.features.interests)} interests per month</li>
                    <li>• {formatFeatureValue(selectedPlan.features.messages)} messages per month</li>
                    {selectedPlan.features.advancedSearch && <li>• Advanced search filters</li>}
                    {selectedPlan.features.contactDetails && <li>• View contact details</li>}
                    {selectedPlan.features.horoscopeMatching && <li>• Horoscope matching</li>}
                    {selectedPlan.features.prioritySupport && <li>• Priority customer support</li>}
                    {selectedPlan.features.profileBoost && <li>• Profile boost feature</li>}
                    {selectedPlan.features.dedicatedManager && <li>• Dedicated relationship manager</li>}
                    {selectedPlan.features.videoCall && <li>• Video calling feature</li>}
                  </ul>
                </div>

                {error && (
                  <Alert color="danger" variant="flat">
                    {error}
                  </Alert>
                )}
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="flat" onPress={onClose}>
              Cancel
            </Button>
            <Button
              color="primary"
              onPress={handleSubscribe}
              isLoading={processingPayment}
            >
              {processingPayment ? 'Processing...' : 'Subscribe Now'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  );
};
