// Simple integration test script
const axios = require('axios');

const API_BASE_URL = 'http://localhost:5000/api';

async function testAPIIntegration() {
  console.log('🧪 Testing API Integration...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing Health Check...');
    const healthResponse = await axios.get(`${API_BASE_URL}/health`);
    console.log('✅ Health Check:', healthResponse.data.message);
    console.log(`   Database: ${healthResponse.data.database || 'In-Memory'}`);
    console.log(`   Total Users: ${healthResponse.data.totalUsers || 0}\n`);

    // Test 2: User Registration
    console.log('2. Testing User Registration...');
    const testUser = {
      name: 'Test User',
      email: `test${Date.now()}@example.com`,
      password: 'password123',
      phone: '+91 **********',
      profileType: 'self'
    };

    const registerResponse = await axios.post(`${API_BASE_URL}/auth/register`, testUser);
    console.log('✅ Registration successful');
    console.log(`   User ID: ${registerResponse.data.data.user.id}`);
    console.log(`   Token received: ${registerResponse.data.data.token ? 'Yes' : 'No'}\n`);

    const token = registerResponse.data.data.token;
    const userId = registerResponse.data.data.user.id;

    // Test 3: Token Verification
    console.log('3. Testing Token Verification...');
    const verifyResponse = await axios.get(`${API_BASE_URL}/auth/verify`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Token verification successful');
    console.log(`   User: ${verifyResponse.data.data.user.name}\n`);

    // Test 4: Profile Update
    console.log('4. Testing Profile Update...');
    const profileUpdate = {
      personalInfo: {
        age: 28,
        height: 170,
        city: 'Mumbai',
        state: 'Maharashtra'
      },
      educationCareer: {
        highestEducation: 'B.Tech',
        occupation: 'Software Engineer'
      }
    };

    const updateResponse = await axios.put(`${API_BASE_URL}/users/profile`, profileUpdate, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Profile update successful');
    console.log(`   Profile completion: ${updateResponse.data.data.user.profileCompletionPercentage}%\n`);

    // Test 5: Search Profiles
    console.log('5. Testing Profile Search...');
    const searchResponse = await axios.get(`${API_BASE_URL}/search?page=1&limit=5`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    console.log('✅ Search successful');
    console.log(`   Profiles found: ${searchResponse.data.data.profiles.length}`);
    console.log(`   Total results: ${searchResponse.data.data.pagination.totalResults}\n`);

    // Test 6: User Login
    console.log('6. Testing User Login...');
    const loginResponse = await axios.post(`${API_BASE_URL}/auth/login`, {
      email: testUser.email,
      password: testUser.password
    });
    console.log('✅ Login successful');
    console.log(`   Same user ID: ${loginResponse.data.data.user.id === userId ? 'Yes' : 'No'}\n`);

    console.log('🎉 All API integration tests passed!\n');
    console.log('Frontend URL: http://localhost:5174');
    console.log('Backend URL: http://localhost:5000');
    console.log('API Health: http://localhost:5000/api/health');

  } catch (error) {
    console.error('❌ API Integration Test Failed:');
    if (error.response) {
      console.error(`   Status: ${error.response.status}`);
      console.error(`   Message: ${error.response.data.message || error.response.statusText}`);
    } else if (error.request) {
      console.error('   No response received. Is the server running?');
      console.error('   Start the server with: cd server && node working-server.js');
    } else {
      console.error(`   Error: ${error.message}`);
    }
  }
}

// Run the test
testAPIIntegration();
