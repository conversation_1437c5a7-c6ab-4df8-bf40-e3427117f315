import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>er, 
  <PERSON><PERSON>, 
  Input, 
  Ava<PERSON>, 
  Di<PERSON>r,
  <PERSON>
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';

// Mock data for conversations
const conversations = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=2',
    lastMessage: 'Hi, I saw your profile and would like to know more about you...',
    lastMessageTime: '2023-06-15T10:30:00Z',
    unreadCount: 2,
    online: true,
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=3',
    lastMessage: 'Thank you for accepting my interest. I would love to connect...',
    lastMessageTime: '2023-06-14T14:20:00Z',
    unreadCount: 0,
    online: false,
  },
  {
    id: '3',
    name: '<PERSON><PERSON>',
    photo: 'https://img.heroui.chat/image/avatar?w=200&h=200&u=4',
    lastMessage: 'Hello, how are you doing today?',
    lastMessageTime: '2023-06-13T09:15:00Z',
    unreadCount: 0,
    online: false,
  },
];

// Mock data for messages in a conversation
const messages = [
  {
    id: '1',
    senderId: '1',
    receiverId: 'me',
    content: 'Hi, I saw your profile and would like to know more about you.',
    timestamp: '2023-06-15T10:30:00Z',
    read: true,
  },
  {
    id: '2',
    senderId: 'me',
    receiverId: '1',
    content: 'Hello! Thank you for reaching out. I would be happy to connect and get to know each other better.',
    timestamp: '2023-06-15T10:35:00Z',
    read: true,
  },
  {
    id: '3',
    senderId: '1',
    receiverId: 'me',
    content: 'Great! I noticed that you are a software engineer. What kind of projects do you work on?',
    timestamp: '2023-06-15T10:40:00Z',
    read: true,
  },
  {
    id: '4',
    senderId: 'me',
    receiverId: '1',
    content: 'I work on web applications using React and Node.js. I also have experience with mobile app development using React Native.',
    timestamp: '2023-06-15T10:45:00Z',
    read: true,
  },
  {
    id: '5',
    senderId: '1',
    receiverId: 'me',
    content: 'That sounds interesting! I work in the IT field as well, but more on the data science side.',
    timestamp: '2023-06-15T10:50:00Z',
    read: false,
  },
  {
    id: '6',
    senderId: '1',
    receiverId: 'me',
    content: 'Would you be interested in talking more over a phone call sometime?',
    timestamp: '2023-06-15T10:55:00Z',
    read: false,
  },
];

export const MessagesPage: React.FC = () => {
  const { user } = useAuth();
  const [selectedConversation, setSelectedConversation] = React.useState<string | null>(null);
  const [messageText, setMessageText] = React.useState('');
  
  const handleSendMessage = () => {
    if (!messageText.trim()) return;
    
    // In a real app, this would send the message to the API
    console.log(`Sending message to ${selectedConversation}: ${messageText}`);
    
    // Clear the input
    setMessageText('');
  };
  
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };
  
  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };
  
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <h1 className="text-2xl font-bold mb-6">Messages</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Left Column - Conversations */}
          <div className="lg:col-span-1">
            <Card className="h-full">
              <CardHeader className="flex justify-between items-center">
                <h2 className="text-lg font-semibold">Conversations</h2>
                <Button 
                  isIconOnly 
                  variant="light" 
                  color="primary"
                  aria-label="New Message"
                >
                  <Icon icon="lucide:edit" />
                </Button>
              </CardHeader>
              <Divider />
              <CardBody className="p-0 overflow-y-auto">
                <div className="flex flex-col divide-y divide-divider">
                  {conversations.map((conversation) => (
                    <div 
                      key={conversation.id} 
                      className={`p-4 cursor-pointer hover:bg-default-100 ${selectedConversation === conversation.id ? 'bg-default-100' : ''}`}
                      onClick={() => setSelectedConversation(conversation.id)}
                    >
                      <div className="flex gap-3">
                        <div className="relative">
                          <Avatar src={conversation.photo} />
                          {conversation.online && (
                            <span className="absolute bottom-0 right-0 w-3 h-3 bg-success rounded-full border-2 border-white"></span>
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-center">
                            <h3 className="font-semibold truncate">{conversation.name}</h3>
                            <span className="text-xs text-default-500">{formatDate(conversation.lastMessageTime)}</span>
                          </div>
                          <p className="text-sm text-default-500 truncate">{conversation.lastMessage}</p>
                        </div>
                        {conversation.unreadCount > 0 && (
                          <Chip size="sm" color="primary" variant="solid" className="self-center">
                            {conversation.unreadCount}
                          </Chip>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardBody>
            </Card>
          </div>
          
          {/* Right Column - Messages */}
          <div className="lg:col-span-2">
            <Card className="h-full flex flex-col">
              {selectedConversation ? (
                <>
                  <CardHeader className="flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <Avatar src={conversations.find(c => c.id === selectedConversation)?.photo} />
                      <div>
                        <h2 className="text-lg font-semibold">{conversations.find(c => c.id === selectedConversation)?.name}</h2>
                        <p className="text-xs text-default-500">
                          {conversations.find(c => c.id === selectedConversation)?.online ? (
                            <span className="text-success">Online</span>
                          ) : (
                            'Last seen recently'
                          )}
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        isIconOnly 
                        variant="light" 
                        color="primary"
                        aria-label="Call"
                      >
                        <Icon icon="lucide:phone" />
                      </Button>
                      <Button 
                        isIconOnly 
                        variant="light" 
                        color="primary"
                        aria-label="More options"
                      >
                        <Icon icon="lucide:more-vertical" />
                      </Button>
                    </div>
                  </CardHeader>
                  <Divider />
                  <CardBody className="flex-grow overflow-y-auto p-4">
                    <div className="flex flex-col gap-4">
                      {messages.map((message) => (
                        <div 
                          key={message.id} 
                          className={`flex ${message.senderId === 'me' ? 'justify-end' : 'justify-start'}`}
                        >
                          <div 
                            className={`max-w-[70%] rounded-lg p-3 ${
                              message.senderId === 'me' 
                                ? 'bg-primary text-white rounded-br-none' 
                                : 'bg-default-100 rounded-bl-none'
                            }`}
                          >
                            <p className="text-sm">{message.content}</p>
                            <div className={`flex justify-end items-center gap-1 mt-1 text-xs ${
                              message.senderId === 'me' ? 'text-white/70' : 'text-default-500'
                            }`}>
                              {formatTime(message.timestamp)}
                              {message.senderId === 'me' && (
                                <Icon 
                                  icon={message.read ? "lucide:check-check" : "lucide:check"} 
                                  className="text-xs" 
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardBody>
                  <Divider />
                  <div className="p-4">
                    <div className="flex gap-2">
                      <Button 
                        isIconOnly 
                        variant="light" 
                        color="default"
                        aria-label="Attach file"
                      >
                        <Icon icon="lucide:paperclip" />
                      </Button>
                      <Input
                        placeholder="Type a message..."
                        value={messageText}
                        onValueChange={setMessageText}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSendMessage();
                          }
                        }}
                        endContent={
                          <Button 
                            isIconOnly 
                            color="primary" 
                            variant="light"
                            onPress={handleSendMessage}
                            isDisabled={!messageText.trim()}
                          >
                            <Icon icon="lucide:send" />
                          </Button>
                        }
                      />
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center h-full p-4">
                  <Icon icon="lucide:message-circle" className="text-6xl text-default-300 mb-4" />
                  <h3 className="text-xl font-semibold mb-2">No conversation selected</h3>
                  <p className="text-default-500 text-center">
                    Select a conversation from the list or start a new one.
                  </p>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};