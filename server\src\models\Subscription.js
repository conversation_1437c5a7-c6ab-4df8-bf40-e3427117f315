const mongoose = require('mongoose');

const subscriptionPlanSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  displayName: String,
  description: String,
  price: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'INR'
  },
  duration: {
    type: Number,
    required: true // in months
  },
  features: [{
    name: String,
    description: String,
    enabled: {
      type: Boolean,
      default: true
    }
  }],
  limits: {
    profileViews: {
      type: Number,
      default: -1 // -1 means unlimited
    },
    contactViews: {
      type: Number,
      default: 0
    },
    messagesPerDay: {
      type: Number,
      default: 10
    },
    interestsPerDay: {
      type: Number,
      default: 5
    },
    searchFilters: {
      type: Number,
      default: 3
    },
    photoUploads: {
      type: Number,
      default: 5
    }
  },
  premiumFeatures: {
    unlimitedViews: {
      type: Boolean,
      default: false
    },
    advancedSearch: {
      type: Boolean,
      default: false
    },
    priorityListing: {
      type: Boolean,
      default: false
    },
    directMessaging: {
      type: Boolean,
      default: false
    },
    contactDetails: {
      type: Boolean,
      default: false
    },
    horoscopeMatching: {
      type: Boolean,
      default: false
    },
    relationshipManager: {
      type: Boolean,
      default: false
    },
    profileHighlight: {
      type: Boolean,
      default: false
    },
    readReceipts: {
      type: Boolean,
      default: false
    },
    videoCall: {
      type: Boolean,
      default: false
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isPopular: {
    type: Boolean,
    default: false
  },
  discountPercentage: {
    type: Number,
    default: 0
  },
  validFrom: {
    type: Date,
    default: Date.now
  },
  validUntil: Date,
  sortOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

const userSubscriptionSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  plan: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SubscriptionPlan',
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'expired', 'cancelled', 'pending', 'failed'],
    default: 'pending'
  },
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date,
    required: true
  },
  autoRenew: {
    type: Boolean,
    default: false
  },
  paymentId: String,
  orderId: String,
  transactionId: String,
  paymentMethod: {
    type: String,
    enum: ['razorpay', 'paytm', 'upi', 'netbanking', 'card', 'wallet']
  },
  amountPaid: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    default: 'INR'
  },
  discountApplied: {
    type: Number,
    default: 0
  },
  couponCode: String,
  paymentDetails: {
    gateway: String,
    gatewayTransactionId: String,
    gatewayOrderId: String,
    paymentStatus: String,
    paymentDate: Date,
    refundId: String,
    refundAmount: Number,
    refundDate: Date
  },
  usage: {
    profileViews: {
      type: Number,
      default: 0
    },
    contactViews: {
      type: Number,
      default: 0
    },
    messagesSent: {
      type: Number,
      default: 0
    },
    interestsSent: {
      type: Number,
      default: 0
    },
    searchesPerformed: {
      type: Number,
      default: 0
    },
    lastResetDate: {
      type: Date,
      default: Date.now
    }
  },
  cancellationReason: String,
  cancelledAt: Date,
  cancelledBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  notes: String
}, {
  timestamps: true
});

// Indexes
subscriptionPlanSchema.index({ isActive: 1, sortOrder: 1 });
userSubscriptionSchema.index({ user: 1, status: 1 });
userSubscriptionSchema.index({ endDate: 1, status: 1 });
userSubscriptionSchema.index({ plan: 1, status: 1 });

// Virtual for checking if subscription is active
userSubscriptionSchema.virtual('isActive').get(function() {
  return this.status === 'active' && this.endDate > new Date();
});

// Virtual for days remaining
userSubscriptionSchema.virtual('daysRemaining').get(function() {
  if (this.status !== 'active') return 0;
  const now = new Date();
  const diffTime = this.endDate - now;
  return Math.max(0, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
});

// Instance method to check if feature is available
userSubscriptionSchema.methods.hasFeature = async function(featureName) {
  if (!this.isActive) return false;
  
  await this.populate('plan');
  return this.plan.premiumFeatures[featureName] || false;
};

// Instance method to check usage limits
userSubscriptionSchema.methods.canUseFeature = async function(featureName) {
  if (!this.isActive) return false;
  
  await this.populate('plan');
  const limit = this.plan.limits[featureName];
  
  if (limit === -1) return true; // Unlimited
  if (limit === 0) return false; // Not allowed
  
  // Reset daily usage if needed
  const today = new Date();
  const lastReset = new Date(this.usage.lastResetDate);
  
  if (today.toDateString() !== lastReset.toDateString()) {
    this.usage.messagesSent = 0;
    this.usage.interestsSent = 0;
    this.usage.searchesPerformed = 0;
    this.usage.lastResetDate = today;
    await this.save();
  }
  
  const currentUsage = this.usage[featureName] || 0;
  return currentUsage < limit;
};

// Instance method to increment usage
userSubscriptionSchema.methods.incrementUsage = async function(featureName) {
  if (!this.usage[featureName]) {
    this.usage[featureName] = 0;
  }
  this.usage[featureName]++;
  await this.save();
};

// Static method to get active subscription for user
userSubscriptionSchema.statics.getActiveSubscription = async function(userId) {
  return this.findOne({
    user: userId,
    status: 'active',
    endDate: { $gt: new Date() }
  }).populate('plan');
};

// Static method to expire subscriptions
userSubscriptionSchema.statics.expireSubscriptions = async function() {
  const expiredSubscriptions = await this.updateMany(
    {
      status: 'active',
      endDate: { $lte: new Date() }
    },
    {
      $set: { status: 'expired' }
    }
  );
  
  return expiredSubscriptions;
};

// Pre-save middleware to set end date
userSubscriptionSchema.pre('save', function(next) {
  if (this.isNew && !this.endDate) {
    const startDate = this.startDate || new Date();
    this.endDate = new Date(startDate.getTime() + (this.plan.duration * 30 * 24 * 60 * 60 * 1000));
  }
  next();
});

const SubscriptionPlan = mongoose.model('SubscriptionPlan', subscriptionPlanSchema);
const UserSubscription = mongoose.model('UserSubscription', userSubscriptionSchema);

module.exports = { SubscriptionPlan, UserSubscription };
