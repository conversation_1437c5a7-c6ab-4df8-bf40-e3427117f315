import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Avatar,
  Chip,
  Di<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>
} from '@heroui/react';
import { Icon } from '@iconify/react';
import { useAuth } from '../contexts/auth-context';
import { interestAPI } from '../services/api';

interface Interest {
  id: string;
  fromUserId: string;
  toUserId: string;
  type: 'like' | 'super_like';
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  message?: string;
  responseMessage?: string;
  createdAt: string;
  respondedAt?: string;
  expiresAt: string;
  user: {
    id: string;
    name: string;
    age: number;
    location: string;
    occupation: string;
    education: string;
    profilePicture: string;
    membershipType: string;
    verificationStatus: {
      photo: boolean;
      phone: boolean;
      email: boolean;
    };
  };
}

export const InterestsPage: React.FC = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = React.useState('received');
  const [interests, setInterests] = React.useState<{
    sent: Interest[];
    received: Interest[];
  }>({
    sent: [],
    received: []
  });
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);
  const [actionLoading, setActionLoading] = React.useState<string | null>(null);

  // Load interests
  React.useEffect(() => {
    const loadInterests = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const response = await interestAPI.getInterests();
        
        if (response.success) {
          setInterests(response.data);
        } else {
          setError(response.message || 'Failed to load interests');
        }
      } catch (err: any) {
        console.error('Interests loading error:', err);
        setError(err.response?.data?.message || 'Failed to load interests');
      } finally {
        setLoading(false);
      }
    };
    
    loadInterests();
  }, [user]);

  // Accept interest
  const handleAcceptInterest = async (interestId: string) => {
    try {
      setActionLoading(interestId);
      
      const response = await interestAPI.respondToInterest(interestId, 'accepted', 'Thank you for your interest! I would love to connect.');
      
      if (response.success) {
        // Update the interest status
        setInterests(prev => ({
          ...prev,
          received: prev.received.map(interest =>
            interest.id === interestId
              ? { ...interest, status: 'accepted', respondedAt: new Date().toISOString() }
              : interest
          )
        }));
      }
    } catch (error: any) {
      console.error('Accept interest error:', error);
    } finally {
      setActionLoading(null);
    }
  };

  // Decline interest
  const handleDeclineInterest = async (interestId: string) => {
    try {
      setActionLoading(interestId);
      
      const response = await interestAPI.respondToInterest(interestId, 'declined', 'Thank you for your interest, but I don\'t think we are a good match.');
      
      if (response.success) {
        // Update the interest status
        setInterests(prev => ({
          ...prev,
          received: prev.received.map(interest =>
            interest.id === interestId
              ? { ...interest, status: 'declined', respondedAt: new Date().toISOString() }
              : interest
          )
        }));
      }
    } catch (error: any) {
      console.error('Decline interest error:', error);
    } finally {
      setActionLoading(null);
    }
  };

  // Withdraw sent interest
  const handleWithdrawInterest = async (interestId: string) => {
    try {
      setActionLoading(interestId);
      
      const response = await interestAPI.withdrawInterest(interestId);
      
      if (response.success) {
        // Remove the interest from sent list
        setInterests(prev => ({
          ...prev,
          sent: prev.sent.filter(interest => interest.id !== interestId)
        }));
      }
    } catch (error: any) {
      console.error('Withdraw interest error:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted': return 'success';
      case 'declined': return 'danger';
      case 'expired': return 'warning';
      default: return 'primary';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'accepted': return 'Accepted';
      case 'declined': return 'Declined';
      case 'expired': return 'Expired';
      default: return 'Pending';
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    
    return date.toLocaleDateString();
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Icon icon="lucide:user-x" size={48} className="mx-auto mb-4 text-default-400" />
          <h2 className="text-xl font-semibold mb-2">Please log in</h2>
          <p className="text-default-500">You need to log in to view your interests.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <Spinner size="lg" color="primary" />
          <p className="mt-4 text-default-500">Loading interests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Interests</h1>
        <p className="text-default-500">
          Manage your sent and received interests
        </p>
      </div>

      {error && (
        <Card className="mb-6 border-danger">
          <CardBody>
            <div className="flex items-center gap-2 text-danger">
              <Icon icon="lucide:alert-circle" />
              <span>{error}</span>
            </div>
          </CardBody>
        </Card>
      )}

      <Tabs 
        selectedKey={activeTab} 
        onSelectionChange={(key) => setActiveTab(key as string)}
        className="mb-6"
      >
        <Tab 
          key="received" 
          title={
            <div className="flex items-center gap-2">
              <Icon icon="lucide:heart" size={16} />
              <span>Received</span>
              {interests.received.length > 0 && (
                <Badge content={interests.received.filter(i => i.status === 'pending').length} color="primary" size="sm" />
              )}
            </div>
          }
        >
          <div className="space-y-4">
            {interests.received.length > 0 ? (
              interests.received.map((interest) => (
                <Card key={interest.id}>
                  <CardBody>
                    <div className="flex gap-4">
                      <div className="relative">
                        <Avatar
                          src={interest.user.profilePicture}
                          size="lg"
                          isBordered={interest.user.membershipType !== 'free'}
                          color={interest.user.membershipType === 'platinum' ? 'warning' : 'primary'}
                        />
                        {interest.user.verificationStatus.photo && (
                          <Badge
                            content={<Icon icon="lucide:check" className="text-white" size={12} />}
                            color="success"
                            placement="bottom-right"
                            size="sm"
                          />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-semibold text-lg">{interest.user.name}</h3>
                            <p className="text-sm text-default-500">
                              {interest.user.age} yrs, {interest.user.location}
                            </p>
                            <p className="text-sm text-default-500">
                              {interest.user.occupation} • {interest.user.education}
                            </p>
                          </div>
                          <div className="text-right">
                            <Chip 
                              color={getStatusColor(interest.status)} 
                              size="sm" 
                              variant="flat"
                            >
                              {getStatusText(interest.status)}
                            </Chip>
                            <p className="text-xs text-default-400 mt-1">
                              {formatTimeAgo(interest.createdAt)}
                            </p>
                          </div>
                        </div>
                        
                        {interest.message && (
                          <div className="bg-default-50 p-3 rounded-lg mb-3">
                            <p className="text-sm">{interest.message}</p>
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Link to={`/profile/${interest.user.id}`}>
                            <Button variant="flat" size="sm" startContent={<Icon icon="lucide:user" />}>
                              View Profile
                            </Button>
                          </Link>
                          
                          {interest.status === 'pending' && (
                            <>
                              <Button
                                color="success"
                                size="sm"
                                startContent={<Icon icon="lucide:check" />}
                                onPress={() => handleAcceptInterest(interest.id)}
                                isLoading={actionLoading === interest.id}
                              >
                                Accept
                              </Button>
                              <Button
                                color="danger"
                                variant="flat"
                                size="sm"
                                startContent={<Icon icon="lucide:x" />}
                                onPress={() => handleDeclineInterest(interest.id)}
                                isLoading={actionLoading === interest.id}
                              >
                                Decline
                              </Button>
                            </>
                          )}
                          
                          {interest.status === 'accepted' && (
                            <Button
                              color="primary"
                              size="sm"
                              startContent={<Icon icon="lucide:message-circle" />}
                            >
                              Start Chat
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))
            ) : (
              <Card>
                <CardBody className="text-center py-12">
                  <Icon icon="lucide:heart" size={48} className="mx-auto mb-4 text-default-400" />
                  <h3 className="text-lg font-semibold mb-2">No interests received yet</h3>
                  <p className="text-default-500 mb-4">
                    Complete your profile and be active to receive more interests
                  </p>
                  <Link to="/search">
                    <Button color="primary">Browse Profiles</Button>
                  </Link>
                </CardBody>
              </Card>
            )}
          </div>
        </Tab>

        <Tab 
          key="sent" 
          title={
            <div className="flex items-center gap-2">
              <Icon icon="lucide:send" size={16} />
              <span>Sent</span>
              {interests.sent.length > 0 && (
                <Badge content={interests.sent.length} color="default" size="sm" />
              )}
            </div>
          }
        >
          <div className="space-y-4">
            {interests.sent.length > 0 ? (
              interests.sent.map((interest) => (
                <Card key={interest.id}>
                  <CardBody>
                    <div className="flex gap-4">
                      <div className="relative">
                        <Avatar
                          src={interest.user.profilePicture}
                          size="lg"
                          isBordered={interest.user.membershipType !== 'free'}
                          color={interest.user.membershipType === 'platinum' ? 'warning' : 'primary'}
                        />
                        {interest.user.verificationStatus.photo && (
                          <Badge
                            content={<Icon icon="lucide:check" className="text-white" size={12} />}
                            color="success"
                            placement="bottom-right"
                            size="sm"
                          />
                        )}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="font-semibold text-lg">{interest.user.name}</h3>
                            <p className="text-sm text-default-500">
                              {interest.user.age} yrs, {interest.user.location}
                            </p>
                            <p className="text-sm text-default-500">
                              {interest.user.occupation} • {interest.user.education}
                            </p>
                          </div>
                          <div className="text-right">
                            <Chip 
                              color={getStatusColor(interest.status)} 
                              size="sm" 
                              variant="flat"
                            >
                              {getStatusText(interest.status)}
                            </Chip>
                            <p className="text-xs text-default-400 mt-1">
                              {formatTimeAgo(interest.createdAt)}
                            </p>
                          </div>
                        </div>
                        
                        {interest.responseMessage && interest.status !== 'pending' && (
                          <div className="bg-default-50 p-3 rounded-lg mb-3">
                            <p className="text-sm font-medium mb-1">Response:</p>
                            <p className="text-sm">{interest.responseMessage}</p>
                          </div>
                        )}
                        
                        <div className="flex gap-2">
                          <Link to={`/profile/${interest.user.id}`}>
                            <Button variant="flat" size="sm" startContent={<Icon icon="lucide:user" />}>
                              View Profile
                            </Button>
                          </Link>
                          
                          {interest.status === 'pending' && (
                            <Button
                              color="danger"
                              variant="flat"
                              size="sm"
                              startContent={<Icon icon="lucide:x" />}
                              onPress={() => handleWithdrawInterest(interest.id)}
                              isLoading={actionLoading === interest.id}
                            >
                              Withdraw
                            </Button>
                          )}
                          
                          {interest.status === 'accepted' && (
                            <Button
                              color="primary"
                              size="sm"
                              startContent={<Icon icon="lucide:message-circle" />}
                            >
                              Start Chat
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))
            ) : (
              <Card>
                <CardBody className="text-center py-12">
                  <Icon icon="lucide:send" size={48} className="mx-auto mb-4 text-default-400" />
                  <h3 className="text-lg font-semibold mb-2">No interests sent yet</h3>
                  <p className="text-default-500 mb-4">
                    Start browsing profiles and send interests to connect with potential matches
                  </p>
                  <Link to="/search">
                    <Button color="primary">Browse Profiles</Button>
                  </Link>
                </CardBody>
              </Card>
            )}
          </div>
        </Tab>
      </Tabs>
    </div>
  );
};
