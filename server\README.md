# Matrimony Platform - Backend Server

A comprehensive Node.js backend server for an Indian matrimony platform with all the features needed for a modern matrimonial service.

## Features

### Core Features
- **User Management**: Registration, authentication, profile management
- **Profile System**: Detailed profiles with personal, family, education, career, lifestyle, and religious information
- **Search & Matching**: Advanced search with filters, compatibility matching
- **Interest Management**: Send/receive interests, accept/decline functionality
- **Messaging System**: Real-time messaging with Socket.IO
- **Subscription Management**: Multiple subscription plans with Razorpay integration
- **File Upload**: Profile pictures, documents, horoscope uploads
- **Horoscope Matching**: Astrological compatibility checking
- **Admin Dashboard**: Complete admin panel for user and content management

### Indian-Specific Features
- **Caste & Community**: Detailed caste and sub-caste options
- **Horoscope Integration**: Kundli matching, Manglik compatibility
- **Regional Support**: State/city-wise search, multiple languages
- **Family Information**: Detailed family background, values, status
- **Religious Preferences**: Religion-specific matching criteria

### Technical Features
- **JWT Authentication**: Secure token-based authentication
- **Real-time Communication**: Socket.IO for instant messaging
- **File Upload**: Multer for handling image and document uploads
- **Email & SMS**: Automated notifications via Nodemailer and Twilio
- **Payment Integration**: Razorpay for subscription payments
- **Rate Limiting**: API rate limiting for security
- **Data Validation**: Comprehensive input validation
- **Error Handling**: Centralized error handling middleware

## Tech Stack

- **Runtime**: Node.js
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: JWT (JSON Web Tokens)
- **Real-time**: Socket.IO
- **File Upload**: Multer
- **Email**: Nodemailer
- **SMS**: Twilio
- **Payment**: Razorpay
- **Validation**: Express-validator
- **Security**: Helmet, CORS, Rate limiting

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd matrimony-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup**
   ```bash
   cp server/.env.example server/.env
   ```
   
   Update the `.env` file with your configuration:
   ```env
   # Server Configuration
   PORT=5000
   NODE_ENV=development
   
   # Database
   MONGODB_URI=mongodb://localhost:27017/matrimony-db
   
   # JWT Secret
   JWT_SECRET=your-super-secret-jwt-key-here
   JWT_EXPIRES_IN=7d
   
   # Email Configuration (Nodemailer)
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASS=your-app-password
   
   # SMS Configuration (Twilio)
   TWILIO_ACCOUNT_SID=your-twilio-account-sid
   TWILIO_AUTH_TOKEN=your-twilio-auth-token
   TWILIO_PHONE_NUMBER=+**********
   
   # Payment Gateway (Razorpay)
   RAZORPAY_KEY_ID=your-razorpay-key-id
   RAZORPAY_KEY_SECRET=your-razorpay-key-secret
   
   # Client URL
   CLIENT_URL=http://localhost:3000
   ```

4. **Database Setup**
   - Install MongoDB locally or use MongoDB Atlas
   - Run the seed script to populate initial data:
   ```bash
   npm run server:seed
   ```

5. **Start the server**
   ```bash
   # Development mode with auto-reload
   npm run server:dev
   
   # Production mode
   npm run server
   ```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/send-phone-otp` - Send phone verification OTP
- `POST /api/auth/verify-phone-otp` - Verify phone OTP
- `GET /api/auth/verify-email/:token` - Verify email address
- `GET /api/auth/me` - Get current user info
- `POST /api/auth/logout` - Logout user

### Profile Management
- `PUT /api/profiles/personal-info` - Update personal information
- `PUT /api/profiles/family-info` - Update family information
- `PUT /api/profiles/education-career` - Update education and career
- `PUT /api/profiles/lifestyle` - Update lifestyle preferences
- `PUT /api/profiles/religious-info` - Update religious information
- `PUT /api/profiles/partner-preferences` - Update partner preferences
- `GET /api/profiles/:userId` - Get user profile by ID
- `GET /api/profiles/completion/status` - Get profile completion status

### Search & Discovery
- `GET /api/search` - Basic search with filters
- `GET /api/search/advanced` - Advanced search (premium)
- `GET /api/search/suggestions` - Get match suggestions
- `GET /api/search/recently-viewed` - Recently viewed profiles

### Interest Management
- `POST /api/interests/send` - Send interest to user
- `GET /api/interests/received` - Get received interests
- `GET /api/interests/sent` - Get sent interests
- `PUT /api/interests/:interestId/respond` - Respond to interest
- `DELETE /api/interests/:interestId` - Withdraw interest
- `GET /api/interests/stats` - Get interest statistics
- `GET /api/interests/mutual` - Get mutual interests

### Messaging
- `GET /api/messages/conversations` - Get user conversations
- `GET /api/messages/conversations/:conversationId/messages` - Get messages
- `POST /api/messages/send` - Send message
- `PUT /api/messages/messages/:messageId/read` - Mark message as read
- `DELETE /api/messages/messages/:messageId` - Delete message
- `GET /api/messages/unread-count` - Get unread message count

### Subscriptions
- `GET /api/subscriptions/plans` - Get subscription plans
- `GET /api/subscriptions/current` - Get current subscription
- `POST /api/subscriptions/create-order` - Create payment order
- `POST /api/subscriptions/verify-payment` - Verify payment
- `GET /api/subscriptions/history` - Get subscription history
- `PUT /api/subscriptions/cancel` - Cancel subscription
- `GET /api/subscriptions/usage` - Get usage statistics

### File Upload
- `POST /api/upload/profile-picture` - Upload profile picture
- `POST /api/upload/profile-photos` - Upload multiple photos
- `POST /api/upload/documents` - Upload verification documents
- `POST /api/upload/horoscope` - Upload horoscope
- `DELETE /api/upload/file/:filename` - Delete uploaded file

### User Management
- `PUT /api/users/profile-picture` - Update profile picture
- `POST /api/users/profile-photos` - Add profile photos
- `POST /api/users/block/:userId` - Block user
- `DELETE /api/users/block/:userId` - Unblock user
- `POST /api/users/shortlist/:userId` - Shortlist profile
- `GET /api/users/shortlist` - Get shortlisted profiles
- `PUT /api/users/settings` - Update account settings

### Horoscope
- `PUT /api/horoscope/update` - Update horoscope information
- `POST /api/horoscope/compatibility/:userId` - Check compatibility
- `GET /api/horoscope/suggestions` - Get horoscope-based suggestions

### Admin (Admin access required)
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/users` - Get all users with filters
- `PUT /api/admin/users/:userId/block` - Block/unblock user
- `PUT /api/admin/users/:userId/verify` - Verify user profile
- `GET /api/admin/subscription-plans` - Get subscription plans
- `POST /api/admin/subscription-plans` - Create subscription plan
- `GET /api/admin/subscriptions` - Get user subscriptions
- `GET /api/admin/reports` - Get reports and analytics

## Database Models

### User Model
- Personal information (age, height, location, etc.)
- Family information (type, values, background)
- Education and career details
- Lifestyle preferences
- Religious information
- Partner preferences
- Verification status
- Subscription details

### Interest Model
- Sender and receiver references
- Interest type and status
- Messages and responses
- Expiration handling

### Message/Conversation Models
- Real-time messaging support
- Read receipts and delivery status
- File attachments
- Conversation management

### Subscription Models
- Multiple subscription plans
- Usage tracking and limits
- Payment integration
- Feature access control

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: Bcrypt for password security
- **Rate Limiting**: API rate limiting to prevent abuse
- **Input Validation**: Comprehensive validation using express-validator
- **CORS Protection**: Cross-origin request security
- **Helmet**: Security headers middleware
- **File Upload Security**: File type and size validation

## Real-time Features

- **Instant Messaging**: Real-time chat with Socket.IO
- **Online Status**: User online/offline status
- **Typing Indicators**: Real-time typing notifications
- **Message Delivery**: Real-time message delivery status

## Deployment

1. **Environment Variables**: Set all required environment variables
2. **Database**: Ensure MongoDB is accessible
3. **File Storage**: Configure file upload directory
4. **Process Manager**: Use PM2 for production deployment
5. **Reverse Proxy**: Configure Nginx for production

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
