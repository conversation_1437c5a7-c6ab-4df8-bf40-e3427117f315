{"name": "indian-matrimony", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "tsc --noCheck && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node server/src/index.js", "server:dev": "nodemon server/src/index.js", "server:seed": "node server/src/scripts/seedData.js", "server:build": "tsc -p server/tsconfig.json"}, "dependencies": {"@heroui/react": "2.7.9", "@heroui/use-theme": "2.1.6", "@iconify/react": "latest", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.13", "@types/node": "^22.15.30", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "framer-motion": "^11.18.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^8.15.1", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^7.0.3", "nodemon": "^3.1.10", "razorpay": "^2.9.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "5.3.4", "socket.io": "^4.8.1", "source-map-js": "^1.2.1", "twilio": "^5.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/generator": "^7.27.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.20", "postcss": "8.4.49", "tailwindcss": "3.4.17", "typescript": "5.7.3", "vite": "^6.0.11"}}