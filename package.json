{"name": "indian-matrimony", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc --noCheck && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroui/react": "2.7.9", "@heroui/use-theme": "2.1.6", "@iconify/react": "latest", "framer-motion": "^11.18.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "5.3.4", "source-map-js": "^1.2.1"}, "devDependencies": {"@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "10.4.20", "tailwindcss": "3.4.17", "postcss": "8.4.49", "typescript": "5.7.3", "vite": "^6.0.11", "@babel/core": "^7.26.10", "@babel/generator": "^7.27.0", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.27.0", "@babel/traverse": "^7.27.0", "@babel/types": "^7.27.0"}}