const express = require('express');
const mongoose = require('mongoose');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
require('dotenv').config();

const app = express();

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000,
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(morgan('combined'));
app.use(limiter);
app.use(cors({
  origin: process.env.CLIENT_URL || "http://localhost:3000",
  credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Serve static files
app.use('/uploads', express.static('uploads'));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Matrimony API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// Load routes safely
const loadRoutes = () => {
  try {
    // Import error handler
    const errorHandler = require('./middleware/errorHandler');
    
    // Import routes one by one with error handling
    console.log('Loading routes...');
    
    // Auth routes
    try {
      const authRoutes = require('./routes/auth');
      app.use('/api/auth', authRoutes);
      console.log('✓ Auth routes loaded');
    } catch (error) {
      console.error('✗ Auth routes failed:', error.message);
    }
    
    // User routes
    try {
      const userRoutes = require('./routes/users');
      app.use('/api/users', userRoutes);
      console.log('✓ User routes loaded');
    } catch (error) {
      console.error('✗ User routes failed:', error.message);
    }
    
    // Profile routes
    try {
      const profileRoutes = require('./routes/profiles');
      app.use('/api/profiles', profileRoutes);
      console.log('✓ Profile routes loaded');
    } catch (error) {
      console.error('✗ Profile routes failed:', error.message);
    }
    
    // Search routes
    try {
      const searchRoutes = require('./routes/search');
      app.use('/api/search', searchRoutes);
      console.log('✓ Search routes loaded');
    } catch (error) {
      console.error('✗ Search routes failed:', error.message);
    }
    
    // Interest routes
    try {
      const interestRoutes = require('./routes/interests');
      app.use('/api/interests', interestRoutes);
      console.log('✓ Interest routes loaded');
    } catch (error) {
      console.error('✗ Interest routes failed:', error.message);
    }
    
    // Message routes
    try {
      const messageRoutes = require('./routes/messages');
      app.use('/api/messages', messageRoutes);
      console.log('✓ Message routes loaded');
    } catch (error) {
      console.error('✗ Message routes failed:', error.message);
    }
    
    // Subscription routes
    try {
      const subscriptionRoutes = require('./routes/subscriptions');
      app.use('/api/subscriptions', subscriptionRoutes);
      console.log('✓ Subscription routes loaded');
    } catch (error) {
      console.error('✗ Subscription routes failed:', error.message);
    }
    
    // Upload routes
    try {
      const uploadRoutes = require('./routes/upload');
      app.use('/api/upload', uploadRoutes);
      console.log('✓ Upload routes loaded');
    } catch (error) {
      console.error('✗ Upload routes failed:', error.message);
    }
    
    // Horoscope routes
    try {
      const horoscopeRoutes = require('./routes/horoscope');
      app.use('/api/horoscope', horoscopeRoutes);
      console.log('✓ Horoscope routes loaded');
    } catch (error) {
      console.error('✗ Horoscope routes failed:', error.message);
    }
    
    // Admin routes
    try {
      const adminRoutes = require('./routes/admin');
      app.use('/api/admin', adminRoutes);
      console.log('✓ Admin routes loaded');
    } catch (error) {
      console.error('✗ Admin routes failed:', error.message);
    }
    
    // Error handling middleware
    app.use(errorHandler);
    
    // 404 handler
    app.use('*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'Route not found'
      });
    });
    
    console.log('All available routes loaded successfully');
    
  } catch (error) {
    console.error('Error loading routes:', error);
  }
};

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/matrimony-db');
    console.log('✓ Connected to MongoDB');
  } catch (error) {
    console.error('✗ MongoDB connection error:', error.message);
    console.log('Note: Make sure MongoDB is running or update MONGODB_URI in .env file');
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
  }
};

// Initialize server
const initializeServer = async () => {
  // Connect to database
  await connectDB();
  
  // Load routes
  loadRoutes();
  
  // Start server
  const PORT = process.env.PORT || 5000;
  app.listen(PORT, () => {
    console.log(`🚀 Matrimony server is running on port ${PORT}`);
    console.log(`📱 Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
  });
};

// Start the server
initializeServer().catch(error => {
  console.error('Failed to initialize server:', error);
  process.exit(1);
});

module.exports = app;
